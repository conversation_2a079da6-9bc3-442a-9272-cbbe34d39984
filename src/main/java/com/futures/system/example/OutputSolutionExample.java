package com.futures.system.example;

import com.futures.system.model.*;
import com.futures.system.processor.*;
import com.futures.system.sink.*;
import com.futures.system.source.*;
import com.futures.system.mock.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 输出方案使用示例
 * 演示如何集成调试输出和ClickHouse生产输出
 */
public class OutputSolutionExample {
    
    private static final Logger LOG = LoggerFactory.getLogger(OutputSolutionExample.class);
    
    public static void main(String[] args) throws Exception {
        // 示例1：调试输出方案
        runDebugOutputExample();
        
        // 示例2：生产输出方案
        // runProductionOutputExample();
        
        // 示例3：混合输出方案
        // runHybridOutputExample();
    }
    
    /**
     * 示例1：调试输出方案
     * 适用于开发和调试阶段
     */
    public static void runDebugOutputExample() throws Exception {
        LOG.info("=== 运行调试输出方案示例 ===");
        
        // 1. 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        // 2. 创建数据源（模拟数据）
        DataStream<SingleLegOrder> singleLegOrderStream = createMockSingleLegOrderStream(env);
        DataStream<CombinationOrder> combinationOrderStream = createMockCombinationOrderStream(env);
        DataStream<TradeDetail> tradeDetailStream = createMockTradeDetailStream(env);
        
        // 3. 构建处理流水线
        // 基础层订单簿
        SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(singleLegOrderStream, tradeDetailStream);
        
        // 虚拟层订单簿
        SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream = singleLegOrderStream
                .keyBy(SingleLegOrder::getContractCode)
                .connect(tradeDetailStream.keyBy(TradeDetail::getContractCode))
                .process(new OrderBookEventMerger());
        
        SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream = 
                VirtualOrderBookManager.createVirtualOrderBookPipeline(baseOrderBookStream, combinationOrderStream);
        
        // 盈亏计算
        PnLCalculationManager.PnLCalculationStreams pnlStreams = 
                PnLCalculationManager.createPnLCalculationPipeline(tradeDetailStream, baseSnapshotStream);
        
        // 4. 创建调试输出配置
        OutputSolutionManager.OutputSolutionConfig outputConfig = 
                OutputSolutionManager.createDebugSolution();
        
        // 5. 应用输出方案
        OutputSolutionManager.applyOutputSolution(
                virtualSnapshotStream,  // 使用虚拟层快照
                pnlStreams.getMergedPnLStream(),
                pnlStreams.getPnlSummaryStream(),
                outputConfig
        );
        
        // 6. 打印配置信息
        OutputSolutionManager.printConfigInfo(outputConfig);
        
        // 7. 执行作业
        LOG.info("启动调试输出方案...");
        env.execute("Futures System - Debug Output Solution");
    }
    
    /**
     * 示例2：生产输出方案
     * 适用于生产环境
     */
    public static void runProductionOutputExample() throws Exception {
        LOG.info("=== 运行生产输出方案示例 ===");
        
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(4); // 生产环境使用更高并行度
        
        // 1. 创建真实数据源
        DataSourceFactory.DataSources dataSources = DataSourceFactory.createKafkaDataSources(env);
        
        // 2. 数据预处理
        DataPreprocessor.PreprocessedDataStreams preprocessedStreams = 
                DataPreprocessor.preprocessAllStreams(
                        dataSources.getSingleLegOrderSource(),
                        dataSources.getCombinationOrderSource(),
                        dataSources.getTradeDetailSource()
                );
        
        // 3. 构建完整处理流水线
        VirtualOrderBookManager.CompleteOrderBookStreams orderBookStreams = 
                VirtualOrderBookManager.createCompleteOrderBookPipeline(
                        preprocessedStreams.getSingleLegOrderStream(),
                        preprocessedStreams.getCombinationOrderStream(),
                        preprocessedStreams.getTradeDetailStream()
                );
        
        PnLCalculationManager.PnLCalculationStreams pnlStreams = 
                PnLCalculationManager.createPnLCalculationPipeline(
                        preprocessedStreams.getTradeDetailStream(),
                        orderBookStreams.getVirtualSnapshotStream()
                );
        
        // 4. 创建生产输出配置
        OutputSolutionManager.OutputSolutionConfig outputConfig = 
                OutputSolutionManager.createProductionSolution(
                        "********************************************************",
                        "futures_user",
                        "futures_password"
                );
        
        // 5. 验证配置
        if (!OutputSolutionManager.validateOutputSolution(outputConfig)) {
            throw new RuntimeException("Output solution validation failed");
        }
        
        // 6. 应用输出方案
        OutputSolutionManager.applyOutputSolution(
                orderBookStreams.getVirtualSnapshotStream(),
                pnlStreams.getMergedPnLStream(),
                pnlStreams.getPnlSummaryStream(),
                outputConfig
        );
        
        LOG.info("启动生产输出方案...");
        env.execute("Futures System - Production Output Solution");
    }
    
    /**
     * 示例3：混合输出方案
     * 同时支持调试和生产输出
     */
    public static void runHybridOutputExample() throws Exception {
        LOG.info("=== 运行混合输出方案示例 ===");
        
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2);
        
        // 1. 从环境变量创建配置
        OutputSolutionManager.OutputSolutionConfig outputConfig = 
                OutputSolutionManager.createConfigFromEnvironment();
        
        // 2. 如果环境变量未配置，使用默认混合配置
        if (outputConfig.getSolutionType() == OutputSolutionManager.OutputSolutionType.DEBUG_CONSOLE) {
            outputConfig = OutputSolutionManager.createHybridSolution(
                    "****************************************",
                    "default",
                    ""
            );
        }
        
        // 3. 构建数据流（简化版本）
        DataStream<SingleLegOrder> singleLegOrderStream = createMockSingleLegOrderStream(env);
        DataStream<TradeDetail> tradeDetailStream = createMockTradeDetailStream(env);
        
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(singleLegOrderStream, tradeDetailStream);
        
        PnLCalculationManager.SimplePnLStreams pnlStreams = 
                PnLCalculationManager.createSimplePnLCalculationPipeline(tradeDetailStream);
        
        // 4. 应用混合输出方案
        OutputSolutionManager.applyOutputSolution(
                snapshotStream,
                pnlStreams.getPnlCalculationStream(),
                pnlStreams.getPnlSummaryStream(),
                outputConfig
        );
        
        OutputSolutionManager.printConfigInfo(outputConfig);
        
        LOG.info("启动混合输出方案...");
        env.execute("Futures System - Hybrid Output Solution");
    }
    
    // 模拟数据源方法（使用MockDataGenerator）
    private static DataStream<SingleLegOrder> createMockSingleLegOrderStream(StreamExecutionEnvironment env) {
        // 生成模拟数据
        MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
        MockDataSourceFactory.MockDataSources dataSources =
                MockDataSourceFactory.createFastTestSources(env, dataSet);
        return dataSources.getSingleLegOrderStream();
    }

    private static DataStream<CombinationOrder> createMockCombinationOrderStream(StreamExecutionEnvironment env) {
        MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
        MockDataSourceFactory.MockDataSources dataSources =
                MockDataSourceFactory.createFastTestSources(env, dataSet);
        return dataSources.getCombinationOrderStream();
    }

    private static DataStream<TradeDetail> createMockTradeDetailStream(StreamExecutionEnvironment env) {
        MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
        MockDataSourceFactory.MockDataSources dataSources =
                MockDataSourceFactory.createFastTestSources(env, dataSet);
        return dataSources.getTradeDetailStream();
    }
}
