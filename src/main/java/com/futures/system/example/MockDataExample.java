package com.futures.system.example;

import com.futures.system.mock.*;
import com.futures.system.model.*;
import com.futures.system.processor.*;
import com.futures.system.sink.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 模拟数据测试示例
 * 演示如何使用模拟数据生成器进行完整的系统功能验证
 */
public class MockDataExample {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataExample.class);
    
    public static void main(String[] args) throws Exception {
        // 示例1：基本模拟数据测试
        runBasicMockDataTest();
        
        // 示例2：完整系统集成测试
        // runCompleteSystemTest();
        
        // 示例3：性能压力测试
        // runPerformanceTest();
    }
    
    /**
     * 示例1：基本模拟数据测试
     */
    public static void runBasicMockDataTest() throws Exception {
        LOG.info("=== 开始基本模拟数据测试 ===");
        
        // 1. 生成模拟数据
        LOG.info("生成模拟数据...");
        MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
        dataSet.printStatistics();
        
        // 2. 验证数据完整性
        if (!dataSet.validateDataIntegrity()) {
            LOG.error("数据完整性验证失败，停止测试");
            return;
        }
        
        // 3. 保存数据到文件
        String dataFilePath = "target/mock_data_test.json";
        dataSet.saveToJsonFile(dataFilePath);
        LOG.info("模拟数据已保存到: {}", dataFilePath);
        
        // 4. 创建Flink环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        // 5. 创建模拟数据源
        MockDataSourceFactory.MockDataSources dataSources = 
                MockDataSourceFactory.createFastTestSources(env, dataSet);
        
        // 6. 应用调试输出
        DebugOutputManager.DebugConfig debugConfig = DebugOutputManager.createDevelopmentConfig();
        
        // 简单的数据流处理验证
        dataSources.getSingleLegOrderStream()
                .addSink(new ConsoleDebugSink.OrderBookSnapshotConsoleSink() {
                    @Override
                    public void invoke(OrderBookSnapshot snapshot, Context context) throws Exception {
                        LOG.info("接收到单腿委托: {}", snapshot);
                    }
                })
                .name("SingleLegOrderDebugSink");
        
        dataSources.getTradeDetailStream()
                .addSink(new ConsoleDebugSink.PnLCalculationConsoleSink() {
                    @Override
                    public void invoke(PnLCalculation calculation, Context context) throws Exception {
                        LOG.info("接收到成交明细: {}", calculation);
                    }
                })
                .name("TradeDetailDebugSink");
        
        // 7. 执行测试
        LOG.info("启动基本模拟数据测试...");
        env.execute("Basic Mock Data Test");
        
        LOG.info("=== 基本模拟数据测试完成 ===");
    }
    
    /**
     * 示例2：完整系统集成测试
     */
    public static void runCompleteSystemTest() throws Exception {
        LOG.info("=== 开始完整系统集成测试 ===");
        
        // 1. 生成大量模拟数据
        MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
        dataSet.printStatistics();
        
        // 2. 创建Flink环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2);
        
        // 3. 创建实时模拟数据源
        MockDataSourceFactory.MockDataSources dataSources = 
                MockDataSourceFactory.createRealTimeSimulationSources(env, dataSet);
        
        // 4. 构建完整处理流水线
        
        // 基础层订单簿处理
        SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(
                        dataSources.getSingleLegOrderStream(), 
                        dataSources.getTradeDetailStream());
        
        // 虚拟层订单簿处理
        SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream = 
                dataSources.getSingleLegOrderStream()
                        .keyBy(SingleLegOrder::getContractCode)
                        .connect(dataSources.getTradeDetailStream().keyBy(TradeDetail::getContractCode))
                        .process(new OrderBookEventMerger())
                        .name("BaseOrderBookStream");
        
        SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream = 
                VirtualOrderBookManager.createVirtualOrderBookPipeline(
                        baseOrderBookStream, 
                        dataSources.getCombinationOrderStream());
        
        // 盈亏计算处理
        PnLCalculationManager.PnLCalculationStreams pnlStreams = 
                PnLCalculationManager.createPnLCalculationPipeline(
                        dataSources.getTradeDetailStream(), 
                        virtualSnapshotStream);
        
        // 5. 应用输出方案
        OutputSolutionManager.OutputSolutionConfig outputConfig = 
                OutputSolutionManager.createDebugSolution();
        
        OutputSolutionManager.applyOutputSolution(
                virtualSnapshotStream,
                pnlStreams.getMergedPnLStream(),
                pnlStreams.getPnlSummaryStream(),
                outputConfig
        );
        
        // 6. 执行完整系统测试
        LOG.info("启动完整系统集成测试...");
        env.execute("Complete System Integration Test");
        
        LOG.info("=== 完整系统集成测试完成 ===");
    }
    
    /**
     * 示例3：性能压力测试
     */
    public static void runPerformanceTest() throws Exception {
        LOG.info("=== 开始性能压力测试 ===");
        
        // 1. 生成大量数据
        MockDataSet dataSet = new MockDataSet();
        
        // 生成更多数据用于压力测试
        dataSet.setSingleLegOrders(MockDataGenerator.generateSingleLegOrders(500));
        dataSet.setTradeDetails(MockDataGenerator.generateTradeDetails(dataSet.getSingleLegOrders()));
        dataSet.setCombinationOrders(MockDataGenerator.generateCombinationOrders(100));
        
        dataSet.printStatistics();
        
        // 2. 创建高性能Flink环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(4);
        
        // 3. 创建快速数据源
        MockDataSourceFactory.MockSourceConfig config = new MockDataSourceFactory.MockSourceConfig();
        config.setRealTimePlayback(false);
        config.setDelayBetweenRecords(1); // 1ms延迟
        config.setLoopPlayback(false);
        
        MockDataSourceFactory.MockDataSources dataSources = 
                MockDataSourceFactory.createMockDataSources(env, dataSet, config);
        
        // 4. 构建高性能处理流水线
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(
                        dataSources.getSingleLegOrderStream(), 
                        dataSources.getTradeDetailStream());
        
        PnLCalculationManager.SimplePnLStreams pnlStreams = 
                PnLCalculationManager.createSimplePnLCalculationPipeline(
                        dataSources.getTradeDetailStream());
        
        // 5. 应用性能监控输出
        OutputSolutionManager.OutputSolutionConfig outputConfig = 
                OutputSolutionManager.createTestConfig();
        
        OutputSolutionManager.applyOutputSolution(
                snapshotStream,
                pnlStreams.getPnlCalculationStream(),
                pnlStreams.getPnlSummaryStream(),
                outputConfig
        );
        
        // 6. 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 7. 执行性能测试
        LOG.info("启动性能压力测试...");
        env.execute("Performance Stress Test");
        
        // 8. 计算执行时间
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        LOG.info("=== 性能压力测试完成 ===");
        LOG.info("执行时间: {} 毫秒", executionTime);
        LOG.info("处理速度: {} 条/秒", 
                (dataSet.getSingleLegOrders().size() + dataSet.getTradeDetails().size()) * 1000.0 / executionTime);
    }
    
    /**
     * 示例4：特定合约测试
     */
    public static void runContractSpecificTest(String contractCode) throws Exception {
        LOG.info("=== 开始合约 {} 专项测试 ===", contractCode);
        
        // 1. 生成特定合约数据
        MockDataSet dataSet = MockDataGenerator.generateDataForContract(contractCode, 50);
        dataSet.printStatistics();
        
        // 2. 创建Flink环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        // 3. 创建数据源
        MockDataSourceFactory.MockDataSources dataSources = 
                MockDataSourceFactory.createFastTestSources(env, dataSet);
        
        // 4. 简单处理流水线
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(
                        dataSources.getSingleLegOrderStream(), 
                        dataSources.getTradeDetailStream());
        
        // 5. 应用调试输出
        DebugOutputManager.DebugConfig debugConfig = DebugOutputManager.createDevelopmentConfig();
        DebugOutputManager.applyOrderBookDebugOutput(snapshotStream, debugConfig);
        
        // 6. 执行测试
        LOG.info("启动合约 {} 专项测试...", contractCode);
        env.execute("Contract Specific Test - " + contractCode);
        
        LOG.info("=== 合约 {} 专项测试完成 ===", contractCode);
    }
}
