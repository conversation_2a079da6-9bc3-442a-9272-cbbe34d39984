package com.futures.system.utils;

import com.futures.system.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 提供各种数据完整性和一致性验证方法
 */
public class DataValidationUtils {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataValidationUtils.class);
    
    // 合约代码格式验证正则表达式
    private static final Pattern CONTRACT_CODE_PATTERN = Pattern.compile("^[A-Z]{1,4}\\d{4}(-[CP]-\\d+)?$");
    
    // 报单编号格式验证正则表达式  
    private static final Pattern ORDER_NUMBER_PATTERN = Pattern.compile("^[0-9A-Z]{6,20}$");
    
    // 会员编码格式验证正则表达式
    private static final Pattern MEMBER_CODE_PATTERN = Pattern.compile("^\\d{4}$");
    
    /**
     * 验证单腿委托订单数据完整性
     */
    public static ValidationResult validateSingleLegOrder(SingleLegOrder order) {
        if (order == null) {
            return ValidationResult.failure("SingleLegOrder is null");
        }
        
        // 验证必填字段
        if (isNullOrEmpty(order.getOrderNumber())) {
            return ValidationResult.failure("Order number is required");
        }
        
        if (isNullOrEmpty(order.getContractCode())) {
            return ValidationResult.failure("Contract code is required");
        }
        
        if (order.getOrderPrice() == null || order.getOrderPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Order price must be positive");
        }
        
        if (order.getOrderVolume() == null || order.getOrderVolume().compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Order volume must be positive");
        }
        
        if (order.getBuySellFlag() == null) {
            return ValidationResult.failure("Buy/sell flag is required");
        }
        
        if (order.getOrderStatus() == null) {
            return ValidationResult.failure("Order status is required");
        }
        
        if (isNullOrEmpty(order.getMemberCode())) {
            return ValidationResult.failure("Member code is required");
        }
        
        // 验证格式
        if (!CONTRACT_CODE_PATTERN.matcher(order.getContractCode()).matches()) {
            return ValidationResult.failure("Invalid contract code format: " + order.getContractCode());
        }
        
        if (!ORDER_NUMBER_PATTERN.matcher(order.getOrderNumber()).matches()) {
            return ValidationResult.failure("Invalid order number format: " + order.getOrderNumber());
        }
        
        if (!MEMBER_CODE_PATTERN.matcher(order.getMemberCode()).matches()) {
            return ValidationResult.failure("Invalid member code format: " + order.getMemberCode());
        }
        
        // 验证数量逻辑
        if (order.getRemainingVolume() != null && 
            order.getRemainingVolume().compareTo(order.getOrderVolume()) > 0) {
            return ValidationResult.failure("Remaining volume cannot exceed order volume");
        }
        
        if (order.getTradedVolume() != null && 
            order.getTradedVolume().compareTo(order.getOrderVolume()) > 0) {
            return ValidationResult.failure("Traded volume cannot exceed order volume");
        }
        
        // 验证时间
        if (order.getOrderTime() == null) {
            return ValidationResult.failure("Order time is required");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 验证组合委托订单数据完整性
     */
    public static ValidationResult validateCombinationOrder(CombinationOrder order) {
        if (order == null) {
            return ValidationResult.failure("CombinationOrder is null");
        }
        
        // 验证基础字段（与单腿订单相同的验证）
        ValidationResult baseResult = validateOrderBaseFields(
            order.getOrderNumber(), order.getOrderPrice(), order.getOrderVolume(),
            order.getBuySellFlag(), order.getOrderStatus(), order.getMemberCode(), order.getOrderTime()
        );
        
        if (!baseResult.isValid()) {
            return baseResult;
        }
        
        // 验证组合订单特有字段
        if (isNullOrEmpty(order.getCombinationContractCode())) {
            return ValidationResult.failure("Combination contract code is required");
        }
        
        if (isNullOrEmpty(order.getLeg1ContractCode())) {
            return ValidationResult.failure("Leg1 contract code is required");
        }
        
        if (isNullOrEmpty(order.getLeg2ContractCode())) {
            return ValidationResult.failure("Leg2 contract code is required");
        }
        
        // 验证腿合约格式
        if (!CONTRACT_CODE_PATTERN.matcher(order.getLeg1ContractCode()).matches()) {
            return ValidationResult.failure("Invalid leg1 contract code format: " + order.getLeg1ContractCode());
        }
        
        if (!CONTRACT_CODE_PATTERN.matcher(order.getLeg2ContractCode()).matches()) {
            return ValidationResult.failure("Invalid leg2 contract code format: " + order.getLeg2ContractCode());
        }
        
        // 验证腿合约不能相同
        if (order.getLeg1ContractCode().equals(order.getLeg2ContractCode())) {
            return ValidationResult.failure("Leg1 and Leg2 contract codes cannot be the same");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 验证成交明细数据完整性
     */
    public static ValidationResult validateTradeDetail(TradeDetail trade) {
        if (trade == null) {
            return ValidationResult.failure("TradeDetail is null");
        }
        
        // 验证必填字段
        if (isNullOrEmpty(trade.getTradeNumber())) {
            return ValidationResult.failure("Trade number is required");
        }
        
        if (isNullOrEmpty(trade.getOrderNumber())) {
            return ValidationResult.failure("Order number is required");
        }
        
        if (isNullOrEmpty(trade.getContractCode())) {
            return ValidationResult.failure("Contract code is required");
        }
        
        if (trade.getTradePrice() == null || trade.getTradePrice().compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Trade price must be positive");
        }
        
        if (trade.getTradeVolume() == null || trade.getTradeVolume().compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Trade volume must be positive");
        }
        
        if (trade.getBuySellFlag() == null) {
            return ValidationResult.failure("Buy/sell flag is required");
        }
        
        if (trade.getTradeType() == null) {
            return ValidationResult.failure("Trade type is required");
        }
        
        if (isNullOrEmpty(trade.getSettlementMemberCode())) {
            return ValidationResult.failure("Settlement member code is required");
        }
        
        // 验证格式
        if (!CONTRACT_CODE_PATTERN.matcher(trade.getContractCode()).matches()) {
            return ValidationResult.failure("Invalid contract code format: " + trade.getContractCode());
        }
        
        if (!ORDER_NUMBER_PATTERN.matcher(trade.getOrderNumber()).matches()) {
            return ValidationResult.failure("Invalid order number format: " + trade.getOrderNumber());
        }
        
        if (!MEMBER_CODE_PATTERN.matcher(trade.getSettlementMemberCode()).matches()) {
            return ValidationResult.failure("Invalid settlement member code format: " + trade.getSettlementMemberCode());
        }
        
        // 验证时间
        if (trade.getTradeTime() == null) {
            return ValidationResult.failure("Trade time is required");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 验证订单基础字段
     */
    private static ValidationResult validateOrderBaseFields(String orderNumber, BigDecimal orderPrice, 
            BigDecimal orderVolume, BuySellFlag buySellFlag, OrderStatus orderStatus, 
            String memberCode, LocalTime orderTime) {
        
        if (isNullOrEmpty(orderNumber)) {
            return ValidationResult.failure("Order number is required");
        }
        
        if (orderPrice == null || orderPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Order price must be positive");
        }
        
        if (orderVolume == null || orderVolume.compareTo(BigDecimal.ZERO) <= 0) {
            return ValidationResult.failure("Order volume must be positive");
        }
        
        if (buySellFlag == null) {
            return ValidationResult.failure("Buy/sell flag is required");
        }
        
        if (orderStatus == null) {
            return ValidationResult.failure("Order status is required");
        }
        
        if (isNullOrEmpty(memberCode)) {
            return ValidationResult.failure("Member code is required");
        }
        
        if (orderTime == null) {
            return ValidationResult.failure("Order time is required");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 检查字符串是否为空
     */
    private static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 验证交易日期是否合理
     */
    public static boolean isValidTradeDate(LocalDate tradeDate) {
        if (tradeDate == null) {
            return false;
        }
        
        LocalDate today = LocalDate.now();
        LocalDate oneYearAgo = today.minusYears(1);
        LocalDate oneMonthLater = today.plusMonths(1);
        
        return !tradeDate.isBefore(oneYearAgo) && !tradeDate.isAfter(oneMonthLater);
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        @Override
        public String toString() {
            return valid ? "Valid" : "Invalid: " + errorMessage;
        }
    }
}
