package com.futures.system.utils;

import com.futures.system.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 数据清洗和转换工具类
 * 提供数据标准化、格式转换和异常处理方法
 */
public class DataCleaningUtils {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataCleaningUtils.class);
    
    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 清洗单腿委托订单数据
     */
    public static SingleLegOrder cleanSingleLegOrder(SingleLegOrder order) {
        if (order == null) {
            return null;
        }
        
        try {
            // 清洗字符串字段
            order.setOrderNumber(cleanString(order.getOrderNumber()));
            order.setContractCode(cleanString(order.getContractCode()));
            order.setMemberCode(cleanString(order.getMemberCode()));
            order.setTradeCode(cleanString(order.getTradeCode()));
            order.setCommodityCode(cleanString(order.getCommodityCode()));
            order.setExchangeCode(cleanString(order.getExchangeCode()));
            order.setSettlementMemberCode(cleanString(order.getSettlementMemberCode()));
            
            // 清洗数值字段
            order.setOrderPrice(cleanPrice(order.getOrderPrice()));
            order.setOrderVolume(cleanVolume(order.getOrderVolume()));
            order.setRemainingVolume(cleanVolume(order.getRemainingVolume()));
            order.setTradedVolume(cleanVolume(order.getTradedVolume()));
            
            // 处理剩余量和成交量的一致性
            if (order.getOrderVolume() != null) {
                if (order.getRemainingVolume() == null) {
                    order.setRemainingVolume(order.getOrderVolume());
                }
                if (order.getTradedVolume() == null) {
                    order.setTradedVolume(BigDecimal.ZERO);
                }
                
                // 确保数量一致性：委托量 = 剩余量 + 成交量
                BigDecimal calculatedRemaining = order.getOrderVolume().subtract(order.getTradedVolume());
                if (calculatedRemaining.compareTo(BigDecimal.ZERO) >= 0) {
                    order.setRemainingVolume(calculatedRemaining);
                }
            }
            
            // 清洗时间字段
            order.setOrderTime(cleanTime(order.getOrderTime()));
            order.setTradeDate(cleanDate(order.getTradeDate()));
            
            return order;
            
        } catch (Exception e) {
            LOG.error("Error cleaning SingleLegOrder: {}", order, e);
            return null;
        }
    }
    
    /**
     * 清洗组合委托订单数据
     */
    public static CombinationOrder cleanCombinationOrder(CombinationOrder order) {
        if (order == null) {
            return null;
        }
        
        try {
            // 清洗字符串字段
            order.setOrderNumber(cleanString(order.getOrderNumber()));
            order.setCombinationContractCode(cleanString(order.getCombinationContractCode()));
            order.setLeg1ContractCode(cleanString(order.getLeg1ContractCode()));
            order.setLeg2ContractCode(cleanString(order.getLeg2ContractCode()));
            order.setMemberCode(cleanString(order.getMemberCode()));
            order.setTradeCode(cleanString(order.getTradeCode()));
            order.setCommodityCode(cleanString(order.getCommodityCode()));
            order.setExchangeCode(cleanString(order.getExchangeCode()));
            order.setSettlementMemberCode(cleanString(order.getSettlementMemberCode()));
            
            // 清洗数值字段
            order.setOrderPrice(cleanPrice(order.getOrderPrice()));
            order.setOrderVolume(cleanVolume(order.getOrderVolume()));
            order.setRemainingVolume(cleanVolume(order.getRemainingVolume()));
            order.setTradedVolume(cleanVolume(order.getTradedVolume()));
            
            // 处理数量一致性
            if (order.getOrderVolume() != null) {
                if (order.getRemainingVolume() == null) {
                    order.setRemainingVolume(order.getOrderVolume());
                }
                if (order.getTradedVolume() == null) {
                    order.setTradedVolume(BigDecimal.ZERO);
                }
                
                BigDecimal calculatedRemaining = order.getOrderVolume().subtract(order.getTradedVolume());
                if (calculatedRemaining.compareTo(BigDecimal.ZERO) >= 0) {
                    order.setRemainingVolume(calculatedRemaining);
                }
            }
            
            // 清洗时间字段
            order.setOrderTime(cleanTime(order.getOrderTime()));
            order.setTradeDate(cleanDate(order.getTradeDate()));
            
            return order;
            
        } catch (Exception e) {
            LOG.error("Error cleaning CombinationOrder: {}", order, e);
            return null;
        }
    }
    
    /**
     * 清洗成交明细数据
     */
    public static TradeDetail cleanTradeDetail(TradeDetail trade) {
        if (trade == null) {
            return null;
        }
        
        try {
            // 清洗字符串字段
            trade.setTradeNumber(cleanString(trade.getTradeNumber()));
            trade.setOrderNumber(cleanString(trade.getOrderNumber()));
            trade.setContractCode(cleanString(trade.getContractCode()));
            trade.setMemberCode(cleanString(trade.getMemberCode()));
            trade.setTradeCode(cleanString(trade.getTradeCode()));
            trade.setCommodityCode(cleanString(trade.getCommodityCode()));
            trade.setExchangeCode(cleanString(trade.getExchangeCode()));
            trade.setSettlementMemberCode(cleanString(trade.getSettlementMemberCode()));
            
            // 清洗数值字段
            trade.setTradePrice(cleanPrice(trade.getTradePrice()));
            trade.setTradeVolume(cleanVolume(trade.getTradeVolume()));
            
            // 清洗时间字段
            trade.setTradeTime(cleanTime(trade.getTradeTime()));
            trade.setTradeDate(cleanDate(trade.getTradeDate()));
            
            return trade;
            
        } catch (Exception e) {
            LOG.error("Error cleaning TradeDetail: {}", trade, e);
            return null;
        }
    }
    
    /**
     * 清洗字符串字段
     */
    private static String cleanString(String value) {
        if (value == null) {
            return null;
        }
        
        String cleaned = value.trim().toUpperCase();
        return cleaned.isEmpty() ? null : cleaned;
    }
    
    /**
     * 清洗价格字段
     */
    private static BigDecimal cleanPrice(BigDecimal price) {
        if (price == null) {
            return null;
        }
        
        // 价格保留6位小数
        return price.setScale(6, RoundingMode.HALF_UP);
    }
    
    /**
     * 清洗数量字段
     */
    private static BigDecimal cleanVolume(BigDecimal volume) {
        if (volume == null) {
            return null;
        }
        
        // 数量保留0位小数（整数）
        return volume.setScale(0, RoundingMode.HALF_UP);
    }
    
    /**
     * 清洗时间字段
     */
    private static LocalTime cleanTime(LocalTime time) {
        if (time == null) {
            return null;
        }
        
        // 时间精确到秒，去掉纳秒部分
        return time.withNano(0);
    }
    
    /**
     * 清洗日期字段
     */
    private static LocalDate cleanDate(LocalDate date) {
        if (date == null) {
            return null;
        }
        
        // 验证日期合理性
        if (!DataValidationUtils.isValidTradeDate(date)) {
            LOG.warn("Invalid trade date: {}, using current date", date);
            return LocalDate.now();
        }
        
        return date;
    }
    
    /**
     * 解析时间字符串
     */
    public static LocalTime parseTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty() || "00:00:00".equals(timeStr)) {
            return null;
        }
        
        try {
            return LocalTime.parse(timeStr.trim(), TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 尝试HH:mm格式
                return LocalTime.parse(timeStr.trim(), DateTimeFormatter.ofPattern("HH:mm"));
            } catch (DateTimeParseException e2) {
                LOG.warn("Failed to parse time: {}", timeStr);
                return null;
            }
        }
    }
    
    /**
     * 解析日期字符串
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return LocalDate.parse(dateStr.trim(), DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            LOG.warn("Failed to parse date: {}", dateStr);
            return null;
        }
    }
    
    /**
     * 标准化合约代码格式
     */
    public static String normalizeContractCode(String contractCode) {
        if (contractCode == null || contractCode.trim().isEmpty()) {
            return null;
        }
        
        return contractCode.trim().toUpperCase();
    }
    
    /**
     * 标准化报单编号格式
     */
    public static String normalizeOrderNumber(String orderNumber) {
        if (orderNumber == null || orderNumber.trim().isEmpty()) {
            return null;
        }
        
        return orderNumber.trim().toUpperCase();
    }
    
    /**
     * 检查数据是否需要进一步清洗
     */
    public static boolean needsCleaning(OrderBookEvent event) {
        if (event == null) {
            return false;
        }
        
        // 检查关键字段是否需要清洗
        return hasWhitespace(event.getContractCode()) ||
               hasWhitespace(event.getMemberCode()) ||
               hasWhitespace(event.getExchangeCode()) ||
               hasWhitespace(event.getCommodityCode());
    }
    
    /**
     * 检查字符串是否包含空白字符
     */
    private static boolean hasWhitespace(String str) {
        return str != null && !str.equals(str.trim());
    }
}
