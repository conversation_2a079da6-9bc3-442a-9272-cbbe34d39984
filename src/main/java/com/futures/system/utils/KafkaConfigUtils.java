package com.futures.system.utils;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Properties;

/**
 * Kafka配置工具类
 * 提供标准的Kafka消费者配置
 */
public class KafkaConfigUtils {
    
    /**
     * 默认Kafka服务器地址
     */
    public static final String DEFAULT_BOOTSTRAP_SERVERS = "localhost:9092";
    
    /**
     * 默认消费者组ID前缀
     */
    public static final String DEFAULT_GROUP_ID_PREFIX = "futures-orderbook-rebuild";
    
    /**
     * 创建Kafka消费者配置
     */
    public static Properties createKafkaConsumerProperties(String bootstrapServers, String groupId) {
        Properties properties = new Properties();
        
        // 基础配置
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        
        // 序列化配置
        properties.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        properties.setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
        // 消费策略配置
        properties.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        properties.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        
        // 性能优化配置
        properties.setProperty(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, "1024");
        properties.setProperty(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "500");
        properties.setProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "1000");
        
        // 会话配置
        properties.setProperty(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "30000");
        properties.setProperty(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "10000");
        
        return properties;
    }
    
    /**
     * 创建默认Kafka消费者配置
     */
    public static Properties createDefaultKafkaConsumerProperties(String topicSuffix) {
        String groupId = DEFAULT_GROUP_ID_PREFIX + "-" + topicSuffix;
        return createKafkaConsumerProperties(DEFAULT_BOOTSTRAP_SERVERS, groupId);
    }
    
    /**
     * 从环境变量获取Kafka配置
     */
    public static Properties createKafkaConsumerPropertiesFromEnv(String topicSuffix) {
        String bootstrapServers = System.getenv("KAFKA_BOOTSTRAP_SERVERS");
        if (bootstrapServers == null || bootstrapServers.trim().isEmpty()) {
            bootstrapServers = DEFAULT_BOOTSTRAP_SERVERS;
        }
        
        String groupIdPrefix = System.getenv("KAFKA_GROUP_ID_PREFIX");
        if (groupIdPrefix == null || groupIdPrefix.trim().isEmpty()) {
            groupIdPrefix = DEFAULT_GROUP_ID_PREFIX;
        }
        
        String groupId = groupIdPrefix + "-" + topicSuffix;
        return createKafkaConsumerProperties(bootstrapServers, groupId);
    }
}
