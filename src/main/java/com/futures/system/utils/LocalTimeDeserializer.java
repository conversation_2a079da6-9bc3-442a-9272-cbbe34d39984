package com.futures.system.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * LocalTime反序列化器
 * 将HH:mm:ss格式的字符串反序列化为LocalTime对象
 */
public class LocalTimeDeserializer extends JsonDeserializer<LocalTime> {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public LocalTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String timeStr = p.getValueAsString();
        if (timeStr == null || timeStr.trim().isEmpty() || "00:00:00".equals(timeStr)) {
            return null;
        }
        
        try {
            return LocalTime.parse(timeStr, FORMATTER);
        } catch (DateTimeParseException e) {
            // 如果解析失败，尝试其他格式或返回null
            try {
                // 尝试解析HH:mm格式
                return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern("HH:mm"));
            } catch (DateTimeParseException e2) {
                // 解析失败，返回null
                return null;
            }
        }
    }
}
