package com.futures.system.utils;

import com.futures.system.model.*;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.MetricGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据质量监控工具
 * 提供数据处理过程中的质量监控和指标统计
 */
public class DataQualityMonitor {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataQualityMonitor.class);
    
    /**
     * 单腿委托订单质量监控函数
     */
    public static class SingleLegOrderQualityMonitor extends RichMapFunction<SingleLegOrder, SingleLegOrder> {
        
        private transient Counter totalRecords;
        private transient Counter validRecords;
        private transient Counter invalidRecords;
        private transient Counter nullRecords;
        private transient AtomicLong lastProcessTime;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                    .addGroup("data_quality")
                    .addGroup("single_leg_orders");
            
            totalRecords = metricGroup.counter("total_records");
            validRecords = metricGroup.counter("valid_records");
            invalidRecords = metricGroup.counter("invalid_records");
            nullRecords = metricGroup.counter("null_records");
            
            lastProcessTime = new AtomicLong(System.currentTimeMillis());
            
            metricGroup.gauge("last_process_time", new Gauge<Long>() {
                @Override
                public Long getValue() {
                    return lastProcessTime.get();
                }
            });
            
            metricGroup.gauge("valid_rate", new Gauge<Double>() {
                @Override
                public Double getValue() {
                    long total = totalRecords.getCount();
                    return total > 0 ? (double) validRecords.getCount() / total : 0.0;
                }
            });
        }
        
        @Override
        public SingleLegOrder map(SingleLegOrder order) throws Exception {
            totalRecords.inc();
            lastProcessTime.set(System.currentTimeMillis());
            
            if (order == null) {
                nullRecords.inc();
                return null;
            }
            
            DataValidationUtils.ValidationResult result = 
                    DataValidationUtils.validateSingleLegOrder(order);
            
            if (result.isValid()) {
                validRecords.inc();
            } else {
                invalidRecords.inc();
                LOG.debug("Invalid SingleLegOrder: {}", result.getErrorMessage());
            }
            
            return order;
        }
    }
    
    /**
     * 组合委托订单质量监控函数
     */
    public static class CombinationOrderQualityMonitor extends RichMapFunction<CombinationOrder, CombinationOrder> {
        
        private transient Counter totalRecords;
        private transient Counter validRecords;
        private transient Counter invalidRecords;
        private transient Counter nullRecords;
        private transient AtomicLong lastProcessTime;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                    .addGroup("data_quality")
                    .addGroup("combination_orders");
            
            totalRecords = metricGroup.counter("total_records");
            validRecords = metricGroup.counter("valid_records");
            invalidRecords = metricGroup.counter("invalid_records");
            nullRecords = metricGroup.counter("null_records");
            
            lastProcessTime = new AtomicLong(System.currentTimeMillis());
            
            metricGroup.gauge("last_process_time", new Gauge<Long>() {
                @Override
                public Long getValue() {
                    return lastProcessTime.get();
                }
            });
            
            metricGroup.gauge("valid_rate", new Gauge<Double>() {
                @Override
                public Double getValue() {
                    long total = totalRecords.getCount();
                    return total > 0 ? (double) validRecords.getCount() / total : 0.0;
                }
            });
        }
        
        @Override
        public CombinationOrder map(CombinationOrder order) throws Exception {
            totalRecords.inc();
            lastProcessTime.set(System.currentTimeMillis());
            
            if (order == null) {
                nullRecords.inc();
                return null;
            }
            
            DataValidationUtils.ValidationResult result = 
                    DataValidationUtils.validateCombinationOrder(order);
            
            if (result.isValid()) {
                validRecords.inc();
            } else {
                invalidRecords.inc();
                LOG.debug("Invalid CombinationOrder: {}", result.getErrorMessage());
            }
            
            return order;
        }
    }
    
    /**
     * 成交明细质量监控函数
     */
    public static class TradeDetailQualityMonitor extends RichMapFunction<TradeDetail, TradeDetail> {
        
        private transient Counter totalRecords;
        private transient Counter validRecords;
        private transient Counter invalidRecords;
        private transient Counter nullRecords;
        private transient AtomicLong lastProcessTime;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                    .addGroup("data_quality")
                    .addGroup("trade_details");
            
            totalRecords = metricGroup.counter("total_records");
            validRecords = metricGroup.counter("valid_records");
            invalidRecords = metricGroup.counter("invalid_records");
            nullRecords = metricGroup.counter("null_records");
            
            lastProcessTime = new AtomicLong(System.currentTimeMillis());
            
            metricGroup.gauge("last_process_time", new Gauge<Long>() {
                @Override
                public Long getValue() {
                    return lastProcessTime.get();
                }
            });
            
            metricGroup.gauge("valid_rate", new Gauge<Double>() {
                @Override
                public Double getValue() {
                    long total = totalRecords.getCount();
                    return total > 0 ? (double) validRecords.getCount() / total : 0.0;
                }
            });
        }
        
        @Override
        public TradeDetail map(TradeDetail trade) throws Exception {
            totalRecords.inc();
            lastProcessTime.set(System.currentTimeMillis());
            
            if (trade == null) {
                nullRecords.inc();
                return null;
            }
            
            DataValidationUtils.ValidationResult result = 
                    DataValidationUtils.validateTradeDetail(trade);
            
            if (result.isValid()) {
                validRecords.inc();
            } else {
                invalidRecords.inc();
                LOG.debug("Invalid TradeDetail: {}", result.getErrorMessage());
            }
            
            return trade;
        }
    }
    
    /**
     * 数据质量统计信息
     */
    public static class DataQualityStats {
        private long totalRecords;
        private long validRecords;
        private long invalidRecords;
        private long nullRecords;
        private LocalDateTime lastUpdateTime;
        
        public DataQualityStats() {
            this.lastUpdateTime = LocalDateTime.now();
        }
        
        public void incrementTotal() {
            totalRecords++;
            lastUpdateTime = LocalDateTime.now();
        }
        
        public void incrementValid() {
            validRecords++;
        }
        
        public void incrementInvalid() {
            invalidRecords++;
        }
        
        public void incrementNull() {
            nullRecords++;
        }
        
        public double getValidRate() {
            return totalRecords > 0 ? (double) validRecords / totalRecords : 0.0;
        }
        
        public double getInvalidRate() {
            return totalRecords > 0 ? (double) invalidRecords / totalRecords : 0.0;
        }
        
        // Getters
        public long getTotalRecords() { return totalRecords; }
        public long getValidRecords() { return validRecords; }
        public long getInvalidRecords() { return invalidRecords; }
        public long getNullRecords() { return nullRecords; }
        public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
        
        @Override
        public String toString() {
            return String.format("DataQualityStats{total=%d, valid=%d(%.2f%%), invalid=%d(%.2f%%), null=%d, lastUpdate=%s}",
                    totalRecords, validRecords, getValidRate() * 100, 
                    invalidRecords, getInvalidRate() * 100, nullRecords, lastUpdateTime);
        }
    }
}
