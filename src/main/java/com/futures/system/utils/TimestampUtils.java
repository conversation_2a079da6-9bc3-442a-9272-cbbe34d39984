package com.futures.system.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间戳处理工具类
 * 提供时间转换、排序和事件时间处理功能
 */
public class TimestampUtils {
    
    private static final Logger LOG = LoggerFactory.getLogger(TimestampUtils.class);
    
    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 默认时区
    private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();
    
    /**
     * 将交易日期和时间转换为时间戳
     */
    public static long toTimestamp(LocalDate date, LocalTime time) {
        if (date == null || time == null) {
            return System.currentTimeMillis();
        }
        
        try {
            return date.atTime(time).atZone(DEFAULT_ZONE).toInstant().toEpochMilli();
        } catch (Exception e) {
            LOG.warn("Failed to convert date {} and time {} to timestamp", date, time, e);
            return System.currentTimeMillis();
        }
    }
    
    /**
     * 将交易日期和时间转换为UTC时间戳
     */
    public static long toUtcTimestamp(LocalDate date, LocalTime time) {
        if (date == null || time == null) {
            return System.currentTimeMillis();
        }
        
        try {
            return date.atTime(time).atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
        } catch (Exception e) {
            LOG.warn("Failed to convert date {} and time {} to UTC timestamp", date, time, e);
            return System.currentTimeMillis();
        }
    }
    
    /**
     * 从时间戳转换为LocalDateTime
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), DEFAULT_ZONE);
        } catch (Exception e) {
            LOG.warn("Failed to convert timestamp {} to LocalDateTime", timestamp, e);
            return LocalDateTime.now();
        }
    }
    
    /**
     * 从UTC时间戳转换为LocalDateTime
     */
    public static LocalDateTime fromUtcTimestamp(long timestamp) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.UTC);
        } catch (Exception e) {
            LOG.warn("Failed to convert UTC timestamp {} to LocalDateTime", timestamp, e);
            return LocalDateTime.now();
        }
    }
    
    /**
     * 解析时间字符串并结合毫秒/微秒信息
     */
    public static LocalTime parseTimeWithPrecision(String timeStr, Long millisec, Long microsec) {
        LocalTime baseTime = parseTime(timeStr);
        if (baseTime == null) {
            return null;
        }
        
        try {
            // 添加毫秒精度
            if (millisec != null && millisec > 0) {
                baseTime = baseTime.plusNanos(millisec * 1_000_000);
            }
            
            // 添加微秒精度（如果有的话）
            if (microsec != null && microsec > 0) {
                baseTime = baseTime.plusNanos(microsec * 1_000);
            }
            
            return baseTime;
        } catch (Exception e) {
            LOG.warn("Failed to add precision to time {}, using base time", timeStr, e);
            return baseTime;
        }
    }
    
    /**
     * 解析时间字符串
     */
    public static LocalTime parseTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty() || "00:00:00".equals(timeStr)) {
            return null;
        }
        
        try {
            return LocalTime.parse(timeStr.trim(), TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 尝试HH:mm格式
                return LocalTime.parse(timeStr.trim(), DateTimeFormatter.ofPattern("HH:mm"));
            } catch (DateTimeParseException e2) {
                LOG.debug("Failed to parse time: {}", timeStr);
                return null;
            }
        }
    }
    
    /**
     * 解析日期字符串
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return LocalDate.parse(dateStr.trim(), DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 尝试其他格式
                return LocalDate.parse(dateStr.trim(), DateTimeFormatter.ofPattern("yyyyMMdd"));
            } catch (DateTimeParseException e2) {
                LOG.debug("Failed to parse date: {}", dateStr);
                return null;
            }
        }
    }
    
    /**
     * 获取当前交易日
     * 如果当前时间在凌晨3点之前，认为是前一个交易日
     */
    public static LocalDate getCurrentTradeDate() {
        LocalDateTime now = LocalDateTime.now();
        if (now.getHour() < 3) {
            return now.toLocalDate().minusDays(1);
        }
        return now.toLocalDate();
    }
    
    /**
     * 检查时间是否在交易时间段内
     */
    public static boolean isInTradingHours(LocalTime time) {
        if (time == null) {
            return false;
        }
        
        // 期货交易时间段（简化版本）
        // 日盘：09:00-15:00
        // 夜盘：21:00-02:30（次日）
        
        return (time.isAfter(LocalTime.of(9, 0)) && time.isBefore(LocalTime.of(15, 0))) ||
               (time.isAfter(LocalTime.of(21, 0)) || time.isBefore(LocalTime.of(2, 30)));
    }
    
    /**
     * 计算两个时间之间的毫秒差
     */
    public static long getTimeDifferenceMillis(LocalDate date1, LocalTime time1, 
                                              LocalDate date2, LocalTime time2) {
        if (date1 == null || time1 == null || date2 == null || time2 == null) {
            return 0;
        }
        
        try {
            long timestamp1 = toTimestamp(date1, time1);
            long timestamp2 = toTimestamp(date2, time2);
            return Math.abs(timestamp2 - timestamp1);
        } catch (Exception e) {
            LOG.warn("Failed to calculate time difference", e);
            return 0;
        }
    }
    
    /**
     * 格式化时间戳为可读字符串
     */
    public static String formatTimestamp(long timestamp) {
        try {
            return fromTimestamp(timestamp).format(DATETIME_FORMATTER);
        } catch (Exception e) {
            return "Invalid timestamp: " + timestamp;
        }
    }
    
    /**
     * 获取下一个500毫秒对齐的时间戳
     */
    public static long getNext500msAlignedTimestamp(long currentTimestamp) {
        // 将时间戳对齐到500毫秒边界
        long remainder = currentTimestamp % 500;
        if (remainder == 0) {
            return currentTimestamp + 500;
        } else {
            return currentTimestamp + (500 - remainder);
        }
    }
    
    /**
     * 检查时间戳是否在500毫秒边界上
     */
    public static boolean isAlignedTo500ms(long timestamp) {
        return timestamp % 500 == 0;
    }
    
    /**
     * 获取当前时间的500毫秒对齐时间戳
     */
    public static long getCurrentAligned500msTimestamp() {
        long now = System.currentTimeMillis();
        long remainder = now % 500;
        return now - remainder;
    }
    
    /**
     * 验证时间戳是否合理（不能太久远或太未来）
     */
    public static boolean isValidTimestamp(long timestamp) {
        long now = System.currentTimeMillis();
        long oneYearAgo = now - (365L * 24 * 60 * 60 * 1000);
        long oneHourLater = now + (60 * 60 * 1000);
        
        return timestamp >= oneYearAgo && timestamp <= oneHourLater;
    }
}
