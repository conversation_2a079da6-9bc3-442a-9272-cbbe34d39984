package com.futures.system.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.futures.system.model.CombinationOrder;
import com.futures.system.model.SingleLegOrder;
import com.futures.system.model.TradeDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JSON反序列化工具类
 * 提供统一的JSON解析配置和方法
 */
public class JsonDeserializationUtils {
    
    private static final Logger LOG = LoggerFactory.getLogger(JsonDeserializationUtils.class);
    
    private static final ObjectMapper OBJECT_MAPPER;
    
    static {
        OBJECT_MAPPER = new ObjectMapper();
        
        // 注册Java时间模块
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
        
        // 配置反序列化特性
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
    }
    
    /**
     * 获取配置好的ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
    
    /**
     * 反序列化单腿委托订单
     */
    public static SingleLegOrder deserializeSingleLegOrder(String json) {
        try {
            return OBJECT_MAPPER.readValue(json, SingleLegOrder.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize SingleLegOrder from JSON: {}", json, e);
            return null;
        }
    }
    
    /**
     * 反序列化组合委托订单
     */
    public static CombinationOrder deserializeCombinationOrder(String json) {
        try {
            return OBJECT_MAPPER.readValue(json, CombinationOrder.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize CombinationOrder from JSON: {}", json, e);
            return null;
        }
    }
    
    /**
     * 反序列化成交明细
     */
    public static TradeDetail deserializeTradeDetail(String json) {
        try {
            return OBJECT_MAPPER.readValue(json, TradeDetail.class);
        } catch (Exception e) {
            LOG.error("Failed to deserialize TradeDetail from JSON: {}", json, e);
            return null;
        }
    }
    
    /**
     * 通用JSON反序列化方法
     */
    public static <T> T deserialize(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            LOG.error("Failed to deserialize {} from JSON: {}", clazz.getSimpleName(), json, e);
            return null;
        }
    }
    
    /**
     * 序列化对象为JSON字符串
     */
    public static String serialize(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            LOG.error("Failed to serialize object to JSON: {}", object, e);
            return null;
        }
    }
    
    /**
     * 验证JSON字符串格式
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
