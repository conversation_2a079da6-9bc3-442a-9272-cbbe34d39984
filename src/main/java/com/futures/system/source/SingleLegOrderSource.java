package com.futures.system.source;

import com.futures.system.model.SingleLegOrder;
import com.futures.system.utils.JsonDeserializationUtils;
import com.futures.system.utils.KafkaConfigUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 单腿委托数据源连接器
 * 从Kafka读取单腿委托数据并转换为SingleLegOrder对象流
 */
public class SingleLegOrderSource {
    
    private static final Logger LOG = LoggerFactory.getLogger(SingleLegOrderSource.class);
    
    /**
     * Kafka Topic名称
     */
    public static final String TOPIC_NAME = "singleleg_order_data_event";
    
    /**
     * 消费者组后缀
     */
    private static final String CONSUMER_GROUP_SUFFIX = "singleleg-orders";
    
    /**
     * 创建单腿委托数据流
     */
    public static DataStream<SingleLegOrder> createDataStream(StreamExecutionEnvironment env) {
        return createDataStream(env, null);
    }
    
    /**
     * 创建单腿委托数据流（自定义Kafka配置）
     */
    public static DataStream<SingleLegOrder> createDataStream(StreamExecutionEnvironment env, Properties kafkaProperties) {
        // 使用默认配置或自定义配置
        Properties properties = kafkaProperties != null ? 
            kafkaProperties : KafkaConfigUtils.createKafkaConsumerPropertiesFromEnv(CONSUMER_GROUP_SUFFIX);
        
        // 创建Kafka Source
        KafkaSource<SingleLegOrder> kafkaSource = KafkaSource.<SingleLegOrder>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setTopics(TOPIC_NAME)
                .setGroupId(properties.getProperty("group.id"))
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SingleLegOrderDeserializationSchema())
                .setProperties(properties)
                .build();
        
        // 创建数据流并配置水印策略
        return env.fromSource(
                kafkaSource,
                OrderBookEventWatermarkStrategy.create(),
                "SingleLegOrderSource"
        );
    }
    
    /**
     * 单腿委托订单反序列化Schema
     */
    private static class SingleLegOrderDeserializationSchema implements DeserializationSchema<SingleLegOrder> {
        
        @Override
        public SingleLegOrder deserialize(byte[] message) throws IOException {
            if (message == null || message.length == 0) {
                return null;
            }
            
            try {
                String json = new String(message, StandardCharsets.UTF_8);
                SingleLegOrder order = JsonDeserializationUtils.deserializeSingleLegOrder(json);
                
                if (order == null) {
                    LOG.warn("Failed to deserialize SingleLegOrder from message: {}", json);
                    return null;
                }
                
                // 验证必要字段
                if (!isValidOrder(order)) {
                    LOG.warn("Invalid SingleLegOrder received: {}", order);
                    return null;
                }
                
                return order;
            } catch (Exception e) {
                LOG.error("Error deserializing SingleLegOrder message", e);
                return null;
            }
        }
        
        @Override
        public boolean isEndOfStream(SingleLegOrder nextElement) {
            return false;
        }
        
        @Override
        public TypeInformation<SingleLegOrder> getProducedType() {
            return TypeInformation.of(SingleLegOrder.class);
        }
        
        /**
         * 验证订单数据的有效性
         */
        private boolean isValidOrder(SingleLegOrder order) {
            return order.getOrderNumber() != null && !order.getOrderNumber().trim().isEmpty() &&
                   order.getContractCode() != null && !order.getContractCode().trim().isEmpty() &&
                   order.getOrderPrice() != null &&
                   order.getOrderVolume() != null &&
                   order.getBuySellFlag() != null &&
                   order.getOrderStatus() != null;
        }
    }
}
