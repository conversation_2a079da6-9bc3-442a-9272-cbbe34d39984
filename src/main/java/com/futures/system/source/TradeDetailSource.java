package com.futures.system.source;

import com.futures.system.model.TradeDetail;
import com.futures.system.utils.JsonDeserializationUtils;
import com.futures.system.utils.KafkaConfigUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 成交明细数据源连接器
 * 从Kafka读取成交明细数据并转换为TradeDetail对象流
 */
public class TradeDetailSource {
    
    private static final Logger LOG = LoggerFactory.getLogger(TradeDetailSource.class);
    
    /**
     * Kafka Topic名称
     */
    public static final String TOPIC_NAME = "trade_data_event";
    
    /**
     * 消费者组后缀
     */
    private static final String CONSUMER_GROUP_SUFFIX = "trade-details";
    
    /**
     * 创建成交明细数据流
     */
    public static DataStream<TradeDetail> createDataStream(StreamExecutionEnvironment env) {
        return createDataStream(env, null);
    }
    
    /**
     * 创建成交明细数据流（自定义Kafka配置）
     */
    public static DataStream<TradeDetail> createDataStream(StreamExecutionEnvironment env, Properties kafkaProperties) {
        // 使用默认配置或自定义配置
        Properties properties = kafkaProperties != null ? 
            kafkaProperties : KafkaConfigUtils.createKafkaConsumerPropertiesFromEnv(CONSUMER_GROUP_SUFFIX);
        
        // 创建Kafka Source
        KafkaSource<TradeDetail> kafkaSource = KafkaSource.<TradeDetail>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setTopics(TOPIC_NAME)
                .setGroupId(properties.getProperty("group.id"))
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new TradeDetailDeserializationSchema())
                .setProperties(properties)
                .build();
        
        // 创建数据流并配置水印策略
        return env.fromSource(
                kafkaSource,
                OrderBookEventWatermarkStrategy.create(),
                "TradeDetailSource"
        );
    }
    
    /**
     * 成交明细反序列化Schema
     */
    private static class TradeDetailDeserializationSchema implements DeserializationSchema<TradeDetail> {
        
        @Override
        public TradeDetail deserialize(byte[] message) throws IOException {
            if (message == null || message.length == 0) {
                return null;
            }
            
            try {
                String json = new String(message, StandardCharsets.UTF_8);
                TradeDetail trade = JsonDeserializationUtils.deserializeTradeDetail(json);
                
                if (trade == null) {
                    LOG.warn("Failed to deserialize TradeDetail from message: {}", json);
                    return null;
                }
                
                // 验证必要字段
                if (!isValidTrade(trade)) {
                    LOG.warn("Invalid TradeDetail received: {}", trade);
                    return null;
                }
                
                return trade;
            } catch (Exception e) {
                LOG.error("Error deserializing TradeDetail message", e);
                return null;
            }
        }
        
        @Override
        public boolean isEndOfStream(TradeDetail nextElement) {
            return false;
        }
        
        @Override
        public TypeInformation<TradeDetail> getProducedType() {
            return TypeInformation.of(TradeDetail.class);
        }
        
        /**
         * 验证成交明细数据的有效性
         */
        private boolean isValidTrade(TradeDetail trade) {
            return trade.getTradeNumber() != null && !trade.getTradeNumber().trim().isEmpty() &&
                   trade.getOrderNumber() != null && !trade.getOrderNumber().trim().isEmpty() &&
                   trade.getContractCode() != null && !trade.getContractCode().trim().isEmpty() &&
                   trade.getTradePrice() != null &&
                   trade.getTradeVolume() != null &&
                   trade.getBuySellFlag() != null &&
                   trade.getTradeType() != null &&
                   trade.getSettlementMemberCode() != null && !trade.getSettlementMemberCode().trim().isEmpty();
        }
    }
}
