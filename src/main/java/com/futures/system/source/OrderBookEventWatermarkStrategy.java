package com.futures.system.source;

import com.futures.system.model.OrderBookEvent;
import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;

import java.time.Duration;

/**
 * 订单簿事件水印策略
 * 为订单簿事件提供事件时间提取和水印生成
 *
 * 参数选择说明：
 * - 最大乱序时间150ms：考虑网络延迟和系统处理时间，但确保不影响0.5秒订单簿快照的精确输出
 * - 空闲超时1.5秒：适合期货交易的高频特性，避免长时间等待但允许短暂的数据间隔
 * - 这些参数确保水印不会阻塞ProcessFunction中500ms定时器的准时触发
 */
public class OrderBookEventWatermarkStrategy {

    /**
     * 默认最大乱序时间：150毫秒
     * 选择150ms的原因：
     * 1. 足够容忍网络延迟和消息传输的小幅乱序
     * 2. 远小于500ms订单簿输出间隔，确保不影响定时快照
     * 3. 在数据准确性和实时性之间取得平衡
     */
    private static final Duration DEFAULT_MAX_OUT_OF_ORDERNESS = Duration.ofMillis(150);

    /**
     * 默认空闲超时时间：1.5秒
     * 选择1.5s的原因：
     * 1. 适合期货交易的高频特性，避免过长等待
     * 2. 允许短暂的数据流中断而不影响水印推进
     * 3. 确保在数据稀少时水印能够正常推进，触发定时处理
     */
    private static final Duration DEFAULT_IDLE_TIMEOUT = Duration.ofMillis(1500);
    
    /**
     * 创建订单簿事件的水印策略（使用优化后的默认参数）
     * 适用于0.5秒订单簿快照输出的实时处理场景
     */
    public static WatermarkStrategy<OrderBookEvent> create() {
        return create(DEFAULT_MAX_OUT_OF_ORDERNESS, DEFAULT_IDLE_TIMEOUT);
    }

    /**
     * 创建自定义参数的水印策略
     *
     * @param maxOutOfOrderness 最大乱序时间，建议不超过200ms以确保订单簿快照的实时性
     * @param idleTimeout 空闲超时时间，建议1-2秒适合期货交易场景
     */
    public static WatermarkStrategy<OrderBookEvent> create(Duration maxOutOfOrderness, Duration idleTimeout) {
        return WatermarkStrategy
                .<OrderBookEvent>forBoundedOutOfOrderness(maxOutOfOrderness)
                .withTimestampAssigner(new OrderBookEventTimestampAssigner())
                .withIdleness(idleTimeout);
    }

    /**
     * 创建超低延迟水印策略（用于对延迟极其敏感的场景）
     * 最大乱序时间50ms，空闲超时1秒
     */
    public static WatermarkStrategy<OrderBookEvent> createLowLatency() {
        return create(Duration.ofMillis(50), Duration.ofSeconds(1));
    }
    
    /**
     * 订单簿事件时间戳分配器
     */
    private static class OrderBookEventTimestampAssigner implements SerializableTimestampAssigner<OrderBookEvent> {
        
        @Override
        public long extractTimestamp(OrderBookEvent event, long recordTimestamp) {
            try {
                // 优先使用事件自身的时间戳
                long eventTimestamp = event.getEventTimestamp();
                
                // 如果事件时间戳无效，使用记录时间戳
                if (eventTimestamp <= 0) {
                    return recordTimestamp > 0 ? recordTimestamp : System.currentTimeMillis();
                }
                
                return eventTimestamp;
            } catch (Exception e) {
                // 异常情况下使用当前时间
                return System.currentTimeMillis();
            }
        }
    }
}
