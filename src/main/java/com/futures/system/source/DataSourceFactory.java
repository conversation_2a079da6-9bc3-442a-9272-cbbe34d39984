package com.futures.system.source;

import com.futures.system.model.CombinationOrder;
import com.futures.system.model.SingleLegOrder;
import com.futures.system.model.TradeDetail;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * 数据源工厂类
 * 统一管理和创建所有数据源连接器
 */
public class DataSourceFactory {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataSourceFactory.class);
    
    /**
     * 数据源配置
     */
    public static class DataSourceConfig {
        private Properties kafkaProperties;
        private boolean enableMetrics;
        private String sourceNamePrefix;
        
        public DataSourceConfig() {
            this.enableMetrics = true;
            this.sourceNamePrefix = "FuturesOrderBook";
        }
        
        public Properties getKafkaProperties() {
            return kafkaProperties;
        }
        
        public void setKafkaProperties(Properties kafkaProperties) {
            this.kafkaProperties = kafkaProperties;
        }
        
        public boolean isEnableMetrics() {
            return enableMetrics;
        }
        
        public void setEnableMetrics(boolean enableMetrics) {
            this.enableMetrics = enableMetrics;
        }
        
        public String getSourceNamePrefix() {
            return sourceNamePrefix;
        }
        
        public void setSourceNamePrefix(String sourceNamePrefix) {
            this.sourceNamePrefix = sourceNamePrefix;
        }
    }
    
    /**
     * 创建所有数据源
     */
    public static DataSources createAllDataSources(StreamExecutionEnvironment env) {
        return createAllDataSources(env, new DataSourceConfig());
    }
    
    /**
     * 创建所有数据源（自定义配置）
     */
    public static DataSources createAllDataSources(StreamExecutionEnvironment env, DataSourceConfig config) {
        LOG.info("Creating all data sources with config: {}", config);
        
        try {
            // 创建单腿委托数据流
            DataStream<SingleLegOrder> singleLegOrderStream = SingleLegOrderSource
                    .createDataStream(env, config.getKafkaProperties());
            
            // 创建组合委托数据流
            DataStream<CombinationOrder> combinationOrderStream = CombinationOrderSource
                    .createDataStream(env, config.getKafkaProperties());
            
            // 创建成交明细数据流
            DataStream<TradeDetail> tradeDetailStream = TradeDetailSource
                    .createDataStream(env, config.getKafkaProperties());
            
            // 配置并行度（可根据需要调整）
            singleLegOrderStream = singleLegOrderStream.setParallelism(4);
            combinationOrderStream = combinationOrderStream.setParallelism(2);
            tradeDetailStream = tradeDetailStream.setParallelism(4);
            
            LOG.info("Successfully created all data sources");
            
            return new DataSources(singleLegOrderStream, combinationOrderStream, tradeDetailStream);
            
        } catch (Exception e) {
            LOG.error("Failed to create data sources", e);
            throw new RuntimeException("Failed to create data sources", e);
        }
    }
    
    /**
     * 数据源集合类
     */
    public static class DataSources {
        private final DataStream<SingleLegOrder> singleLegOrderStream;
        private final DataStream<CombinationOrder> combinationOrderStream;
        private final DataStream<TradeDetail> tradeDetailStream;
        
        public DataSources(DataStream<SingleLegOrder> singleLegOrderStream,
                          DataStream<CombinationOrder> combinationOrderStream,
                          DataStream<TradeDetail> tradeDetailStream) {
            this.singleLegOrderStream = singleLegOrderStream;
            this.combinationOrderStream = combinationOrderStream;
            this.tradeDetailStream = tradeDetailStream;
        }
        
        public DataStream<SingleLegOrder> getSingleLegOrderStream() {
            return singleLegOrderStream;
        }
        
        public DataStream<CombinationOrder> getCombinationOrderStream() {
            return combinationOrderStream;
        }
        
        public DataStream<TradeDetail> getTradeDetailStream() {
            return tradeDetailStream;
        }
        
        /**
         * 打印数据源统计信息（用于调试）
         */
        public void printStatistics() {
            LOG.info("Data Sources Statistics:");
            LOG.info("- Single Leg Order Stream: parallelism={}", singleLegOrderStream.getParallelism());
            LOG.info("- Combination Order Stream: parallelism={}", combinationOrderStream.getParallelism());
            LOG.info("- Trade Detail Stream: parallelism={}", tradeDetailStream.getParallelism());
        }
    }
}
