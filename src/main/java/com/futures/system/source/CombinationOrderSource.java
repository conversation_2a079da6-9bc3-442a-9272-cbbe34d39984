package com.futures.system.source;

import com.futures.system.model.CombinationOrder;
import com.futures.system.utils.JsonDeserializationUtils;
import com.futures.system.utils.KafkaConfigUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 组合委托数据源连接器
 * 从Kafka读取组合委托数据并转换为CombinationOrder对象流
 */
public class CombinationOrderSource {
    
    private static final Logger LOG = LoggerFactory.getLogger(CombinationOrderSource.class);
    
    /**
     * Kafka Topic名称
     */
    public static final String TOPIC_NAME = "cmb_order_data_event";
    
    /**
     * 消费者组后缀
     */
    private static final String CONSUMER_GROUP_SUFFIX = "combination-orders";
    
    /**
     * 创建组合委托数据流
     */
    public static DataStream<CombinationOrder> createDataStream(StreamExecutionEnvironment env) {
        return createDataStream(env, null);
    }
    
    /**
     * 创建组合委托数据流（自定义Kafka配置）
     */
    public static DataStream<CombinationOrder> createDataStream(StreamExecutionEnvironment env, Properties kafkaProperties) {
        // 使用默认配置或自定义配置
        Properties properties = kafkaProperties != null ? 
            kafkaProperties : KafkaConfigUtils.createKafkaConsumerPropertiesFromEnv(CONSUMER_GROUP_SUFFIX);
        
        // 创建Kafka Source
        KafkaSource<CombinationOrder> kafkaSource = KafkaSource.<CombinationOrder>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setTopics(TOPIC_NAME)
                .setGroupId(properties.getProperty("group.id"))
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new CombinationOrderDeserializationSchema())
                .setProperties(properties)
                .build();
        
        // 创建数据流并配置水印策略
        return env.fromSource(
                kafkaSource,
                OrderBookEventWatermarkStrategy.create(),
                "CombinationOrderSource"
        );
    }
    
    /**
     * 组合委托订单反序列化Schema
     */
    private static class CombinationOrderDeserializationSchema implements DeserializationSchema<CombinationOrder> {
        
        @Override
        public CombinationOrder deserialize(byte[] message) throws IOException {
            if (message == null || message.length == 0) {
                return null;
            }
            
            try {
                String json = new String(message, StandardCharsets.UTF_8);
                CombinationOrder order = JsonDeserializationUtils.deserializeCombinationOrder(json);
                
                if (order == null) {
                    LOG.warn("Failed to deserialize CombinationOrder from message: {}", json);
                    return null;
                }
                
                // 验证必要字段
                if (!isValidOrder(order)) {
                    LOG.warn("Invalid CombinationOrder received: {}", order);
                    return null;
                }
                
                return order;
            } catch (Exception e) {
                LOG.error("Error deserializing CombinationOrder message", e);
                return null;
            }
        }
        
        @Override
        public boolean isEndOfStream(CombinationOrder nextElement) {
            return false;
        }
        
        @Override
        public TypeInformation<CombinationOrder> getProducedType() {
            return TypeInformation.of(CombinationOrder.class);
        }
        
        /**
         * 验证组合订单数据的有效性
         */
        private boolean isValidOrder(CombinationOrder order) {
            return order.getOrderNumber() != null && !order.getOrderNumber().trim().isEmpty() &&
                   order.getCombinationContractCode() != null && !order.getCombinationContractCode().trim().isEmpty() &&
                   order.getLeg1ContractCode() != null && !order.getLeg1ContractCode().trim().isEmpty() &&
                   order.getLeg2ContractCode() != null && !order.getLeg2ContractCode().trim().isEmpty() &&
                   order.getOrderPrice() != null &&
                   order.getOrderVolume() != null &&
                   order.getBuySellFlag() != null &&
                   order.getOrderStatus() != null;
        }
    }
}
