package com.futures.system.source;

import com.futures.system.model.CombinationOrder;
import com.futures.system.model.SingleLegOrder;
import com.futures.system.model.TradeDetail;
import com.futures.system.utils.JsonDeserializationUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 文件数据源
 * 用于开发和测试，从本地文件读取JSON数据
 */
public class FileDataSource {
    
    private static final Logger LOG = LoggerFactory.getLogger(FileDataSource.class);
    
    /**
     * 从文件创建单腿委托数据流
     */
    public static DataStream<SingleLegOrder> createSingleLegOrderStreamFromFile(
            StreamExecutionEnvironment env, String filePath) {
        
        return env.readTextFile(filePath)
                .flatMap(new FlatMapFunction<String, SingleLegOrder>() {
                    @Override
                    public void flatMap(String line, Collector<SingleLegOrder> out) throws Exception {
                        if (line == null || line.trim().isEmpty()) {
                            return;
                        }
                        
                        try {
                            SingleLegOrder order = JsonDeserializationUtils.deserializeSingleLegOrder(line.trim());
                            if (order != null && isValidSingleLegOrder(order)) {
                                out.collect(order);
                            } else {
                                LOG.warn("Invalid SingleLegOrder in file: {}", line);
                            }
                        } catch (Exception e) {
                            LOG.error("Error parsing SingleLegOrder from line: {}", line, e);
                        }
                    }
                })
                .assignTimestampsAndWatermarks(OrderBookEventWatermarkStrategy.create())
                .name("SingleLegOrderFileSource");
    }
    
    /**
     * 从文件创建组合委托数据流
     */
    public static DataStream<CombinationOrder> createCombinationOrderStreamFromFile(
            StreamExecutionEnvironment env, String filePath) {
        
        return env.readTextFile(filePath)
                .flatMap(new FlatMapFunction<String, CombinationOrder>() {
                    @Override
                    public void flatMap(String line, Collector<CombinationOrder> out) throws Exception {
                        if (line == null || line.trim().isEmpty()) {
                            return;
                        }
                        
                        try {
                            CombinationOrder order = JsonDeserializationUtils.deserializeCombinationOrder(line.trim());
                            if (order != null && isValidCombinationOrder(order)) {
                                out.collect(order);
                            } else {
                                LOG.warn("Invalid CombinationOrder in file: {}", line);
                            }
                        } catch (Exception e) {
                            LOG.error("Error parsing CombinationOrder from line: {}", line, e);
                        }
                    }
                })
                .assignTimestampsAndWatermarks(OrderBookEventWatermarkStrategy.create())
                .name("CombinationOrderFileSource");
    }
    
    /**
     * 从文件创建成交明细数据流
     */
    public static DataStream<TradeDetail> createTradeDetailStreamFromFile(
            StreamExecutionEnvironment env, String filePath) {
        
        return env.readTextFile(filePath)
                .flatMap(new FlatMapFunction<String, TradeDetail>() {
                    @Override
                    public void flatMap(String line, Collector<TradeDetail> out) throws Exception {
                        if (line == null || line.trim().isEmpty()) {
                            return;
                        }
                        
                        try {
                            TradeDetail trade = JsonDeserializationUtils.deserializeTradeDetail(line.trim());
                            if (trade != null && isValidTradeDetail(trade)) {
                                out.collect(trade);
                            } else {
                                LOG.warn("Invalid TradeDetail in file: {}", line);
                            }
                        } catch (Exception e) {
                            LOG.error("Error parsing TradeDetail from line: {}", line, e);
                        }
                    }
                })
                .assignTimestampsAndWatermarks(OrderBookEventWatermarkStrategy.create())
                .name("TradeDetailFileSource");
    }
    
    /**
     * 从现有文件创建所有数据源（用于测试）
     */
    public static DataSourceFactory.DataSources createAllDataSourcesFromFiles(
            StreamExecutionEnvironment env) {
        
        // 使用项目根目录下的测试文件
        String singleLegFile = "singleleg_order_data_event.txt";
        String combinationFile = "cmb_order_data_event.txt";
        String tradeFile = "trade_data_event.txt";
        
        DataStream<SingleLegOrder> singleLegOrderStream = 
                createSingleLegOrderStreamFromFile(env, singleLegFile);
        
        DataStream<CombinationOrder> combinationOrderStream = 
                createCombinationOrderStreamFromFile(env, combinationFile);
        
        DataStream<TradeDetail> tradeDetailStream = 
                createTradeDetailStreamFromFile(env, tradeFile);
        
        return new DataSourceFactory.DataSources(
                singleLegOrderStream, combinationOrderStream, tradeDetailStream);
    }
    
    /**
     * 验证单腿订单有效性
     */
    private static boolean isValidSingleLegOrder(SingleLegOrder order) {
        return order.getOrderNumber() != null && !order.getOrderNumber().trim().isEmpty() &&
               order.getContractCode() != null && !order.getContractCode().trim().isEmpty() &&
               order.getOrderPrice() != null &&
               order.getOrderVolume() != null &&
               order.getBuySellFlag() != null &&
               order.getOrderStatus() != null;
    }
    
    /**
     * 验证组合订单有效性
     */
    private static boolean isValidCombinationOrder(CombinationOrder order) {
        return order.getOrderNumber() != null && !order.getOrderNumber().trim().isEmpty() &&
               order.getCombinationContractCode() != null && !order.getCombinationContractCode().trim().isEmpty() &&
               order.getLeg1ContractCode() != null && !order.getLeg1ContractCode().trim().isEmpty() &&
               order.getLeg2ContractCode() != null && !order.getLeg2ContractCode().trim().isEmpty() &&
               order.getOrderPrice() != null &&
               order.getOrderVolume() != null &&
               order.getBuySellFlag() != null &&
               order.getOrderStatus() != null;
    }
    
    /**
     * 验证成交明细有效性
     */
    private static boolean isValidTradeDetail(TradeDetail trade) {
        return trade.getTradeNumber() != null && !trade.getTradeNumber().trim().isEmpty() &&
               trade.getOrderNumber() != null && !trade.getOrderNumber().trim().isEmpty() &&
               trade.getContractCode() != null && !trade.getContractCode().trim().isEmpty() &&
               trade.getTradePrice() != null &&
               trade.getTradeVolume() != null &&
               trade.getBuySellFlag() != null &&
               trade.getTradeType() != null &&
               trade.getSettlementMemberCode() != null && !trade.getSettlementMemberCode().trim().isEmpty();
    }
}
