package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 订单簿事件基类
 * 所有订单簿相关事件的父类
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "eventType"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = SingleLegOrder.class, name = "SINGLE_LEG_ORDER"),
    @JsonSubTypes.Type(value = CombinationOrder.class, name = "COMBINATION_ORDER"),
    @JsonSubTypes.Type(value = TradeDetail.class, name = "TRADE_DETAIL")
})
public abstract class OrderBookEvent implements Serializable {
    
    /**
     * 全局序列号
     */
    @JsonProperty("_seq")
    private Long sequenceNumber;
    
    /**
     * 事件号
     */
    @JsonProperty("_eno")
    private Long eventNumber;
    
    /**
     * 交易日期
     */
    @JsonProperty("trd_dt")
    private LocalDate tradeDate;
    
    /**
     * 交易所编码
     */
    @JsonProperty("exchg_cde")
    private String exchangeCode;
    
    /**
     * 会员编码
     */
    @JsonProperty("memb_cde")
    private String memberCode;
    
    /**
     * 交易编码
     */
    @JsonProperty("trd_cde")
    private String tradeCode;
    
    /**
     * 期货公司统一编码
     */
    @JsonProperty("fcomp_unfy_cde")
    private String futuresCompanyCode;
    
    /**
     * 合约代码
     */
    @JsonProperty("contract_cde")
    private String contractCode;
    
    /**
     * 品种代码
     */
    @JsonProperty("comm_cde")
    private String commodityCode;
    
    /**
     * 数据时间
     */
    @JsonProperty("_dt")
    private LocalDateTime dataTime;

    // 构造函数
    public OrderBookEvent() {}

    public OrderBookEvent(Long sequenceNumber, Long eventNumber, LocalDate tradeDate, 
                         String exchangeCode, String memberCode, String tradeCode,
                         String futuresCompanyCode, String contractCode, String commodityCode) {
        this.sequenceNumber = sequenceNumber;
        this.eventNumber = eventNumber;
        this.tradeDate = tradeDate;
        this.exchangeCode = exchangeCode;
        this.memberCode = memberCode;
        this.tradeCode = tradeCode;
        this.futuresCompanyCode = futuresCompanyCode;
        this.contractCode = contractCode;
        this.commodityCode = commodityCode;
        this.dataTime = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(Long sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public Long getEventNumber() {
        return eventNumber;
    }

    public void setEventNumber(Long eventNumber) {
        this.eventNumber = eventNumber;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getExchangeCode() {
        return exchangeCode;
    }

    public void setExchangeCode(String exchangeCode) {
        this.exchangeCode = exchangeCode;
    }

    public String getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getFuturesCompanyCode() {
        return futuresCompanyCode;
    }

    public void setFuturesCompanyCode(String futuresCompanyCode) {
        this.futuresCompanyCode = futuresCompanyCode;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public LocalDateTime getDataTime() {
        return dataTime;
    }

    public void setDataTime(LocalDateTime dataTime) {
        this.dataTime = dataTime;
    }

    /**
     * 获取事件时间戳（用于Flink事件时间处理）
     */
    public abstract long getEventTimestamp();

    /**
     * 获取分区键（用于Flink KeyedStream分区）
     */
    public String getPartitionKey() {
        return contractCode;
    }

    @Override
    public String toString() {
        return "OrderBookEvent{" +
                "sequenceNumber=" + sequenceNumber +
                ", eventNumber=" + eventNumber +
                ", tradeDate=" + tradeDate +
                ", exchangeCode='" + exchangeCode + '\'' +
                ", memberCode='" + memberCode + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", commodityCode='" + commodityCode + '\'' +
                '}';
    }
}
