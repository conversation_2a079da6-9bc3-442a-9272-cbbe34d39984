package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 盈亏计算状态
 * 维护结算会员在某个合约上的持仓和盈亏状态
 */
public class PnLState implements Serializable {
    
    /**
     * 结算会员编码
     */
    private String settlementMemberCode;
    
    /**
     * 合约代码
     */
    private String contractCode;
    
    /**
     * 交易日期
     */
    private LocalDate tradeDate;
    
    /**
     * 多头持仓
     */
    private Position longPosition;
    
    /**
     * 空头持仓
     */
    private Position shortPosition;
    
    /**
     * 净持仓（多头-空头）
     */
    private BigDecimal netPosition;
    
    /**
     * 已实现盈亏
     */
    private BigDecimal realizedPnL;
    
    /**
     * 浮动盈亏
     */
    private BigDecimal unrealizedPnL;
    
    /**
     * 总盈亏
     */
    private BigDecimal totalPnL;
    
    /**
     * 最新成交价（用于浮动盈亏计算）
     */
    private BigDecimal latestTradePrice;
    
    /**
     * 成交记录（按成交编号索引，防重复处理）
     */
    private Map<String, TradeRecord> processedTrades;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 更新序列号
     */
    private long updateSequence;

    // 构造函数
    public PnLState(String settlementMemberCode, String contractCode, LocalDate tradeDate) {
        this.settlementMemberCode = settlementMemberCode;
        this.contractCode = contractCode;
        this.tradeDate = tradeDate;
        this.longPosition = new Position();
        this.shortPosition = new Position();
        this.netPosition = BigDecimal.ZERO;
        this.realizedPnL = BigDecimal.ZERO;
        this.unrealizedPnL = BigDecimal.ZERO;
        this.totalPnL = BigDecimal.ZERO;
        this.processedTrades = new ConcurrentHashMap<>();
        this.lastUpdateTime = LocalDateTime.now();
        this.updateSequence = 0;
    }

    /**
     * 处理成交
     */
    public synchronized boolean processTrade(TradeDetail trade) {
        if (trade == null || trade.getTradeNumber() == null) {
            return false;
        }
        
        // 检查是否已处理过此成交
        if (processedTrades.containsKey(trade.getTradeNumber())) {
            return false;
        }
        
        try {
            // 创建成交记录
            TradeRecord record = new TradeRecord(trade);
            processedTrades.put(trade.getTradeNumber(), record);
            
            // 更新持仓
            updatePosition(trade);
            
            // 更新最新成交价
            this.latestTradePrice = trade.getTradePrice();
            
            // 重新计算盈亏
            recalculatePnL();
            
            // 更新元数据
            this.lastUpdateTime = LocalDateTime.now();
            this.updateSequence++;
            
            return true;
        } catch (Exception e) {
            // 处理失败，移除已添加的成交记录
            processedTrades.remove(trade.getTradeNumber());
            return false;
        }
    }

    /**
     * 更新持仓
     */
    private void updatePosition(TradeDetail trade) {
        BigDecimal volume = trade.getTradeVolume();
        BigDecimal price = trade.getTradePrice();
        BuySellFlag side = trade.getBuySellFlag();
        OpenCloseType openCloseType = trade.getOpenCloseType();
        
        if (openCloseType == OpenCloseType.OPEN) {
            // 开仓
            if (side == BuySellFlag.BUY) {
                // 买开：增加多头持仓
                longPosition.addVolume(volume, price);
            } else {
                // 卖开：增加空头持仓
                shortPosition.addVolume(volume, price);
            }
        } else {
            // 平仓
            if (side == BuySellFlag.BUY) {
                // 买平：减少空头持仓，计算平仓盈亏
                BigDecimal closePnL = shortPosition.closeVolume(volume, price);
                realizedPnL = realizedPnL.add(closePnL);
            } else {
                // 卖平：减少多头持仓，计算平仓盈亏
                BigDecimal closePnL = longPosition.closeVolume(volume, price);
                realizedPnL = realizedPnL.add(closePnL);
            }
        }
        
        // 更新净持仓
        netPosition = longPosition.getVolume().subtract(shortPosition.getVolume());
    }

    /**
     * 重新计算盈亏
     */
    private void recalculatePnL() {
        if (latestTradePrice == null) {
            unrealizedPnL = BigDecimal.ZERO;
        } else {
            // 计算浮动盈亏
            BigDecimal longUnrealized = longPosition.calculateUnrealizedPnL(latestTradePrice);
            BigDecimal shortUnrealized = shortPosition.calculateUnrealizedPnL(latestTradePrice);
            unrealizedPnL = longUnrealized.add(shortUnrealized);
        }
        
        // 计算总盈亏
        totalPnL = realizedPnL.add(unrealizedPnL);
    }

    /**
     * 更新最新价格（用于重新计算浮动盈亏）
     */
    public synchronized void updateLatestPrice(BigDecimal latestPrice) {
        if (latestPrice != null && latestPrice.compareTo(BigDecimal.ZERO) > 0) {
            this.latestTradePrice = latestPrice;
            recalculatePnL();
            this.lastUpdateTime = LocalDateTime.now();
            this.updateSequence++;
        }
    }

    /**
     * 生成盈亏计算结果
     */
    public PnLCalculation toPnLCalculation() {
        PnLCalculation calculation = new PnLCalculation(settlementMemberCode, tradeDate, contractCode);
        
        calculation.setOpenVolume(longPosition.getVolume().add(shortPosition.getVolume()));
        calculation.setCloseVolume(getClosedVolume());
        calculation.setCurrentPosition(netPosition);
        calculation.setAverageOpenPrice(getWeightedAveragePrice());
        calculation.setLatestTradePrice(latestTradePrice);
        calculation.setRealizedPnL(realizedPnL);
        calculation.setUnrealizedPnL(unrealizedPnL);
        calculation.setTotalPnL(totalPnL);
        calculation.setOpenAmount(getOpenAmount());
        calculation.setCloseAmount(getCloseAmount());
        calculation.setNetPositionSide(netPosition);
        calculation.setCalculationTime(lastUpdateTime);
        calculation.setUpdateSequence(updateSequence);
        
        return calculation;
    }

    /**
     * 获取已平仓数量
     */
    private BigDecimal getClosedVolume() {
        return longPosition.getClosedVolume().add(shortPosition.getClosedVolume());
    }

    /**
     * 获取加权平均价格
     */
    private BigDecimal getWeightedAveragePrice() {
        BigDecimal totalAmount = longPosition.getTotalAmount().add(shortPosition.getTotalAmount());
        BigDecimal totalVolume = longPosition.getVolume().add(shortPosition.getVolume());
        
        if (totalVolume.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return totalAmount.divide(totalVolume, 6, RoundingMode.HALF_UP);
    }

    /**
     * 获取开仓金额
     */
    private BigDecimal getOpenAmount() {
        return longPosition.getTotalAmount().add(shortPosition.getTotalAmount());
    }

    /**
     * 获取平仓金额
     */
    private BigDecimal getCloseAmount() {
        return longPosition.getCloseAmount().add(shortPosition.getCloseAmount());
    }

    /**
     * 检查是否有持仓
     */
    public boolean hasPosition() {
        return netPosition.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 检查是否有活动（有成交或持仓）
     */
    public boolean hasActivity() {
        return !processedTrades.isEmpty() || hasPosition();
    }

    /**
     * 深拷贝盈亏状态
     */
    public PnLState deepCopy() {
        PnLState copy = new PnLState(this.settlementMemberCode, this.contractCode, this.tradeDate);
        copy.longPosition = this.longPosition.deepCopy();
        copy.shortPosition = this.shortPosition.deepCopy();
        copy.netPosition = this.netPosition;
        copy.realizedPnL = this.realizedPnL;
        copy.unrealizedPnL = this.unrealizedPnL;
        copy.totalPnL = this.totalPnL;
        copy.latestTradePrice = this.latestTradePrice;
        copy.processedTrades = new ConcurrentHashMap<>(this.processedTrades);
        copy.lastUpdateTime = this.lastUpdateTime;
        copy.updateSequence = this.updateSequence;
        return copy;
    }

    // Getters
    public String getSettlementMemberCode() { return settlementMemberCode; }
    public String getContractCode() { return contractCode; }
    public LocalDate getTradeDate() { return tradeDate; }
    public Position getLongPosition() { return longPosition; }
    public Position getShortPosition() { return shortPosition; }
    public BigDecimal getNetPosition() { return netPosition; }
    public BigDecimal getRealizedPnL() { return realizedPnL; }
    public BigDecimal getUnrealizedPnL() { return unrealizedPnL; }
    public BigDecimal getTotalPnL() { return totalPnL; }
    public BigDecimal getLatestTradePrice() { return latestTradePrice; }
    public Map<String, TradeRecord> getProcessedTrades() { return processedTrades; }
    public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
    public long getUpdateSequence() { return updateSequence; }

    @Override
    public String toString() {
        return String.format("PnLState{member=%s, contract=%s, netPos=%s, realizedPnL=%s, unrealizedPnL=%s, totalPnL=%s, trades=%d}",
                settlementMemberCode, contractCode, netPosition, realizedPnL, unrealizedPnL, totalPnL, processedTrades.size());
    }
}
