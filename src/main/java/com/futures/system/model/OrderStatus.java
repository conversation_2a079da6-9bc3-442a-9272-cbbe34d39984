package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 订单状态枚举
 * 对应数据字段 ord_sts
 */
public enum OrderStatus {
    /**
     * 0 = 全部成交
     */
    FULLY_FILLED("0", "全部成交"),
    
    /**
     * 1 = 部分成交还在队列中
     */
    PARTIALLY_FILLED_IN_QUEUE("1", "部分成交还在队列中"),
    
    /**
     * 2 = 部分成交不在队列中
     */
    PARTIALLY_FILLED_NOT_IN_QUEUE("2", "部分成交不在队列中"),
    
    /**
     * 3 = 未成交还在队列中
     */
    NOT_FILLED_IN_QUEUE("3", "未成交还在队列中"),
    
    /**
     * 4 = 未成交不在队列中
     */
    NOT_FILLED_NOT_IN_QUEUE("4", "未成交不在队列中"),
    
    /**
     * 5 = 撤单
     */
    CANCELLED("5", "撤单");

    private final String code;
    private final String description;

    OrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static OrderStatus fromCode(String code) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown order status code: " + code);
    }

    /**
     * 判断订单是否还在队列中（可以被成交）
     */
    public boolean isInQueue() {
        return this == PARTIALLY_FILLED_IN_QUEUE || this == NOT_FILLED_IN_QUEUE;
    }

    /**
     * 判断订单是否已经有成交
     */
    public boolean hasTraded() {
        return this == FULLY_FILLED || this == PARTIALLY_FILLED_IN_QUEUE || this == PARTIALLY_FILLED_NOT_IN_QUEUE;
    }

    /**
     * 判断订单是否已经完成（不会再有变化）
     */
    public boolean isFinished() {
        return this == FULLY_FILLED || this == CANCELLED || 
               this == PARTIALLY_FILLED_NOT_IN_QUEUE || this == NOT_FILLED_NOT_IN_QUEUE;
    }
}
