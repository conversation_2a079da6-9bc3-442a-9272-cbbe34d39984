package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 虚拟层订单簿
 * 在基础订单簿之上叠加组合委托，支持负值的虚拟价格档位
 */
public class VirtualOrderBook implements Serializable {
    
    /**
     * 基础订单簿
     */
    private BaseOrderBook baseOrderBook;
    
    /**
     * 虚拟买方价格档位（价格从高到低排序）
     */
    private TreeMap<BigDecimal, VirtualPriceLevel> virtualBidLevels;
    
    /**
     * 虚拟卖方价格档位（价格从低到高排序）
     */
    private TreeMap<BigDecimal, VirtualPriceLevel> virtualAskLevels;
    
    /**
     * 组合订单索引（按报单编号快速查找）
     */
    private Map<String, CombinationOrderInfo> combinationOrderIndex;
    
    /**
     * 腿合约最优价格缓存
     */
    private Map<String, ContractPriceInfo> legContractPrices;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 虚拟层版本号
     */
    private long virtualVersion;

    // 构造函数
    public VirtualOrderBook(BaseOrderBook baseOrderBook) {
        this.baseOrderBook = baseOrderBook;
        this.virtualBidLevels = new TreeMap<>(Collections.reverseOrder());
        this.virtualAskLevels = new TreeMap<>();
        this.combinationOrderIndex = new HashMap<>();
        this.legContractPrices = new HashMap<>();
        this.lastUpdateTime = LocalDateTime.now();
        this.virtualVersion = 0;
    }

    /**
     * 添加组合订单
     */
    public synchronized boolean addCombinationOrder(CombinationOrderInfo orderInfo) {
        if (orderInfo == null || orderInfo.getOrderNumber() == null) {
            return false;
        }
        
        // 检查订单是否已存在
        if (combinationOrderIndex.containsKey(orderInfo.getOrderNumber())) {
            return updateCombinationOrder(orderInfo);
        }
        
        // 只处理在队列中的订单
        if (!orderInfo.getOrderStatus().isInQueue()) {
            return false;
        }
        
        try {
            // 计算组合价格
            BigDecimal combinationPrice = calculateCombinationPrice(orderInfo);
            if (combinationPrice == null) {
                return false;
            }
            
            orderInfo.setCalculatedPrice(combinationPrice);
            
            // 添加到虚拟价格档位
            TreeMap<BigDecimal, VirtualPriceLevel> levels = 
                    orderInfo.getBuySellFlag() == BuySellFlag.BUY ? virtualBidLevels : virtualAskLevels;
            
            VirtualPriceLevel level = levels.computeIfAbsent(combinationPrice, 
                    k -> new VirtualPriceLevel(k, orderInfo.getBuySellFlag()));
            level.addCombinationOrder(orderInfo);
            
            // 添加到索引
            combinationOrderIndex.put(orderInfo.getOrderNumber(), orderInfo);
            
            updateVirtualVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新组合订单
     */
    public synchronized boolean updateCombinationOrder(CombinationOrderInfo updatedOrder) {
        if (updatedOrder == null || updatedOrder.getOrderNumber() == null) {
            return false;
        }
        
        CombinationOrderInfo existingOrder = combinationOrderIndex.get(updatedOrder.getOrderNumber());
        if (existingOrder == null) {
            return addCombinationOrder(updatedOrder);
        }
        
        try {
            // 重新计算组合价格
            BigDecimal newCombinationPrice = calculateCombinationPrice(updatedOrder);
            if (newCombinationPrice == null) {
                return removeCombinationOrder(updatedOrder.getOrderNumber());
            }
            
            updatedOrder.setCalculatedPrice(newCombinationPrice);
            
            // 如果价格改变，需要移动到新的价格档位
            if (existingOrder.getCalculatedPrice() == null || 
                !existingOrder.getCalculatedPrice().equals(newCombinationPrice)) {
                removeCombinationOrder(existingOrder.getOrderNumber());
                return addCombinationOrder(updatedOrder);
            }
            
            // 更新同一价格档位内的订单
            TreeMap<BigDecimal, VirtualPriceLevel> levels = 
                    updatedOrder.getBuySellFlag() == BuySellFlag.BUY ? virtualBidLevels : virtualAskLevels;
            VirtualPriceLevel level = levels.get(newCombinationPrice);
            
            if (level != null) {
                if (updatedOrder.getOrderStatus().isInQueue()) {
                    level.updateCombinationOrder(updatedOrder);
                    combinationOrderIndex.put(updatedOrder.getOrderNumber(), updatedOrder);
                } else {
                    removeCombinationOrder(updatedOrder.getOrderNumber());
                }
            }
            
            updateVirtualVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 移除组合订单
     */
    public synchronized boolean removeCombinationOrder(String orderNumber) {
        if (orderNumber == null) {
            return false;
        }
        
        CombinationOrderInfo order = combinationOrderIndex.remove(orderNumber);
        if (order == null) {
            return false;
        }
        
        try {
            // 从虚拟价格档位移除
            TreeMap<BigDecimal, VirtualPriceLevel> levels = 
                    order.getBuySellFlag() == BuySellFlag.BUY ? virtualBidLevels : virtualAskLevels;
            
            if (order.getCalculatedPrice() != null) {
                VirtualPriceLevel level = levels.get(order.getCalculatedPrice());
                if (level != null) {
                    level.removeCombinationOrder(orderNumber);
                    
                    // 如果虚拟价格档位为空，移除该档位
                    if (level.isEmpty()) {
                        levels.remove(order.getCalculatedPrice());
                    }
                }
            }
            
            updateVirtualVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新腿合约价格信息
     */
    public synchronized void updateLegContractPrice(String contractCode, BigDecimal bestBidPrice, BigDecimal bestAskPrice) {
        ContractPriceInfo priceInfo = legContractPrices.computeIfAbsent(contractCode, k -> new ContractPriceInfo(k));
        priceInfo.setBestBidPrice(bestBidPrice);
        priceInfo.setBestAskPrice(bestAskPrice);
        priceInfo.setLastUpdateTime(LocalDateTime.now());
        
        // 重新计算所有相关的组合订单价格
        recalculateAffectedCombinationOrders(contractCode);
    }

    /**
     * 计算组合价格
     */
    private BigDecimal calculateCombinationPrice(CombinationOrderInfo orderInfo) {
        String leg1Contract = orderInfo.getLeg1ContractCode();
        String leg2Contract = orderInfo.getLeg2ContractCode();
        
        ContractPriceInfo leg1Price = legContractPrices.get(leg1Contract);
        ContractPriceInfo leg2Price = legContractPrices.get(leg2Contract);
        
        if (leg1Price == null || leg2Price == null) {
            return null;
        }
        
        // 根据组合类型和买卖方向计算价格
        // 这里简化处理，实际应根据具体的组合策略计算
        if (orderInfo.getBuySellFlag() == BuySellFlag.BUY) {
            // 买入组合：使用卖价计算成本
            BigDecimal leg1Cost = leg1Price.getBestAskPrice();
            BigDecimal leg2Cost = leg2Price.getBestAskPrice();
            
            if (leg1Cost != null && leg2Cost != null) {
                // 简单的价差计算：leg1 - leg2
                return leg1Cost.subtract(leg2Cost);
            }
        } else {
            // 卖出组合：使用买价计算收益
            BigDecimal leg1Revenue = leg1Price.getBestBidPrice();
            BigDecimal leg2Revenue = leg2Price.getBestBidPrice();
            
            if (leg1Revenue != null && leg2Revenue != null) {
                // 简单的价差计算：leg1 - leg2
                return leg1Revenue.subtract(leg2Revenue);
            }
        }
        
        return null;
    }

    /**
     * 重新计算受影响的组合订单
     */
    private void recalculateAffectedCombinationOrders(String contractCode) {
        List<CombinationOrderInfo> affectedOrders = new ArrayList<>();
        
        for (CombinationOrderInfo order : combinationOrderIndex.values()) {
            if (contractCode.equals(order.getLeg1ContractCode()) || 
                contractCode.equals(order.getLeg2ContractCode())) {
                affectedOrders.add(order);
            }
        }
        
        for (CombinationOrderInfo order : affectedOrders) {
            updateCombinationOrder(order);
        }
    }

    /**
     * 生成合并的订单簿快照
     */
    public OrderBookSnapshot generateMergedSnapshot(int depth) {
        OrderBookSnapshot snapshot = new OrderBookSnapshot(baseOrderBook.getContractCode());
        
        // 设置基本信息
        snapshot.setSnapshotTime(LocalDateTime.now());
        snapshot.setSnapshotSequence(OrderBookSnapshotGenerator.getGlobalSnapshotSequence());
        snapshot.setHasVirtualLayer(true);
        
        // 合并买方档位
        mergeBidLevels(snapshot, depth);
        
        // 合并卖方档位
        mergeAskLevels(snapshot, depth);
        
        // 计算统计信息
        snapshot.calculateStatistics();
        
        return snapshot;
    }

    /**
     * 合并买方档位
     */
    private void mergeBidLevels(OrderBookSnapshot snapshot, int depth) {
        TreeMap<BigDecimal, PriceLevelSnapshot> mergedBids = new TreeMap<>(Collections.reverseOrder());
        
        // 添加基础层买方档位
        for (PriceLevel baseLevel : baseOrderBook.getTopBidLevels(depth * 2)) {
            PriceLevelSnapshot levelSnapshot = PriceLevelSnapshot.fromPriceLevel(baseLevel, BuySellFlag.BUY);
            mergedBids.put(baseLevel.getPrice(), levelSnapshot);
        }
        
        // 叠加虚拟层买方档位
        for (Map.Entry<BigDecimal, VirtualPriceLevel> entry : virtualBidLevels.entrySet()) {
            BigDecimal price = entry.getKey();
            VirtualPriceLevel virtualLevel = entry.getValue();
            
            PriceLevelSnapshot levelSnapshot = mergedBids.get(price);
            if (levelSnapshot == null) {
                levelSnapshot = new PriceLevelSnapshot(price, BuySellFlag.BUY);
                mergedBids.put(price, levelSnapshot);
            }
            
            // 添加虚拟数据
            levelSnapshot.addVirtualData(virtualLevel.getTotalVolume(), virtualLevel.getOrderCount());
        }
        
        // 取前N档
        mergedBids.values().stream()
                .limit(depth)
                .forEach(snapshot::addBidLevel);
    }

    /**
     * 合并卖方档位
     */
    private void mergeAskLevels(OrderBookSnapshot snapshot, int depth) {
        TreeMap<BigDecimal, PriceLevelSnapshot> mergedAsks = new TreeMap<>();
        
        // 添加基础层卖方档位
        for (PriceLevel baseLevel : baseOrderBook.getTopAskLevels(depth * 2)) {
            PriceLevelSnapshot levelSnapshot = PriceLevelSnapshot.fromPriceLevel(baseLevel, BuySellFlag.SELL);
            mergedAsks.put(baseLevel.getPrice(), levelSnapshot);
        }
        
        // 叠加虚拟层卖方档位
        for (Map.Entry<BigDecimal, VirtualPriceLevel> entry : virtualAskLevels.entrySet()) {
            BigDecimal price = entry.getKey();
            VirtualPriceLevel virtualLevel = entry.getValue();
            
            PriceLevelSnapshot levelSnapshot = mergedAsks.get(price);
            if (levelSnapshot == null) {
                levelSnapshot = new PriceLevelSnapshot(price, BuySellFlag.SELL);
                mergedAsks.put(price, levelSnapshot);
            }
            
            // 添加虚拟数据
            levelSnapshot.addVirtualData(virtualLevel.getTotalVolume(), virtualLevel.getOrderCount());
        }
        
        // 取前N档
        mergedAsks.values().stream()
                .limit(depth)
                .forEach(snapshot::addAskLevel);
    }

    /**
     * 更新虚拟版本号
     */
    private void updateVirtualVersion() {
        virtualVersion++;
        lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 检查虚拟层是否为空
     */
    public boolean isVirtualLayerEmpty() {
        return virtualBidLevels.isEmpty() && virtualAskLevels.isEmpty();
    }

    // Getters
    public BaseOrderBook getBaseOrderBook() { return baseOrderBook; }
    public TreeMap<BigDecimal, VirtualPriceLevel> getVirtualBidLevels() { return virtualBidLevels; }
    public TreeMap<BigDecimal, VirtualPriceLevel> getVirtualAskLevels() { return virtualAskLevels; }
    public Map<String, CombinationOrderInfo> getCombinationOrderIndex() { return combinationOrderIndex; }
    public Map<String, ContractPriceInfo> getLegContractPrices() { return legContractPrices; }
    public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
    public long getVirtualVersion() { return virtualVersion; }

    @Override
    public String toString() {
        return String.format("VirtualOrderBook{contract=%s, virtualBids=%d, virtualAsks=%d, combinations=%d, version=%d}",
                baseOrderBook.getContractCode(), virtualBidLevels.size(), virtualAskLevels.size(), 
                combinationOrderIndex.size(), virtualVersion);
    }
}
