package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单簿快照
 * 每0.5秒输出的完整订单簿状态
 */
public class OrderBookSnapshot implements Serializable {
    
    /**
     * 合约代码
     */
    @JsonProperty("contract_code")
    private String contractCode;
    
    /**
     * 快照时间
     */
    @JsonProperty("snapshot_time")
    private LocalDateTime snapshotTime;
    
    /**
     * 快照序列号
     */
    @JsonProperty("snapshot_sequence")
    private Long snapshotSequence;
    
    /**
     * 交易日期
     */
    @JsonProperty("trade_date")
    private String tradeDate;
    
    /**
     * 交易所编码
     */
    @JsonProperty("exchange_code")
    private String exchangeCode;
    
    /**
     * 品种代码
     */
    @JsonProperty("commodity_code")
    private String commodityCode;
    
    /**
     * 买方价格档位（按价格从高到低排序）
     */
    @JsonProperty("bid_levels")
    private List<PriceLevelSnapshot> bidLevels;
    
    /**
     * 卖方价格档位（按价格从低到高排序）
     */
    @JsonProperty("ask_levels")
    private List<PriceLevelSnapshot> askLevels;
    
    /**
     * 最优买价
     */
    @JsonProperty("best_bid_price")
    private BigDecimal bestBidPrice;
    
    /**
     * 最优买量
     */
    @JsonProperty("best_bid_volume")
    private BigDecimal bestBidVolume;
    
    /**
     * 最优卖价
     */
    @JsonProperty("best_ask_price")
    private BigDecimal bestAskPrice;
    
    /**
     * 最优卖量
     */
    @JsonProperty("best_ask_volume")
    private BigDecimal bestAskVolume;
    
    /**
     * 买方总订单数
     */
    @JsonProperty("total_bid_orders")
    private Integer totalBidOrders;
    
    /**
     * 卖方总订单数
     */
    @JsonProperty("total_ask_orders")
    private Integer totalAskOrders;
    
    /**
     * 买方总数量
     */
    @JsonProperty("total_bid_volume")
    private BigDecimal totalBidVolume;
    
    /**
     * 卖方总数量
     */
    @JsonProperty("total_ask_volume")
    private BigDecimal totalAskVolume;
    
    /**
     * 是否包含虚拟层数据（组合订单）
     */
    @JsonProperty("has_virtual_layer")
    private Boolean hasVirtualLayer;
    
    /**
     * 基础层买方档位数量
     */
    @JsonProperty("base_bid_levels_count")
    private Integer baseBidLevelsCount;
    
    /**
     * 基础层卖方档位数量
     */
    @JsonProperty("base_ask_levels_count")
    private Integer baseAskLevelsCount;

    // 构造函数
    public OrderBookSnapshot() {
        this.bidLevels = new ArrayList<>();
        this.askLevels = new ArrayList<>();
        this.snapshotTime = LocalDateTime.now();
        this.hasVirtualLayer = false;
        this.totalBidOrders = 0;
        this.totalAskOrders = 0;
        this.totalBidVolume = BigDecimal.ZERO;
        this.totalAskVolume = BigDecimal.ZERO;
    }

    public OrderBookSnapshot(String contractCode) {
        this();
        this.contractCode = contractCode;
    }

    /**
     * 添加买方价格档位
     */
    public void addBidLevel(PriceLevelSnapshot level) {
        bidLevels.add(level);
    }

    /**
     * 添加卖方价格档位
     */
    public void addAskLevel(PriceLevelSnapshot level) {
        askLevels.add(level);
    }

    /**
     * 获取买五档数据
     */
    public List<PriceLevelSnapshot> getTopBidLevels(int count) {
        return bidLevels.size() <= count ? bidLevels : bidLevels.subList(0, count);
    }

    /**
     * 获取卖五档数据
     */
    public List<PriceLevelSnapshot> getTopAskLevels(int count) {
        return askLevels.size() <= count ? askLevels : askLevels.subList(0, count);
    }

    /**
     * 更新最优买卖价信息
     */
    public void updateBestPrices() {
        if (!bidLevels.isEmpty()) {
            PriceLevelSnapshot bestBid = bidLevels.get(0);
            this.bestBidPrice = bestBid.getPrice();
            this.bestBidVolume = bestBid.getTotalVolume();
        } else {
            this.bestBidPrice = null;
            this.bestBidVolume = BigDecimal.ZERO;
        }

        if (!askLevels.isEmpty()) {
            PriceLevelSnapshot bestAsk = askLevels.get(0);
            this.bestAskPrice = bestAsk.getPrice();
            this.bestAskVolume = bestAsk.getTotalVolume();
        } else {
            this.bestAskPrice = null;
            this.bestAskVolume = BigDecimal.ZERO;
        }
    }

    /**
     * 计算统计信息
     */
    public void calculateStatistics() {
        // 计算买方统计
        totalBidOrders = bidLevels.stream().mapToInt(PriceLevelSnapshot::getOrderCount).sum();
        totalBidVolume = bidLevels.stream()
                .map(PriceLevelSnapshot::getTotalVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算卖方统计
        totalAskOrders = askLevels.stream().mapToInt(PriceLevelSnapshot::getOrderCount).sum();
        totalAskVolume = askLevels.stream()
                .map(PriceLevelSnapshot::getTotalVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 更新最优价格
        updateBestPrices();
    }

    // Getters and Setters
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public LocalDateTime getSnapshotTime() {
        return snapshotTime;
    }

    public void setSnapshotTime(LocalDateTime snapshotTime) {
        this.snapshotTime = snapshotTime;
    }

    public Long getSnapshotSequence() {
        return snapshotSequence;
    }

    public void setSnapshotSequence(Long snapshotSequence) {
        this.snapshotSequence = snapshotSequence;
    }

    public String getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(String tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getExchangeCode() {
        return exchangeCode;
    }

    public void setExchangeCode(String exchangeCode) {
        this.exchangeCode = exchangeCode;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public List<PriceLevelSnapshot> getBidLevels() {
        return bidLevels;
    }

    public void setBidLevels(List<PriceLevelSnapshot> bidLevels) {
        this.bidLevels = bidLevels;
    }

    public List<PriceLevelSnapshot> getAskLevels() {
        return askLevels;
    }

    public void setAskLevels(List<PriceLevelSnapshot> askLevels) {
        this.askLevels = askLevels;
    }

    public BigDecimal getBestBidPrice() {
        return bestBidPrice;
    }

    public void setBestBidPrice(BigDecimal bestBidPrice) {
        this.bestBidPrice = bestBidPrice;
    }

    public BigDecimal getBestBidVolume() {
        return bestBidVolume;
    }

    public void setBestBidVolume(BigDecimal bestBidVolume) {
        this.bestBidVolume = bestBidVolume;
    }

    public BigDecimal getBestAskPrice() {
        return bestAskPrice;
    }

    public void setBestAskPrice(BigDecimal bestAskPrice) {
        this.bestAskPrice = bestAskPrice;
    }

    public BigDecimal getBestAskVolume() {
        return bestAskVolume;
    }

    public void setBestAskVolume(BigDecimal bestAskVolume) {
        this.bestAskVolume = bestAskVolume;
    }

    public Integer getTotalBidOrders() {
        return totalBidOrders;
    }

    public void setTotalBidOrders(Integer totalBidOrders) {
        this.totalBidOrders = totalBidOrders;
    }

    public Integer getTotalAskOrders() {
        return totalAskOrders;
    }

    public void setTotalAskOrders(Integer totalAskOrders) {
        this.totalAskOrders = totalAskOrders;
    }

    public BigDecimal getTotalBidVolume() {
        return totalBidVolume;
    }

    public void setTotalBidVolume(BigDecimal totalBidVolume) {
        this.totalBidVolume = totalBidVolume;
    }

    public BigDecimal getTotalAskVolume() {
        return totalAskVolume;
    }

    public void setTotalAskVolume(BigDecimal totalAskVolume) {
        this.totalAskVolume = totalAskVolume;
    }

    public Boolean getHasVirtualLayer() {
        return hasVirtualLayer;
    }

    public void setHasVirtualLayer(Boolean hasVirtualLayer) {
        this.hasVirtualLayer = hasVirtualLayer;
    }

    public Integer getBaseBidLevelsCount() {
        return baseBidLevelsCount;
    }

    public void setBaseBidLevelsCount(Integer baseBidLevelsCount) {
        this.baseBidLevelsCount = baseBidLevelsCount;
    }

    public Integer getBaseAskLevelsCount() {
        return baseAskLevelsCount;
    }

    public void setBaseAskLevelsCount(Integer baseAskLevelsCount) {
        this.baseAskLevelsCount = baseAskLevelsCount;
    }

    @Override
    public String toString() {
        return "OrderBookSnapshot{" +
                "contractCode='" + contractCode + '\'' +
                ", snapshotTime=" + snapshotTime +
                ", bestBidPrice=" + bestBidPrice +
                ", bestBidVolume=" + bestBidVolume +
                ", bestAskPrice=" + bestAskPrice +
                ", bestAskVolume=" + bestAskVolume +
                ", totalBidOrders=" + totalBidOrders +
                ", totalAskOrders=" + totalAskOrders +
                ", hasVirtualLayer=" + hasVirtualLayer +
                '}';
    }
}
