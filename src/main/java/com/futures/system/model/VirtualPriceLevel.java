package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 虚拟价格档位
 * 表示组合委托在某个价格的虚拟订单集合，支持负值
 */
public class VirtualPriceLevel implements Serializable, Comparable<VirtualPriceLevel> {
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 总数量（可能为负值）
     */
    private BigDecimal totalVolume;
    
    /**
     * 订单数量
     */
    private int orderCount;
    
    /**
     * 买卖方向
     */
    private BuySellFlag side;
    
    /**
     * 该价格档位的组合订单队列（按时间优先级排序）
     */
    private LinkedList<CombinationOrderInfo> orders;

    public VirtualPriceLevel(BigDecimal price, BuySellFlag side) {
        this.price = price;
        this.side = side;
        this.totalVolume = BigDecimal.ZERO;
        this.orderCount = 0;
        this.orders = new LinkedList<>();
    }

    /**
     * 添加组合订单到虚拟价格档位
     */
    public void addCombinationOrder(CombinationOrderInfo order) {
        orders.add(order);
        
        // 根据买卖方向计算数量贡献
        BigDecimal volumeContribution = calculateVolumeContribution(order);
        totalVolume = totalVolume.add(volumeContribution);
        orderCount++;
    }

    /**
     * 从虚拟价格档位移除组合订单
     */
    public boolean removeCombinationOrder(String orderNumber) {
        for (int i = 0; i < orders.size(); i++) {
            CombinationOrderInfo order = orders.get(i);
            if (order.getOrderNumber().equals(orderNumber)) {
                orders.remove(i);
                
                // 减去数量贡献
                BigDecimal volumeContribution = calculateVolumeContribution(order);
                totalVolume = totalVolume.subtract(volumeContribution);
                orderCount--;
                
                return true;
            }
        }
        return false;
    }

    /**
     * 更新组合订单
     */
    public boolean updateCombinationOrder(CombinationOrderInfo updatedOrder) {
        for (int i = 0; i < orders.size(); i++) {
            CombinationOrderInfo order = orders.get(i);
            if (order.getOrderNumber().equals(updatedOrder.getOrderNumber())) {
                // 计算数量变化
                BigDecimal oldContribution = calculateVolumeContribution(order);
                BigDecimal newContribution = calculateVolumeContribution(updatedOrder);
                
                // 更新订单
                orders.set(i, updatedOrder);
                
                // 更新总数量
                totalVolume = totalVolume.subtract(oldContribution).add(newContribution);
                
                // 如果剩余量为0，移除订单
                if (updatedOrder.getRemainingVolume().compareTo(BigDecimal.ZERO) <= 0) {
                    removeCombinationOrder(updatedOrder.getOrderNumber());
                }
                
                return true;
            }
        }
        return false;
    }

    /**
     * 计算订单对总数量的贡献
     * 组合订单可能产生负贡献，这是正常的业务场景
     */
    private BigDecimal calculateVolumeContribution(CombinationOrderInfo order) {
        BigDecimal volume = order.getRemainingVolume();
        
        // 根据组合策略和买卖方向计算贡献
        // 这里简化处理，实际应根据具体的组合类型计算
        
        if (order.getBuySellFlag() == BuySellFlag.BUY) {
            // 买入组合订单：正贡献
            return volume;
        } else {
            // 卖出组合订单：可能产生负贡献（套利场景）
            return volume.negate();
        }
    }

    /**
     * 检查虚拟价格档位是否为空
     */
    public boolean isEmpty() {
        return orders.isEmpty();
    }

    /**
     * 获取第一个订单（时间优先级最高）
     */
    public CombinationOrderInfo getFirstOrder() {
        return orders.isEmpty() ? null : orders.getFirst();
    }

    /**
     * 清空虚拟价格档位
     */
    public void clear() {
        orders.clear();
        totalVolume = BigDecimal.ZERO;
        orderCount = 0;
    }

    /**
     * 深拷贝虚拟价格档位
     */
    public VirtualPriceLevel deepCopy() {
        VirtualPriceLevel copy = new VirtualPriceLevel(this.price, this.side);
        copy.totalVolume = this.totalVolume;
        copy.orderCount = this.orderCount;
        
        for (CombinationOrderInfo order : this.orders) {
            copy.orders.add(order.deepCopy());
        }
        
        return copy;
    }

    /**
     * 检查是否包含负值
     */
    public boolean hasNegativeVolume() {
        return totalVolume.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 获取绝对数量
     */
    public BigDecimal getAbsoluteTotalVolume() {
        return totalVolume.abs();
    }

    /**
     * 检查价格档位是否有效
     */
    public boolean isValid() {
        return !orders.isEmpty() && orderCount > 0;
    }

    @Override
    public int compareTo(VirtualPriceLevel other) {
        return this.price.compareTo(other.price);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VirtualPriceLevel that = (VirtualPriceLevel) o;
        return Objects.equals(price, that.price) && side == that.side;
    }

    @Override
    public int hashCode() {
        return Objects.hash(price, side);
    }

    // Getters and Setters
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public BuySellFlag getSide() {
        return side;
    }

    public void setSide(BuySellFlag side) {
        this.side = side;
    }

    public LinkedList<CombinationOrderInfo> getOrders() {
        return orders;
    }

    public void setOrders(LinkedList<CombinationOrderInfo> orders) {
        this.orders = orders;
    }

    @Override
    public String toString() {
        return String.format("VirtualPriceLevel{price=%s, totalVolume=%s, orderCount=%d, side=%s, hasNegative=%s}",
                price, totalVolume, orderCount, side, hasNegativeVolume());
    }
}
