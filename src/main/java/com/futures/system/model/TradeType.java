package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 成交类型枚举
 * 对应数据字段 trd_type
 */
public enum TradeType {
    /**
     * 0 = 普通成交
     */
    NORMAL("0", "普通成交"),
    
    /**
     * 1 = 期转现衍生成交
     */
    EFP_DERIVED("1", "期转现衍生成交"),
    
    /**
     * 2 = 组合套利衍生成交
     */
    COMBINATION_ARBITRAGE_DERIVED("2", "组合套利衍生成交");

    private final String code;
    private final String description;

    TradeType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static TradeType fromCode(String code) {
        for (TradeType type : TradeType.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown trade type code: " + code);
    }
}
