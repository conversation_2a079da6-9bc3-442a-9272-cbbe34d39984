package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.futures.system.utils.LocalTimeDeserializer;
import com.futures.system.utils.LocalTimeSerializer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;

/**
 * 单腿委托订单
 * 对应数据表 dwd_trd_snglleg_ord_dtl_rt
 */
public class SingleLegOrder extends OrderBookEvent {
    
    /**
     * 报单编号
     */
    @JsonProperty("ord_nbr")
    private String orderNumber;
    
    /**
     * 统一开户账号
     */
    @JsonProperty("unfy_opnacct_cde")
    private String unifiedAccountCode;
    
    /**
     * 结算会员会员编码
     */
    @JsonProperty("settle_memb_memb_cde")
    private String settlementMemberCode;
    
    /**
     * 高频委托标志
     */
    @JsonProperty("highfrequ_ord_tag")
    private String highFrequencyOrderFlag;
    
    /**
     * 报单类型
     */
    @JsonProperty("ord_type")
    private OrderType orderType;
    
    /**
     * 报单状态
     */
    @JsonProperty("ord_sts")
    private OrderStatus orderStatus;
    
    /**
     * 报单来源
     */
    @JsonProperty("ord_src")
    private String orderSource;
    
    /**
     * 成交量类型
     */
    @JsonProperty("trd_vol_type")
    private String tradeVolumeType;
    
    /**
     * 报单价格条件
     */
    @JsonProperty("ord_prc_cndt")
    private String orderPriceCondition;
    
    /**
     * 有效期类型
     */
    @JsonProperty("vldprd_type")
    private String validPeriodType;
    
    /**
     * 最终修改席位号
     */
    @JsonProperty("final_alt_seat_nbr")
    private String finalAlterSeatNumber;
    
    /**
     * 席位号
     */
    @JsonProperty("seat_nbr")
    private String seatNumber;
    
    /**
     * 剩余量
     */
    @JsonProperty("rmn_vol")
    private BigDecimal remainingVolume;
    
    /**
     * 成交量
     */
    @JsonProperty("trd_vol")
    private BigDecimal tradedVolume;
    
    /**
     * 最小成交量
     */
    @JsonProperty("min_trd_vol")
    private BigDecimal minimumTradeVolume;
    
    /**
     * 委托量
     */
    @JsonProperty("ord_vol")
    private BigDecimal orderVolume;
    
    /**
     * 触发价格
     */
    @JsonProperty("trig_prc")
    private BigDecimal triggerPrice;
    
    /**
     * 委托价格
     */
    @JsonProperty("ord_prc")
    private BigDecimal orderPrice;
    
    /**
     * 最近更新时间微秒
     */
    @JsonProperty("rcnt_renew_tm_microsec")
    private Long recentRenewTimeMicrosec;
    
    /**
     * 最近更新时间毫秒
     */
    @JsonProperty("rcnt_renew_tm_millisec")
    private Long recentRenewTimeMillisec;
    
    /**
     * 最终修改时间
     */
    @JsonProperty("final_alt_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime finalAlterTime;
    
    /**
     * 报单时间微秒
     */
    @JsonProperty("ord_tm_microsec")
    private Long orderTimeMicrosec;
    
    /**
     * 报单时间毫秒
     */
    @JsonProperty("ord_tm_millisec")
    private Long orderTimeMillisec;
    
    /**
     * 报单时间
     */
    @JsonProperty("ord_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime orderTime;
    
    /**
     * 撤销时间
     */
    @JsonProperty("repel_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime cancelTime;
    
    /**
     * 挂起时间
     */
    @JsonProperty("susp_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime suspendTime;
    
    /**
     * 激活时间
     */
    @JsonProperty("actv_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime activeTime;
    
    /**
     * 强平原因
     */
    @JsonProperty("forcclos_resn")
    private String forceCloseReason;
    
    /**
     * 触发条件
     */
    @JsonProperty("trig_cndt")
    private String triggerCondition;
    
    /**
     * 投机套保标志
     */
    @JsonProperty("specu_hedg_tag")
    private String speculationHedgeFlag;
    
    /**
     * 开平仓类型
     */
    @JsonProperty("ocpos_type")
    private OpenCloseType openCloseType;
    
    /**
     * 买卖标志
     */
    @JsonProperty("b_s_tag")
    private BuySellFlag buySellFlag;
    
    /**
     * 看涨看跌标志
     */
    @JsonProperty("c_p_tag")
    private String callPutFlag;
    
    /**
     * 执行价格
     */
    @JsonProperty("exec_prc")
    private BigDecimal executePrice;
    
    /**
     * 本地报单编号
     */
    @JsonProperty("local_ord_nbr")
    private String localOrderNumber;
    
    /**
     * 发生日期
     */
    @JsonProperty("ocr_dt")
    private LocalDate occurDate;
    
    /**
     * 撤销时间微秒
     */
    @JsonProperty("repel_tm_microsec")
    private Long cancelTimeMicrosec;
    
    /**
     * 撤销时间毫秒
     */
    @JsonProperty("repel_tm_millisec")
    private Long cancelTimeMillisec;

    // 构造函数
    public SingleLegOrder() {
        super();
    }

    @Override
    public long getEventTimestamp() {
        if (orderTime != null && getTradeDate() != null) {
            return getTradeDate().atTime(orderTime).toInstant(ZoneOffset.UTC).toEpochMilli();
        }
        return System.currentTimeMillis();
    }

    // Getters and Setters
    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getUnifiedAccountCode() {
        return unifiedAccountCode;
    }

    public void setUnifiedAccountCode(String unifiedAccountCode) {
        this.unifiedAccountCode = unifiedAccountCode;
    }

    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public String getHighFrequencyOrderFlag() {
        return highFrequencyOrderFlag;
    }

    public void setHighFrequencyOrderFlag(String highFrequencyOrderFlag) {
        this.highFrequencyOrderFlag = highFrequencyOrderFlag;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public OrderStatus getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(OrderStatus orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getTradeVolumeType() {
        return tradeVolumeType;
    }

    public void setTradeVolumeType(String tradeVolumeType) {
        this.tradeVolumeType = tradeVolumeType;
    }

    public String getOrderPriceCondition() {
        return orderPriceCondition;
    }

    public void setOrderPriceCondition(String orderPriceCondition) {
        this.orderPriceCondition = orderPriceCondition;
    }

    public String getValidPeriodType() {
        return validPeriodType;
    }

    public void setValidPeriodType(String validPeriodType) {
        this.validPeriodType = validPeriodType;
    }

    public String getFinalAlterSeatNumber() {
        return finalAlterSeatNumber;
    }

    public void setFinalAlterSeatNumber(String finalAlterSeatNumber) {
        this.finalAlterSeatNumber = finalAlterSeatNumber;
    }

    public String getSeatNumber() {
        return seatNumber;
    }

    public void setSeatNumber(String seatNumber) {
        this.seatNumber = seatNumber;
    }

    public BigDecimal getRemainingVolume() {
        return remainingVolume;
    }

    public void setRemainingVolume(BigDecimal remainingVolume) {
        this.remainingVolume = remainingVolume;
    }

    public BigDecimal getTradedVolume() {
        return tradedVolume;
    }

    public void setTradedVolume(BigDecimal tradedVolume) {
        this.tradedVolume = tradedVolume;
    }

    public BigDecimal getMinimumTradeVolume() {
        return minimumTradeVolume;
    }

    public void setMinimumTradeVolume(BigDecimal minimumTradeVolume) {
        this.minimumTradeVolume = minimumTradeVolume;
    }

    public BigDecimal getOrderVolume() {
        return orderVolume;
    }

    public void setOrderVolume(BigDecimal orderVolume) {
        this.orderVolume = orderVolume;
    }

    public BigDecimal getTriggerPrice() {
        return triggerPrice;
    }

    public void setTriggerPrice(BigDecimal triggerPrice) {
        this.triggerPrice = triggerPrice;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Long getRecentRenewTimeMicrosec() {
        return recentRenewTimeMicrosec;
    }

    public void setRecentRenewTimeMicrosec(Long recentRenewTimeMicrosec) {
        this.recentRenewTimeMicrosec = recentRenewTimeMicrosec;
    }

    public Long getRecentRenewTimeMillisec() {
        return recentRenewTimeMillisec;
    }

    public void setRecentRenewTimeMillisec(Long recentRenewTimeMillisec) {
        this.recentRenewTimeMillisec = recentRenewTimeMillisec;
    }

    public LocalTime getFinalAlterTime() {
        return finalAlterTime;
    }

    public void setFinalAlterTime(LocalTime finalAlterTime) {
        this.finalAlterTime = finalAlterTime;
    }

    public Long getOrderTimeMicrosec() {
        return orderTimeMicrosec;
    }

    public void setOrderTimeMicrosec(Long orderTimeMicrosec) {
        this.orderTimeMicrosec = orderTimeMicrosec;
    }

    public Long getOrderTimeMillisec() {
        return orderTimeMillisec;
    }

    public void setOrderTimeMillisec(Long orderTimeMillisec) {
        this.orderTimeMillisec = orderTimeMillisec;
    }

    public LocalTime getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(LocalTime orderTime) {
        this.orderTime = orderTime;
    }

    public LocalTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public LocalTime getSuspendTime() {
        return suspendTime;
    }

    public void setSuspendTime(LocalTime suspendTime) {
        this.suspendTime = suspendTime;
    }

    public LocalTime getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(LocalTime activeTime) {
        this.activeTime = activeTime;
    }

    public String getForceCloseReason() {
        return forceCloseReason;
    }

    public void setForceCloseReason(String forceCloseReason) {
        this.forceCloseReason = forceCloseReason;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public String getSpeculationHedgeFlag() {
        return speculationHedgeFlag;
    }

    public void setSpeculationHedgeFlag(String speculationHedgeFlag) {
        this.speculationHedgeFlag = speculationHedgeFlag;
    }

    public OpenCloseType getOpenCloseType() {
        return openCloseType;
    }

    public void setOpenCloseType(OpenCloseType openCloseType) {
        this.openCloseType = openCloseType;
    }

    public BuySellFlag getBuySellFlag() {
        return buySellFlag;
    }

    public void setBuySellFlag(BuySellFlag buySellFlag) {
        this.buySellFlag = buySellFlag;
    }

    public String getCallPutFlag() {
        return callPutFlag;
    }

    public void setCallPutFlag(String callPutFlag) {
        this.callPutFlag = callPutFlag;
    }

    public BigDecimal getExecutePrice() {
        return executePrice;
    }

    public void setExecutePrice(BigDecimal executePrice) {
        this.executePrice = executePrice;
    }

    public String getLocalOrderNumber() {
        return localOrderNumber;
    }

    public void setLocalOrderNumber(String localOrderNumber) {
        this.localOrderNumber = localOrderNumber;
    }

    public LocalDate getOccurDate() {
        return occurDate;
    }

    public void setOccurDate(LocalDate occurDate) {
        this.occurDate = occurDate;
    }

    public Long getCancelTimeMicrosec() {
        return cancelTimeMicrosec;
    }

    public void setCancelTimeMicrosec(Long cancelTimeMicrosec) {
        this.cancelTimeMicrosec = cancelTimeMicrosec;
    }

    public Long getCancelTimeMillisec() {
        return cancelTimeMillisec;
    }

    public void setCancelTimeMillisec(Long cancelTimeMillisec) {
        this.cancelTimeMillisec = cancelTimeMillisec;
    }

    @Override
    public String toString() {
        return "SingleLegOrder{" +
                "orderNumber='" + orderNumber + '\'' +
                ", contractCode='" + getContractCode() + '\'' +
                ", orderPrice=" + orderPrice +
                ", orderVolume=" + orderVolume +
                ", remainingVolume=" + remainingVolume +
                ", orderStatus=" + orderStatus +
                ", buySellFlag=" + buySellFlag +
                ", orderTime=" + orderTime +
                '}';
    }
}
