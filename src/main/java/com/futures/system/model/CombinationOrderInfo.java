package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 组合订单信息
 * 用于虚拟层订单簿中存储组合订单的关键信息
 */
public class CombinationOrderInfo implements Serializable {
    
    /**
     * 报单编号
     */
    private String orderNumber;
    
    /**
     * 组合合约代码
     */
    private String combinationContractCode;
    
    /**
     * 腿1合约代码
     */
    private String leg1ContractCode;
    
    /**
     * 腿2合约代码
     */
    private String leg2ContractCode;
    
    /**
     * 委托价格（原始价格）
     */
    private BigDecimal orderPrice;
    
    /**
     * 计算后的组合价格
     */
    private BigDecimal calculatedPrice;
    
    /**
     * 委托数量
     */
    private BigDecimal orderVolume;
    
    /**
     * 剩余数量
     */
    private BigDecimal remainingVolume;
    
    /**
     * 已成交数量
     */
    private BigDecimal tradedVolume;
    
    /**
     * 买卖标志
     */
    private BuySellFlag buySellFlag;
    
    /**
     * 订单状态
     */
    private OrderStatus orderStatus;
    
    /**
     * 订单类型
     */
    private OrderType orderType;
    
    /**
     * 开平仓类型
     */
    private OpenCloseType openCloseType;
    
    /**
     * 报单时间
     */
    private LocalTime orderTime;
    
    /**
     * 会员编码
     */
    private String memberCode;
    
    /**
     * 交易编码
     */
    private String tradeCode;
    
    /**
     * 结算会员编码
     */
    private String settlementMemberCode;
    
    /**
     * 组合策略类型
     */
    private CombinationStrategy strategy;
    
    /**
     * 腿1比例
     */
    private BigDecimal leg1Ratio;
    
    /**
     * 腿2比例
     */
    private BigDecimal leg2Ratio;

    // 构造函数
    public CombinationOrderInfo() {
        this.tradedVolume = BigDecimal.ZERO;
        this.leg1Ratio = BigDecimal.ONE;
        this.leg2Ratio = BigDecimal.ONE;
        this.strategy = CombinationStrategy.SPREAD;
    }

    public CombinationOrderInfo(String orderNumber, String combinationContractCode, 
                               String leg1ContractCode, String leg2ContractCode,
                               BigDecimal orderPrice, BigDecimal orderVolume, 
                               BuySellFlag buySellFlag, LocalTime orderTime) {
        this();
        this.orderNumber = orderNumber;
        this.combinationContractCode = combinationContractCode;
        this.leg1ContractCode = leg1ContractCode;
        this.leg2ContractCode = leg2ContractCode;
        this.orderPrice = orderPrice;
        this.orderVolume = orderVolume;
        this.remainingVolume = orderVolume;
        this.buySellFlag = buySellFlag;
        this.orderTime = orderTime;
        this.orderStatus = OrderStatus.NOT_FILLED_IN_QUEUE;
    }

    /**
     * 从组合委托订单创建CombinationOrderInfo
     */
    public static CombinationOrderInfo fromCombinationOrder(CombinationOrder order) {
        CombinationOrderInfo orderInfo = new CombinationOrderInfo();
        orderInfo.orderNumber = order.getOrderNumber();
        orderInfo.combinationContractCode = order.getCombinationContractCode();
        orderInfo.leg1ContractCode = order.getLeg1ContractCode();
        orderInfo.leg2ContractCode = order.getLeg2ContractCode();
        orderInfo.orderPrice = order.getOrderPrice();
        orderInfo.orderVolume = order.getOrderVolume();
        orderInfo.remainingVolume = order.getRemainingVolume();
        orderInfo.tradedVolume = order.getTradedVolume();
        orderInfo.buySellFlag = order.getBuySellFlag();
        orderInfo.orderStatus = order.getOrderStatus();
        orderInfo.orderType = order.getOrderType();
        orderInfo.openCloseType = order.getOpenCloseType();
        orderInfo.orderTime = order.getOrderTime();
        orderInfo.memberCode = order.getMemberCode();
        orderInfo.tradeCode = order.getTradeCode();
        orderInfo.settlementMemberCode = order.getSettlementMemberCode();
        
        // 解析组合策略（简化处理）
        orderInfo.strategy = parseStrategy(order.getCombinationContractCode());
        
        return orderInfo;
    }

    /**
     * 解析组合策略
     */
    private static CombinationStrategy parseStrategy(String combinationContractCode) {
        if (combinationContractCode == null) {
            return CombinationStrategy.SPREAD;
        }
        
        // 根据合约代码解析策略类型（简化实现）
        if (combinationContractCode.contains("SP")) {
            return CombinationStrategy.SPREAD;
        } else if (combinationContractCode.contains("STR")) {
            return CombinationStrategy.STRANGLE;
        } else if (combinationContractCode.contains("STD")) {
            return CombinationStrategy.STRADDLE;
        }
        
        return CombinationStrategy.SPREAD;
    }

    /**
     * 更新订单状态和数量
     */
    public void updateFromTrade(BigDecimal tradeVolume) {
        this.tradedVolume = this.tradedVolume.add(tradeVolume);
        this.remainingVolume = this.orderVolume.subtract(this.tradedVolume);
        
        if (this.remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
            this.orderStatus = OrderStatus.FULLY_FILLED;
            this.remainingVolume = BigDecimal.ZERO;
        } else {
            this.orderStatus = OrderStatus.PARTIALLY_FILLED_IN_QUEUE;
        }
    }

    /**
     * 撤销订单
     */
    public void cancel() {
        this.orderStatus = OrderStatus.CANCELLED;
        this.remainingVolume = BigDecimal.ZERO;
    }

    /**
     * 计算组合订单的虚拟影响
     * 返回对基础订单簿的虚拟影响量
     */
    public BigDecimal calculateVirtualImpact() {
        if (!orderStatus.isInQueue()) {
            return BigDecimal.ZERO;
        }
        
        // 根据组合策略和买卖方向计算影响
        BigDecimal impact = remainingVolume;
        
        if (strategy == CombinationStrategy.SPREAD) {
            // 价差策略：可能产生负影响
            if (buySellFlag == BuySellFlag.SELL) {
                impact = impact.negate();
            }
        }
        
        return impact;
    }

    /**
     * 检查是否需要重新计算价格
     */
    public boolean needsPriceRecalculation(BigDecimal newCalculatedPrice) {
        return calculatedPrice == null || !calculatedPrice.equals(newCalculatedPrice);
    }

    /**
     * 深拷贝组合订单信息
     */
    public CombinationOrderInfo deepCopy() {
        CombinationOrderInfo copy = new CombinationOrderInfo();
        copy.orderNumber = this.orderNumber;
        copy.combinationContractCode = this.combinationContractCode;
        copy.leg1ContractCode = this.leg1ContractCode;
        copy.leg2ContractCode = this.leg2ContractCode;
        copy.orderPrice = this.orderPrice;
        copy.calculatedPrice = this.calculatedPrice;
        copy.orderVolume = this.orderVolume;
        copy.remainingVolume = this.remainingVolume;
        copy.tradedVolume = this.tradedVolume;
        copy.buySellFlag = this.buySellFlag;
        copy.orderStatus = this.orderStatus;
        copy.orderType = this.orderType;
        copy.openCloseType = this.openCloseType;
        copy.orderTime = this.orderTime;
        copy.memberCode = this.memberCode;
        copy.tradeCode = this.tradeCode;
        copy.settlementMemberCode = this.settlementMemberCode;
        copy.strategy = this.strategy;
        copy.leg1Ratio = this.leg1Ratio;
        copy.leg2Ratio = this.leg2Ratio;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CombinationOrderInfo that = (CombinationOrderInfo) o;
        return Objects.equals(orderNumber, that.orderNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderNumber);
    }

    // Getters and Setters
    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }

    public String getCombinationContractCode() { return combinationContractCode; }
    public void setCombinationContractCode(String combinationContractCode) { this.combinationContractCode = combinationContractCode; }

    public String getLeg1ContractCode() { return leg1ContractCode; }
    public void setLeg1ContractCode(String leg1ContractCode) { this.leg1ContractCode = leg1ContractCode; }

    public String getLeg2ContractCode() { return leg2ContractCode; }
    public void setLeg2ContractCode(String leg2ContractCode) { this.leg2ContractCode = leg2ContractCode; }

    public BigDecimal getOrderPrice() { return orderPrice; }
    public void setOrderPrice(BigDecimal orderPrice) { this.orderPrice = orderPrice; }

    public BigDecimal getCalculatedPrice() { return calculatedPrice; }
    public void setCalculatedPrice(BigDecimal calculatedPrice) { this.calculatedPrice = calculatedPrice; }

    public BigDecimal getOrderVolume() { return orderVolume; }
    public void setOrderVolume(BigDecimal orderVolume) { this.orderVolume = orderVolume; }

    public BigDecimal getRemainingVolume() { return remainingVolume; }
    public void setRemainingVolume(BigDecimal remainingVolume) { this.remainingVolume = remainingVolume; }

    public BigDecimal getTradedVolume() { return tradedVolume; }
    public void setTradedVolume(BigDecimal tradedVolume) { this.tradedVolume = tradedVolume; }

    public BuySellFlag getBuySellFlag() { return buySellFlag; }
    public void setBuySellFlag(BuySellFlag buySellFlag) { this.buySellFlag = buySellFlag; }

    public OrderStatus getOrderStatus() { return orderStatus; }
    public void setOrderStatus(OrderStatus orderStatus) { this.orderStatus = orderStatus; }

    public OrderType getOrderType() { return orderType; }
    public void setOrderType(OrderType orderType) { this.orderType = orderType; }

    public OpenCloseType getOpenCloseType() { return openCloseType; }
    public void setOpenCloseType(OpenCloseType openCloseType) { this.openCloseType = openCloseType; }

    public LocalTime getOrderTime() { return orderTime; }
    public void setOrderTime(LocalTime orderTime) { this.orderTime = orderTime; }

    public String getMemberCode() { return memberCode; }
    public void setMemberCode(String memberCode) { this.memberCode = memberCode; }

    public String getTradeCode() { return tradeCode; }
    public void setTradeCode(String tradeCode) { this.tradeCode = tradeCode; }

    public String getSettlementMemberCode() { return settlementMemberCode; }
    public void setSettlementMemberCode(String settlementMemberCode) { this.settlementMemberCode = settlementMemberCode; }

    public CombinationStrategy getStrategy() { return strategy; }
    public void setStrategy(CombinationStrategy strategy) { this.strategy = strategy; }

    public BigDecimal getLeg1Ratio() { return leg1Ratio; }
    public void setLeg1Ratio(BigDecimal leg1Ratio) { this.leg1Ratio = leg1Ratio; }

    public BigDecimal getLeg2Ratio() { return leg2Ratio; }
    public void setLeg2Ratio(BigDecimal leg2Ratio) { this.leg2Ratio = leg2Ratio; }

    @Override
    public String toString() {
        return String.format("CombinationOrderInfo{orderNumber='%s', combination='%s', leg1='%s', leg2='%s', " +
                "calculatedPrice=%s, remainingVolume=%s, buySellFlag=%s, orderStatus=%s, strategy=%s}",
                orderNumber, combinationContractCode, leg1ContractCode, leg2ContractCode,
                calculatedPrice, remainingVolume, buySellFlag, orderStatus, strategy);
    }

    /**
     * 组合策略枚举
     */
    public enum CombinationStrategy {
        SPREAD("价差"),
        STRADDLE("跨式"),
        STRANGLE("宽跨式"),
        BUTTERFLY("蝶式"),
        CONDOR("鹰式");

        private final String description;

        CombinationStrategy(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
