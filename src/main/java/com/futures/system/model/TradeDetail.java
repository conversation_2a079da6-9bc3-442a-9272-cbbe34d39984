package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.futures.system.utils.LocalTimeDeserializer;
import com.futures.system.utils.LocalTimeSerializer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;

/**
 * 成交明细
 * 对应数据表 dwd_trd_trd_dtl_rt
 */
public class TradeDetail extends OrderBookEvent {
    
    /**
     * 统一开户账号
     */
    @JsonProperty("unfy_opnacct_cde")
    private String unifiedAccountCode;
    
    /**
     * 期货期权标志
     */
    @JsonProperty("futrs_opt_tag")
    private String futuresOptionFlag;
    
    /**
     * 买卖标志
     */
    @JsonProperty("b_s_tag")
    private BuySellFlag buySellFlag;
    
    /**
     * 唯一交易编号
     */
    @JsonProperty("uniq_trd_nbr")
    private String uniqueTradeNumber;
    
    /**
     * 成交编号
     */
    @JsonProperty("trd_nbr")
    private String tradeNumber;
    
    /**
     * 结算会员会员编码
     */
    @JsonProperty("settle_memb_memb_cde")
    private String settlementMemberCode;
    
    /**
     * 结算会员统一编码
     */
    @JsonProperty("settle_memb_unfy_cde")
    private String settlementMemberUnifiedCode;
    
    /**
     * 主动被动标志
     */
    @JsonProperty("intv_pasv_tag")
    private String initiativePassiveFlag;
    
    /**
     * 委托数量
     */
    @JsonProperty("ord_vol")
    private BigDecimal orderVolume;
    
    /**
     * 报单时间
     */
    @JsonProperty("ord_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime orderTime;
    
    /**
     * 委托价格
     */
    @JsonProperty("ord_prc")
    private BigDecimal orderPrice;
    
    /**
     * 委托类型
     */
    @JsonProperty("ord_type")
    private OrderType orderType;
    
    /**
     * 会员资金账号
     */
    @JsonProperty("memb_fund_acctnbr")
    private String memberFundAccountNumber;
    
    /**
     * 对手唯一交易编号
     */
    @JsonProperty("contpty_uniq_trd_nbr")
    private String counterpartyUniqueTradeNumber;
    
    /**
     * 对手报单编号
     */
    @JsonProperty("contpty_ord_nbr")
    private String counterpartyOrderNumber;
    
    /**
     * 对手统一开户账号
     */
    @JsonProperty("contpty_unfy_opnacct_cde")
    private String counterpartyUnifiedAccountCode;
    
    /**
     * 对手会员编码
     */
    @JsonProperty("contpty_memb_cde")
    private String counterpartyMemberCode;
    
    /**
     * 对手期货公司统一编码
     */
    @JsonProperty("contpty_fcomp_unfy_cde")
    private String counterpartyFuturesCompanyCode;
    
    /**
     * 对手开平仓类型
     */
    @JsonProperty("contpty_ocpos_type")
    private OpenCloseType counterpartyOpenCloseType;
    
    /**
     * 对手买卖标志
     */
    @JsonProperty("contpty_b_s_tag")
    private BuySellFlag counterpartyBuySellFlag;
    
    /**
     * 对手委托数量
     */
    @JsonProperty("contpty_ord_vol")
    private BigDecimal counterpartyOrderVolume;
    
    /**
     * 对手报单时间
     */
    @JsonProperty("contpty_ord_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime counterpartyOrderTime;
    
    /**
     * 对手委托价格
     */
    @JsonProperty("contpty_ord_prc")
    private BigDecimal counterpartyOrderPrice;
    
    /**
     * 对手报单类型
     */
    @JsonProperty("contpty_ord_type")
    private OrderType counterpartyOrderType;
    
    /**
     * 成交价格
     */
    @JsonProperty("trd_prc")
    private BigDecimal tradePrice;
    
    /**
     * 开平仓类型
     */
    @JsonProperty("ocpos_type")
    private OpenCloseType openCloseType;
    
    /**
     * 高频委托标志
     */
    @JsonProperty("highfrequ_ord_tag")
    private String highFrequencyOrderFlag;
    
    /**
     * 成交类型
     */
    @JsonProperty("trd_type")
    private TradeType tradeType;
    
    /**
     * 报单价格条件
     */
    @JsonProperty("ord_prc_cndt")
    private String orderPriceCondition;
    
    /**
     * 投机套保标志
     */
    @JsonProperty("specu_hedg_tag")
    private String speculationHedgeFlag;
    
    /**
     * 看涨看跌标志
     */
    @JsonProperty("c_p_tag")
    private String callPutFlag;
    
    /**
     * 执行价格
     */
    @JsonProperty("exec_prc")
    private BigDecimal executePrice;
    
    /**
     * 交易角色
     */
    @JsonProperty("trd_role")
    private String tradeRole;
    
    /**
     * 本地报单编号
     */
    @JsonProperty("local_ord_nbr")
    private String localOrderNumber;
    
    /**
     * 报单编号
     */
    @JsonProperty("ord_nbr")
    private String orderNumber;
    
    /**
     * 发生日期
     */
    @JsonProperty("ocr_dt")
    private LocalDate occurDate;
    
    /**
     * 记录号
     */
    @JsonProperty("rcod_nbr")
    private String recordNumber;
    
    /**
     * 席位号
     */
    @JsonProperty("seat_cde")
    private String seatCode;
    
    /**
     * 成交数量
     */
    @JsonProperty("trd_vol")
    private BigDecimal tradeVolume;
    
    /**
     * 成交时间毫秒
     */
    @JsonProperty("trd_tm_millisec")
    private Long tradeTimeMillisec;
    
    /**
     * 成交时间微秒
     */
    @JsonProperty("trd_tm_microsec")
    private Long tradeTimeMicrosec;
    
    /**
     * 成交时间
     */
    @JsonProperty("trd_tm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime tradeTime;

    // 构造函数
    public TradeDetail() {
        super();
    }

    @Override
    public long getEventTimestamp() {
        if (tradeTime != null && getTradeDate() != null) {
            return getTradeDate().atTime(tradeTime).toInstant(ZoneOffset.UTC).toEpochMilli();
        }
        return System.currentTimeMillis();
    }

    // Getters and Setters
    public String getUnifiedAccountCode() {
        return unifiedAccountCode;
    }

    public void setUnifiedAccountCode(String unifiedAccountCode) {
        this.unifiedAccountCode = unifiedAccountCode;
    }

    public String getFuturesOptionFlag() {
        return futuresOptionFlag;
    }

    public void setFuturesOptionFlag(String futuresOptionFlag) {
        this.futuresOptionFlag = futuresOptionFlag;
    }

    public BuySellFlag getBuySellFlag() {
        return buySellFlag;
    }

    public void setBuySellFlag(BuySellFlag buySellFlag) {
        this.buySellFlag = buySellFlag;
    }

    public String getUniqueTradeNumber() {
        return uniqueTradeNumber;
    }

    public void setUniqueTradeNumber(String uniqueTradeNumber) {
        this.uniqueTradeNumber = uniqueTradeNumber;
    }

    public String getTradeNumber() {
        return tradeNumber;
    }

    public void setTradeNumber(String tradeNumber) {
        this.tradeNumber = tradeNumber;
    }

    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public String getSettlementMemberUnifiedCode() {
        return settlementMemberUnifiedCode;
    }

    public void setSettlementMemberUnifiedCode(String settlementMemberUnifiedCode) {
        this.settlementMemberUnifiedCode = settlementMemberUnifiedCode;
    }

    public String getInitiativePassiveFlag() {
        return initiativePassiveFlag;
    }

    public void setInitiativePassiveFlag(String initiativePassiveFlag) {
        this.initiativePassiveFlag = initiativePassiveFlag;
    }

    public BigDecimal getOrderVolume() {
        return orderVolume;
    }

    public void setOrderVolume(BigDecimal orderVolume) {
        this.orderVolume = orderVolume;
    }

    public LocalTime getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(LocalTime orderTime) {
        this.orderTime = orderTime;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public String getMemberFundAccountNumber() {
        return memberFundAccountNumber;
    }

    public void setMemberFundAccountNumber(String memberFundAccountNumber) {
        this.memberFundAccountNumber = memberFundAccountNumber;
    }

    public String getCounterpartyUniqueTradeNumber() {
        return counterpartyUniqueTradeNumber;
    }

    public void setCounterpartyUniqueTradeNumber(String counterpartyUniqueTradeNumber) {
        this.counterpartyUniqueTradeNumber = counterpartyUniqueTradeNumber;
    }

    public String getCounterpartyOrderNumber() {
        return counterpartyOrderNumber;
    }

    public void setCounterpartyOrderNumber(String counterpartyOrderNumber) {
        this.counterpartyOrderNumber = counterpartyOrderNumber;
    }

    public String getCounterpartyUnifiedAccountCode() {
        return counterpartyUnifiedAccountCode;
    }

    public void setCounterpartyUnifiedAccountCode(String counterpartyUnifiedAccountCode) {
        this.counterpartyUnifiedAccountCode = counterpartyUnifiedAccountCode;
    }

    public String getCounterpartyMemberCode() {
        return counterpartyMemberCode;
    }

    public void setCounterpartyMemberCode(String counterpartyMemberCode) {
        this.counterpartyMemberCode = counterpartyMemberCode;
    }

    public String getCounterpartyFuturesCompanyCode() {
        return counterpartyFuturesCompanyCode;
    }

    public void setCounterpartyFuturesCompanyCode(String counterpartyFuturesCompanyCode) {
        this.counterpartyFuturesCompanyCode = counterpartyFuturesCompanyCode;
    }

    public OpenCloseType getCounterpartyOpenCloseType() {
        return counterpartyOpenCloseType;
    }

    public void setCounterpartyOpenCloseType(OpenCloseType counterpartyOpenCloseType) {
        this.counterpartyOpenCloseType = counterpartyOpenCloseType;
    }

    public BuySellFlag getCounterpartyBuySellFlag() {
        return counterpartyBuySellFlag;
    }

    public void setCounterpartyBuySellFlag(BuySellFlag counterpartyBuySellFlag) {
        this.counterpartyBuySellFlag = counterpartyBuySellFlag;
    }

    public BigDecimal getCounterpartyOrderVolume() {
        return counterpartyOrderVolume;
    }

    public void setCounterpartyOrderVolume(BigDecimal counterpartyOrderVolume) {
        this.counterpartyOrderVolume = counterpartyOrderVolume;
    }

    public LocalTime getCounterpartyOrderTime() {
        return counterpartyOrderTime;
    }

    public void setCounterpartyOrderTime(LocalTime counterpartyOrderTime) {
        this.counterpartyOrderTime = counterpartyOrderTime;
    }

    public BigDecimal getCounterpartyOrderPrice() {
        return counterpartyOrderPrice;
    }

    public void setCounterpartyOrderPrice(BigDecimal counterpartyOrderPrice) {
        this.counterpartyOrderPrice = counterpartyOrderPrice;
    }

    public OrderType getCounterpartyOrderType() {
        return counterpartyOrderType;
    }

    public void setCounterpartyOrderType(OrderType counterpartyOrderType) {
        this.counterpartyOrderType = counterpartyOrderType;
    }

    public BigDecimal getTradePrice() {
        return tradePrice;
    }

    public void setTradePrice(BigDecimal tradePrice) {
        this.tradePrice = tradePrice;
    }

    public OpenCloseType getOpenCloseType() {
        return openCloseType;
    }

    public void setOpenCloseType(OpenCloseType openCloseType) {
        this.openCloseType = openCloseType;
    }

    public String getHighFrequencyOrderFlag() {
        return highFrequencyOrderFlag;
    }

    public void setHighFrequencyOrderFlag(String highFrequencyOrderFlag) {
        this.highFrequencyOrderFlag = highFrequencyOrderFlag;
    }

    public TradeType getTradeType() {
        return tradeType;
    }

    public void setTradeType(TradeType tradeType) {
        this.tradeType = tradeType;
    }

    public String getOrderPriceCondition() {
        return orderPriceCondition;
    }

    public void setOrderPriceCondition(String orderPriceCondition) {
        this.orderPriceCondition = orderPriceCondition;
    }

    public String getSpeculationHedgeFlag() {
        return speculationHedgeFlag;
    }

    public void setSpeculationHedgeFlag(String speculationHedgeFlag) {
        this.speculationHedgeFlag = speculationHedgeFlag;
    }

    public String getCallPutFlag() {
        return callPutFlag;
    }

    public void setCallPutFlag(String callPutFlag) {
        this.callPutFlag = callPutFlag;
    }

    public BigDecimal getExecutePrice() {
        return executePrice;
    }

    public void setExecutePrice(BigDecimal executePrice) {
        this.executePrice = executePrice;
    }

    public String getTradeRole() {
        return tradeRole;
    }

    public void setTradeRole(String tradeRole) {
        this.tradeRole = tradeRole;
    }

    public String getLocalOrderNumber() {
        return localOrderNumber;
    }

    public void setLocalOrderNumber(String localOrderNumber) {
        this.localOrderNumber = localOrderNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public LocalDate getOccurDate() {
        return occurDate;
    }

    public void setOccurDate(LocalDate occurDate) {
        this.occurDate = occurDate;
    }

    public String getRecordNumber() {
        return recordNumber;
    }

    public void setRecordNumber(String recordNumber) {
        this.recordNumber = recordNumber;
    }

    public String getSeatCode() {
        return seatCode;
    }

    public void setSeatCode(String seatCode) {
        this.seatCode = seatCode;
    }

    public BigDecimal getTradeVolume() {
        return tradeVolume;
    }

    public void setTradeVolume(BigDecimal tradeVolume) {
        this.tradeVolume = tradeVolume;
    }

    public Long getTradeTimeMillisec() {
        return tradeTimeMillisec;
    }

    public void setTradeTimeMillisec(Long tradeTimeMillisec) {
        this.tradeTimeMillisec = tradeTimeMillisec;
    }

    public Long getTradeTimeMicrosec() {
        return tradeTimeMicrosec;
    }

    public void setTradeTimeMicrosec(Long tradeTimeMicrosec) {
        this.tradeTimeMicrosec = tradeTimeMicrosec;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    @Override
    public String toString() {
        return "TradeDetail{" +
                "tradeNumber='" + tradeNumber + '\'' +
                ", contractCode='" + getContractCode() + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", tradePrice=" + tradePrice +
                ", tradeVolume=" + tradeVolume +
                ", buySellFlag=" + buySellFlag +
                ", tradeType=" + tradeType +
                ", tradeTime=" + tradeTime +
                '}';
    }
}
