package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 买卖标志枚举
 * 对应数据字段 b_s_tag
 */
public enum BuySellFlag {
    /**
     * B = 买
     */
    BUY("B", "买"),
    
    /**
     * S = 卖
     */
    SELL("S", "卖");

    private final String code;
    private final String description;

    BuySellFlag(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static BuySellFlag fromCode(String code) {
        for (BuySellFlag flag : BuySellFlag.values()) {
            if (flag.code.equals(code)) {
                return flag;
            }
        }
        throw new IllegalArgumentException("Unknown buy/sell flag code: " + code);
    }

    /**
     * 获取相反的买卖方向
     */
    public BuySellFlag opposite() {
        return this == BUY ? SELL : BUY;
    }
}
