package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 持仓模型
 * 表示某个方向（多头或空头）的持仓信息
 */
public class Position implements Serializable {
    
    /**
     * 持仓数量
     */
    private BigDecimal volume;
    
    /**
     * 持仓总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 平均开仓价格
     */
    private BigDecimal averagePrice;
    
    /**
     * 已平仓数量
     */
    private BigDecimal closedVolume;
    
    /**
     * 平仓金额
     */
    private BigDecimal closeAmount;
    
    /**
     * 已实现盈亏（仅平仓部分）
     */
    private BigDecimal realizedPnL;

    // 构造函数
    public Position() {
        this.volume = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
        this.averagePrice = BigDecimal.ZERO;
        this.closedVolume = BigDecimal.ZERO;
        this.closeAmount = BigDecimal.ZERO;
        this.realizedPnL = BigDecimal.ZERO;
    }

    /**
     * 增加持仓（开仓）
     */
    public void addVolume(BigDecimal addVolume, BigDecimal price) {
        if (addVolume == null || addVolume.compareTo(BigDecimal.ZERO) <= 0 ||
            price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        
        BigDecimal addAmount = addVolume.multiply(price);
        
        // 更新总金额和数量
        this.totalAmount = this.totalAmount.add(addAmount);
        this.volume = this.volume.add(addVolume);
        
        // 重新计算平均价格
        if (this.volume.compareTo(BigDecimal.ZERO) > 0) {
            this.averagePrice = this.totalAmount.divide(this.volume, 6, RoundingMode.HALF_UP);
        }
    }

    /**
     * 减少持仓（平仓）
     * 返回平仓盈亏
     */
    public BigDecimal closeVolume(BigDecimal closeVol, BigDecimal closePrice) {
        if (closeVol == null || closeVol.compareTo(BigDecimal.ZERO) <= 0 ||
            closePrice == null || closePrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 检查平仓数量不能超过持仓数量
        BigDecimal actualCloseVolume = closeVol.min(this.volume);
        if (actualCloseVolume.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 计算平仓盈亏
        BigDecimal closePnL = actualCloseVolume.multiply(closePrice.subtract(this.averagePrice));
        
        // 更新持仓
        this.volume = this.volume.subtract(actualCloseVolume);
        BigDecimal closeAmountValue = actualCloseVolume.multiply(closePrice);
        this.closeAmount = this.closeAmount.add(closeAmountValue);
        this.closedVolume = this.closedVolume.add(actualCloseVolume);
        this.realizedPnL = this.realizedPnL.add(closePnL);
        
        // 更新总金额（按比例减少）
        if (this.volume.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal remainingRatio = this.volume.divide(this.volume.add(actualCloseVolume), 6, RoundingMode.HALF_UP);
            this.totalAmount = this.totalAmount.multiply(remainingRatio);
        } else {
            this.totalAmount = BigDecimal.ZERO;
            this.averagePrice = BigDecimal.ZERO;
        }
        
        return closePnL;
    }

    /**
     * 计算浮动盈亏
     */
    public BigDecimal calculateUnrealizedPnL(BigDecimal currentPrice) {
        if (currentPrice == null || this.volume.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        return this.volume.multiply(currentPrice.subtract(this.averagePrice));
    }

    /**
     * 计算持仓市值
     */
    public BigDecimal calculateMarketValue(BigDecimal currentPrice) {
        if (currentPrice == null || this.volume.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        return this.volume.multiply(currentPrice);
    }

    /**
     * 检查是否有持仓
     */
    public boolean hasPosition() {
        return this.volume.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取持仓成本
     */
    public BigDecimal getPositionCost() {
        return this.totalAmount;
    }

    /**
     * 获取平均盈亏率
     */
    public BigDecimal getAveragePnLRate(BigDecimal currentPrice) {
        if (currentPrice == null || this.averagePrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        return currentPrice.subtract(this.averagePrice)
                .divide(this.averagePrice, 6, RoundingMode.HALF_UP);
    }

    /**
     * 重置持仓
     */
    public void reset() {
        this.volume = BigDecimal.ZERO;
        this.totalAmount = BigDecimal.ZERO;
        this.averagePrice = BigDecimal.ZERO;
        this.closedVolume = BigDecimal.ZERO;
        this.closeAmount = BigDecimal.ZERO;
        this.realizedPnL = BigDecimal.ZERO;
    }

    /**
     * 深拷贝持仓
     */
    public Position deepCopy() {
        Position copy = new Position();
        copy.volume = this.volume;
        copy.totalAmount = this.totalAmount;
        copy.averagePrice = this.averagePrice;
        copy.closedVolume = this.closedVolume;
        copy.closeAmount = this.closeAmount;
        copy.realizedPnL = this.realizedPnL;
        return copy;
    }

    /**
     * 合并另一个持仓
     */
    public void merge(Position other) {
        if (other == null) {
            return;
        }
        
        // 合并开仓部分
        if (other.volume.compareTo(BigDecimal.ZERO) > 0) {
            addVolume(other.volume, other.averagePrice);
        }
        
        // 合并平仓部分
        this.closedVolume = this.closedVolume.add(other.closedVolume);
        this.closeAmount = this.closeAmount.add(other.closeAmount);
        this.realizedPnL = this.realizedPnL.add(other.realizedPnL);
    }

    // Getters and Setters
    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getAveragePrice() {
        return averagePrice;
    }

    public void setAveragePrice(BigDecimal averagePrice) {
        this.averagePrice = averagePrice;
    }

    public BigDecimal getClosedVolume() {
        return closedVolume;
    }

    public void setClosedVolume(BigDecimal closedVolume) {
        this.closedVolume = closedVolume;
    }

    public BigDecimal getCloseAmount() {
        return closeAmount;
    }

    public void setCloseAmount(BigDecimal closeAmount) {
        this.closeAmount = closeAmount;
    }

    public BigDecimal getRealizedPnL() {
        return realizedPnL;
    }

    public void setRealizedPnL(BigDecimal realizedPnL) {
        this.realizedPnL = realizedPnL;
    }

    @Override
    public String toString() {
        return String.format("Position{volume=%s, avgPrice=%s, totalAmount=%s, closedVol=%s, realizedPnL=%s}",
                volume, averagePrice, totalAmount, closedVolume, realizedPnL);
    }
}
