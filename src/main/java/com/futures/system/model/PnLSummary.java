package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 盈亏汇总
 * 按结算会员汇总的盈亏计算结果
 */
public class PnLSummary implements Serializable {
    
    /**
     * 结算会员编码
     */
    @JsonProperty("settlement_member_code")
    private String settlementMemberCode;
    
    /**
     * 汇总时间
     */
    @JsonProperty("summary_time")
    private LocalDateTime summaryTime;
    
    /**
     * 汇总序列号
     */
    @JsonProperty("summary_sequence")
    private Long summarySequence;
    
    /**
     * 按合约的盈亏计算结果
     */
    @JsonProperty("contract_pnl_map")
    private Map<String, PnLCalculation> contractPnLMap;
    
    /**
     * 总已实现盈亏
     */
    @JsonProperty("total_realized_pnl")
    private BigDecimal totalRealizedPnL;
    
    /**
     * 总浮动盈亏
     */
    @JsonProperty("total_unrealized_pnl")
    private BigDecimal totalUnrealizedPnL;
    
    /**
     * 总盈亏
     */
    @JsonProperty("total_pnl")
    private BigDecimal totalPnL;
    
    /**
     * 总开仓金额
     */
    @JsonProperty("total_open_amount")
    private BigDecimal totalOpenAmount;
    
    /**
     * 总平仓金额
     */
    @JsonProperty("total_close_amount")
    private BigDecimal totalCloseAmount;
    
    /**
     * 总收益率
     */
    @JsonProperty("total_return_rate")
    private BigDecimal totalReturnRate;
    
    /**
     * 已实现收益率
     */
    @JsonProperty("realized_return_rate")
    private BigDecimal realizedReturnRate;
    
    /**
     * 合约总数
     */
    @JsonProperty("total_contracts")
    private Integer totalContracts;
    
    /**
     * 有持仓的合约数
     */
    @JsonProperty("active_contracts")
    private Integer activeContracts;
    
    /**
     * 最大单合约盈亏
     */
    @JsonProperty("max_contract_pnl")
    private BigDecimal maxContractPnL;
    
    /**
     * 最小单合约盈亏
     */
    @JsonProperty("min_contract_pnl")
    private BigDecimal minContractPnL;

    // 构造函数
    public PnLSummary() {
        this.contractPnLMap = new HashMap<>();
        this.totalRealizedPnL = BigDecimal.ZERO;
        this.totalUnrealizedPnL = BigDecimal.ZERO;
        this.totalPnL = BigDecimal.ZERO;
        this.totalOpenAmount = BigDecimal.ZERO;
        this.totalCloseAmount = BigDecimal.ZERO;
        this.totalReturnRate = BigDecimal.ZERO;
        this.realizedReturnRate = BigDecimal.ZERO;
        this.totalContracts = 0;
        this.activeContracts = 0;
        this.maxContractPnL = BigDecimal.ZERO;
        this.minContractPnL = BigDecimal.ZERO;
        this.summaryTime = LocalDateTime.now();
    }

    public PnLSummary(String settlementMemberCode) {
        this();
        this.settlementMemberCode = settlementMemberCode;
    }

    /**
     * 添加合约盈亏
     */
    public void addContractPnL(String contractCode, PnLCalculation calculation) {
        contractPnLMap.put(contractCode, calculation);
    }

    /**
     * 获取指定合约的盈亏
     */
    public PnLCalculation getContractPnL(String contractCode) {
        return contractPnLMap.get(contractCode);
    }

    /**
     * 检查是否有盈利
     */
    public boolean isProfitable() {
        return totalPnL.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查是否有亏损
     */
    public boolean hasLoss() {
        return totalPnL.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 获取盈利合约数量
     */
    public int getProfitableContractsCount() {
        return (int) contractPnLMap.values().stream()
                .filter(pnl -> pnl.getTotalPnL().compareTo(BigDecimal.ZERO) > 0)
                .count();
    }

    /**
     * 获取亏损合约数量
     */
    public int getLossContractsCount() {
        return (int) contractPnLMap.values().stream()
                .filter(pnl -> pnl.getTotalPnL().compareTo(BigDecimal.ZERO) < 0)
                .count();
    }

    /**
     * 获取风险等级
     */
    public RiskLevel getRiskLevel() {
        if (totalReturnRate == null) {
            return RiskLevel.UNKNOWN;
        }
        
        BigDecimal absReturnRate = totalReturnRate.abs();
        
        if (absReturnRate.compareTo(new BigDecimal("0.1")) >= 0) {
            return RiskLevel.HIGH;
        } else if (absReturnRate.compareTo(new BigDecimal("0.05")) >= 0) {
            return RiskLevel.MEDIUM;
        } else {
            return RiskLevel.LOW;
        }
    }

    /**
     * 深拷贝盈亏汇总
     */
    public PnLSummary deepCopy() {
        PnLSummary copy = new PnLSummary(this.settlementMemberCode);
        copy.summaryTime = this.summaryTime;
        copy.summarySequence = this.summarySequence;
        copy.totalRealizedPnL = this.totalRealizedPnL;
        copy.totalUnrealizedPnL = this.totalUnrealizedPnL;
        copy.totalPnL = this.totalPnL;
        copy.totalOpenAmount = this.totalOpenAmount;
        copy.totalCloseAmount = this.totalCloseAmount;
        copy.totalReturnRate = this.totalReturnRate;
        copy.realizedReturnRate = this.realizedReturnRate;
        copy.totalContracts = this.totalContracts;
        copy.activeContracts = this.activeContracts;
        copy.maxContractPnL = this.maxContractPnL;
        copy.minContractPnL = this.minContractPnL;
        
        // 深拷贝合约盈亏映射
        for (Map.Entry<String, PnLCalculation> entry : this.contractPnLMap.entrySet()) {
            copy.contractPnLMap.put(entry.getKey(), entry.getValue().deepCopy());
        }
        
        return copy;
    }

    // Getters and Setters
    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public LocalDateTime getSummaryTime() {
        return summaryTime;
    }

    public void setSummaryTime(LocalDateTime summaryTime) {
        this.summaryTime = summaryTime;
    }

    public Long getSummarySequence() {
        return summarySequence;
    }

    public void setSummarySequence(Long summarySequence) {
        this.summarySequence = summarySequence;
    }

    public Map<String, PnLCalculation> getContractPnLMap() {
        return contractPnLMap;
    }

    public void setContractPnLMap(Map<String, PnLCalculation> contractPnLMap) {
        this.contractPnLMap = contractPnLMap;
    }

    public BigDecimal getTotalRealizedPnL() {
        return totalRealizedPnL;
    }

    public void setTotalRealizedPnL(BigDecimal totalRealizedPnL) {
        this.totalRealizedPnL = totalRealizedPnL;
    }

    public BigDecimal getTotalUnrealizedPnL() {
        return totalUnrealizedPnL;
    }

    public void setTotalUnrealizedPnL(BigDecimal totalUnrealizedPnL) {
        this.totalUnrealizedPnL = totalUnrealizedPnL;
    }

    public BigDecimal getTotalPnL() {
        return totalPnL;
    }

    public void setTotalPnL(BigDecimal totalPnL) {
        this.totalPnL = totalPnL;
    }

    public BigDecimal getTotalOpenAmount() {
        return totalOpenAmount;
    }

    public void setTotalOpenAmount(BigDecimal totalOpenAmount) {
        this.totalOpenAmount = totalOpenAmount;
    }

    public BigDecimal getTotalCloseAmount() {
        return totalCloseAmount;
    }

    public void setTotalCloseAmount(BigDecimal totalCloseAmount) {
        this.totalCloseAmount = totalCloseAmount;
    }

    public BigDecimal getTotalReturnRate() {
        return totalReturnRate;
    }

    public void setTotalReturnRate(BigDecimal totalReturnRate) {
        this.totalReturnRate = totalReturnRate;
    }

    public BigDecimal getRealizedReturnRate() {
        return realizedReturnRate;
    }

    public void setRealizedReturnRate(BigDecimal realizedReturnRate) {
        this.realizedReturnRate = realizedReturnRate;
    }

    public Integer getTotalContracts() {
        return totalContracts;
    }

    public void setTotalContracts(Integer totalContracts) {
        this.totalContracts = totalContracts;
    }

    public Integer getActiveContracts() {
        return activeContracts;
    }

    public void setActiveContracts(Integer activeContracts) {
        this.activeContracts = activeContracts;
    }

    public BigDecimal getMaxContractPnL() {
        return maxContractPnL;
    }

    public void setMaxContractPnL(BigDecimal maxContractPnL) {
        this.maxContractPnL = maxContractPnL;
    }

    public BigDecimal getMinContractPnL() {
        return minContractPnL;
    }

    public void setMinContractPnL(BigDecimal minContractPnL) {
        this.minContractPnL = minContractPnL;
    }

    @Override
    public String toString() {
        return String.format("PnLSummary{member=%s, totalPnL=%s, realizedPnL=%s, unrealizedPnL=%s, " +
                "returnRate=%s, contracts=%d/%d, time=%s}",
                settlementMemberCode, totalPnL, totalRealizedPnL, totalUnrealizedPnL,
                totalReturnRate, activeContracts, totalContracts, summaryTime);
    }

    /**
     * 风险等级枚举
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中风险"),
        HIGH("高风险"),
        UNKNOWN("未知");

        private final String description;

        RiskLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
