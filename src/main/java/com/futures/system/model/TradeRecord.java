package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 成交记录
 * 用于盈亏计算中记录已处理的成交信息
 */
public class TradeRecord implements Serializable {
    
    /**
     * 成交编号
     */
    private String tradeNumber;
    
    /**
     * 报单编号
     */
    private String orderNumber;
    
    /**
     * 合约代码
     */
    private String contractCode;
    
    /**
     * 成交价格
     */
    private BigDecimal tradePrice;
    
    /**
     * 成交数量
     */
    private BigDecimal tradeVolume;
    
    /**
     * 买卖标志
     */
    private BuySellFlag buySellFlag;
    
    /**
     * 开平仓类型
     */
    private OpenCloseType openCloseType;
    
    /**
     * 成交类型
     */
    private TradeType tradeType;
    
    /**
     * 成交时间
     */
    private LocalTime tradeTime;
    
    /**
     * 结算会员编码
     */
    private String settlementMemberCode;
    
    /**
     * 成交金额
     */
    private BigDecimal tradeAmount;
    
    /**
     * 处理时间戳
     */
    private long processTimestamp;

    // 构造函数
    public TradeRecord() {
        this.processTimestamp = System.currentTimeMillis();
    }

    public TradeRecord(TradeDetail trade) {
        this();
        if (trade != null) {
            this.tradeNumber = trade.getTradeNumber();
            this.orderNumber = trade.getOrderNumber();
            this.contractCode = trade.getContractCode();
            this.tradePrice = trade.getTradePrice();
            this.tradeVolume = trade.getTradeVolume();
            this.buySellFlag = trade.getBuySellFlag();
            this.openCloseType = trade.getOpenCloseType();
            this.tradeType = trade.getTradeType();
            this.tradeTime = trade.getTradeTime();
            this.settlementMemberCode = trade.getSettlementMemberCode();
            
            // 计算成交金额
            if (this.tradePrice != null && this.tradeVolume != null) {
                this.tradeAmount = this.tradePrice.multiply(this.tradeVolume);
            } else {
                this.tradeAmount = BigDecimal.ZERO;
            }
        }
    }

    /**
     * 检查是否为开仓成交
     */
    public boolean isOpenTrade() {
        return openCloseType == OpenCloseType.OPEN;
    }

    /**
     * 检查是否为平仓成交
     */
    public boolean isCloseTrade() {
        return openCloseType == OpenCloseType.CLOSE || 
               openCloseType == OpenCloseType.CLOSE_TODAY ||
               openCloseType == OpenCloseType.CLOSE_YESTERDAY ||
               openCloseType == OpenCloseType.FORCE_CLOSE;
    }

    /**
     * 检查是否为买入成交
     */
    public boolean isBuyTrade() {
        return buySellFlag == BuySellFlag.BUY;
    }

    /**
     * 检查是否为卖出成交
     */
    public boolean isSellTrade() {
        return buySellFlag == BuySellFlag.SELL;
    }

    /**
     * 检查是否为普通成交
     */
    public boolean isNormalTrade() {
        return tradeType == TradeType.NORMAL;
    }

    /**
     * 获取成交方向（开仓时的持仓方向）
     */
    public PositionSide getPositionSide() {
        if (isOpenTrade()) {
            return isBuyTrade() ? PositionSide.LONG : PositionSide.SHORT;
        } else {
            // 平仓时方向相反
            return isBuyTrade() ? PositionSide.SHORT : PositionSide.LONG;
        }
    }

    /**
     * 计算对持仓的影响
     */
    public BigDecimal getPositionImpact() {
        if (isOpenTrade()) {
            // 开仓：买入为正，卖出为负
            return isBuyTrade() ? tradeVolume : tradeVolume.negate();
        } else {
            // 平仓：买入为负（减少空头），卖出为负（减少多头）
            return tradeVolume.negate();
        }
    }

    /**
     * 深拷贝成交记录
     */
    public TradeRecord deepCopy() {
        TradeRecord copy = new TradeRecord();
        copy.tradeNumber = this.tradeNumber;
        copy.orderNumber = this.orderNumber;
        copy.contractCode = this.contractCode;
        copy.tradePrice = this.tradePrice;
        copy.tradeVolume = this.tradeVolume;
        copy.buySellFlag = this.buySellFlag;
        copy.openCloseType = this.openCloseType;
        copy.tradeType = this.tradeType;
        copy.tradeTime = this.tradeTime;
        copy.settlementMemberCode = this.settlementMemberCode;
        copy.tradeAmount = this.tradeAmount;
        copy.processTimestamp = this.processTimestamp;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TradeRecord that = (TradeRecord) o;
        return Objects.equals(tradeNumber, that.tradeNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tradeNumber);
    }

    // Getters and Setters
    public String getTradeNumber() {
        return tradeNumber;
    }

    public void setTradeNumber(String tradeNumber) {
        this.tradeNumber = tradeNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public BigDecimal getTradePrice() {
        return tradePrice;
    }

    public void setTradePrice(BigDecimal tradePrice) {
        this.tradePrice = tradePrice;
    }

    public BigDecimal getTradeVolume() {
        return tradeVolume;
    }

    public void setTradeVolume(BigDecimal tradeVolume) {
        this.tradeVolume = tradeVolume;
    }

    public BuySellFlag getBuySellFlag() {
        return buySellFlag;
    }

    public void setBuySellFlag(BuySellFlag buySellFlag) {
        this.buySellFlag = buySellFlag;
    }

    public OpenCloseType getOpenCloseType() {
        return openCloseType;
    }

    public void setOpenCloseType(OpenCloseType openCloseType) {
        this.openCloseType = openCloseType;
    }

    public TradeType getTradeType() {
        return tradeType;
    }

    public void setTradeType(TradeType tradeType) {
        this.tradeType = tradeType;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public long getProcessTimestamp() {
        return processTimestamp;
    }

    public void setProcessTimestamp(long processTimestamp) {
        this.processTimestamp = processTimestamp;
    }

    @Override
    public String toString() {
        return String.format("TradeRecord{tradeNumber='%s', contract='%s', price=%s, volume=%s, side=%s, openClose=%s, time=%s}",
                tradeNumber, contractCode, tradePrice, tradeVolume, buySellFlag, openCloseType, tradeTime);
    }

    /**
     * 持仓方向枚举
     */
    public enum PositionSide {
        LONG("多头"),
        SHORT("空头");

        private final String description;

        PositionSide(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
