package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基础层订单簿
 * 基于单腿委托数据构建的基础订单簿，按价格-时间优先级维护买卖队列
 */
public class BaseOrderBook implements Serializable {
    
    /**
     * 合约代码
     */
    private String contractCode;
    
    /**
     * 买方价格档位（价格从高到低排序）
     */
    private TreeMap<BigDecimal, PriceLevel> bidLevels;
    
    /**
     * 卖方价格档位（价格从低到高排序）
     */
    private TreeMap<BigDecimal, PriceLevel> askLevels;
    
    /**
     * 订单索引（按报单编号快速查找）
     */
    private Map<String, OrderInfo> orderIndex;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 订单簿版本号（用于乐观锁）
     */
    private long version;
    
    /**
     * 最优买价
     */
    private BigDecimal bestBidPrice;
    
    /**
     * 最优卖价
     */
    private BigDecimal bestAskPrice;
    
    /**
     * 买方总数量
     */
    private BigDecimal totalBidVolume;
    
    /**
     * 卖方总数量
     */
    private BigDecimal totalAskVolume;

    // 构造函数
    public BaseOrderBook(String contractCode) {
        this.contractCode = contractCode;
        this.bidLevels = new TreeMap<>(Collections.reverseOrder()); // 买方价格从高到低
        this.askLevels = new TreeMap<>(); // 卖方价格从低到高
        this.orderIndex = new ConcurrentHashMap<>();
        this.lastUpdateTime = LocalDateTime.now();
        this.version = 0;
        this.totalBidVolume = BigDecimal.ZERO;
        this.totalAskVolume = BigDecimal.ZERO;
    }

    /**
     * 添加订单到订单簿
     */
    public synchronized boolean addOrder(OrderInfo order) {
        if (order == null || order.getOrderNumber() == null) {
            return false;
        }
        
        // 检查订单是否已存在
        if (orderIndex.containsKey(order.getOrderNumber())) {
            return updateOrder(order);
        }
        
        // 只处理在队列中的订单
        if (!order.getOrderStatus().isInQueue()) {
            return false;
        }
        
        try {
            // 添加到价格档位
            TreeMap<BigDecimal, PriceLevel> levels = order.getBuySellFlag() == BuySellFlag.BUY ? bidLevels : askLevels;
            PriceLevel level = levels.computeIfAbsent(order.getOrderPrice(), k -> new PriceLevel(k));
            level.addOrder(order);
            
            // 添加到索引
            orderIndex.put(order.getOrderNumber(), order);
            
            // 更新统计信息
            updateStatistics();
            updateVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新订单
     */
    public synchronized boolean updateOrder(OrderInfo updatedOrder) {
        if (updatedOrder == null || updatedOrder.getOrderNumber() == null) {
            return false;
        }
        
        OrderInfo existingOrder = orderIndex.get(updatedOrder.getOrderNumber());
        if (existingOrder == null) {
            return addOrder(updatedOrder);
        }
        
        try {
            // 如果价格改变，需要移动到新的价格档位
            if (!existingOrder.getOrderPrice().equals(updatedOrder.getOrderPrice())) {
                removeOrder(existingOrder.getOrderNumber());
                return addOrder(updatedOrder);
            }
            
            // 更新同一价格档位内的订单
            TreeMap<BigDecimal, PriceLevel> levels = updatedOrder.getBuySellFlag() == BuySellFlag.BUY ? bidLevels : askLevels;
            PriceLevel level = levels.get(updatedOrder.getOrderPrice());
            
            if (level != null) {
                if (updatedOrder.getOrderStatus().isInQueue()) {
                    // 更新订单数量
                    level.updateOrderVolume(updatedOrder.getOrderNumber(), updatedOrder.getRemainingVolume());
                    orderIndex.put(updatedOrder.getOrderNumber(), updatedOrder);
                } else {
                    // 订单不在队列中，移除
                    removeOrder(updatedOrder.getOrderNumber());
                }
            }
            
            updateStatistics();
            updateVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 移除订单
     */
    public synchronized boolean removeOrder(String orderNumber) {
        if (orderNumber == null) {
            return false;
        }
        
        OrderInfo order = orderIndex.remove(orderNumber);
        if (order == null) {
            return false;
        }
        
        try {
            // 从价格档位移除
            TreeMap<BigDecimal, PriceLevel> levels = order.getBuySellFlag() == BuySellFlag.BUY ? bidLevels : askLevels;
            PriceLevel level = levels.get(order.getOrderPrice());
            
            if (level != null) {
                level.removeOrder(orderNumber);
                
                // 如果价格档位为空，移除该档位
                if (level.isEmpty()) {
                    levels.remove(order.getOrderPrice());
                }
            }
            
            updateStatistics();
            updateVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理成交，更新相关订单
     */
    public synchronized boolean processTrade(String orderNumber, BigDecimal tradeVolume) {
        if (orderNumber == null || tradeVolume == null || tradeVolume.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        OrderInfo order = orderIndex.get(orderNumber);
        if (order == null) {
            return false;
        }
        
        try {
            // 更新订单成交信息
            order.updateFromTrade(tradeVolume);
            
            // 更新价格档位
            TreeMap<BigDecimal, PriceLevel> levels = order.getBuySellFlag() == BuySellFlag.BUY ? bidLevels : askLevels;
            PriceLevel level = levels.get(order.getOrderPrice());
            
            if (level != null) {
                if (order.getRemainingVolume().compareTo(BigDecimal.ZERO) <= 0) {
                    // 全部成交，移除订单
                    level.removeOrder(orderNumber);
                    orderIndex.remove(orderNumber);
                    
                    if (level.isEmpty()) {
                        levels.remove(order.getOrderPrice());
                    }
                } else {
                    // 部分成交，更新剩余量
                    level.updateOrderVolume(orderNumber, order.getRemainingVolume());
                }
            }
            
            updateStatistics();
            updateVersion();
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取最优买价档位
     */
    public PriceLevel getBestBidLevel() {
        return bidLevels.isEmpty() ? null : bidLevels.firstEntry().getValue();
    }

    /**
     * 获取最优卖价档位
     */
    public PriceLevel getBestAskLevel() {
        return askLevels.isEmpty() ? null : askLevels.firstEntry().getValue();
    }

    /**
     * 获取买方前N档
     */
    public List<PriceLevel> getTopBidLevels(int count) {
        return bidLevels.values().stream().limit(count).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取卖方前N档
     */
    public List<PriceLevel> getTopAskLevels(int count) {
        return askLevels.values().stream().limit(count).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics() {
        // 更新最优价格
        bestBidPrice = bidLevels.isEmpty() ? null : bidLevels.firstKey();
        bestAskPrice = askLevels.isEmpty() ? null : askLevels.firstKey();
        
        // 更新总数量
        totalBidVolume = bidLevels.values().stream()
                .map(PriceLevel::getTotalVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        totalAskVolume = askLevels.values().stream()
                .map(PriceLevel::getTotalVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 更新版本号
     */
    private void updateVersion() {
        version++;
    }

    /**
     * 深拷贝订单簿
     */
    public BaseOrderBook deepCopy() {
        BaseOrderBook copy = new BaseOrderBook(this.contractCode);
        
        // 复制买方档位
        for (Map.Entry<BigDecimal, PriceLevel> entry : this.bidLevels.entrySet()) {
            copy.bidLevels.put(entry.getKey(), entry.getValue().deepCopy());
        }
        
        // 复制卖方档位
        for (Map.Entry<BigDecimal, PriceLevel> entry : this.askLevels.entrySet()) {
            copy.askLevels.put(entry.getKey(), entry.getValue().deepCopy());
        }
        
        // 复制订单索引
        for (Map.Entry<String, OrderInfo> entry : this.orderIndex.entrySet()) {
            copy.orderIndex.put(entry.getKey(), entry.getValue().deepCopy());
        }
        
        copy.lastUpdateTime = this.lastUpdateTime;
        copy.version = this.version;
        copy.bestBidPrice = this.bestBidPrice;
        copy.bestAskPrice = this.bestAskPrice;
        copy.totalBidVolume = this.totalBidVolume;
        copy.totalAskVolume = this.totalAskVolume;
        
        return copy;
    }

    /**
     * 清空订单簿
     */
    public synchronized void clear() {
        bidLevels.clear();
        askLevels.clear();
        orderIndex.clear();
        updateStatistics();
        updateVersion();
    }

    /**
     * 检查订单簿是否为空
     */
    public boolean isEmpty() {
        return bidLevels.isEmpty() && askLevels.isEmpty();
    }

    // Getters
    public String getContractCode() { return contractCode; }
    public TreeMap<BigDecimal, PriceLevel> getBidLevels() { return bidLevels; }
    public TreeMap<BigDecimal, PriceLevel> getAskLevels() { return askLevels; }
    public Map<String, OrderInfo> getOrderIndex() { return orderIndex; }
    public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
    public long getVersion() { return version; }
    public BigDecimal getBestBidPrice() { return bestBidPrice; }
    public BigDecimal getBestAskPrice() { return bestAskPrice; }
    public BigDecimal getTotalBidVolume() { return totalBidVolume; }
    public BigDecimal getTotalAskVolume() { return totalAskVolume; }

    @Override
    public String toString() {
        return String.format("BaseOrderBook{contract=%s, bestBid=%s, bestAsk=%s, bidLevels=%d, askLevels=%d, orders=%d, version=%d}",
                contractCode, bestBidPrice, bestAskPrice, bidLevels.size(), askLevels.size(), orderIndex.size(), version);
    }
}
