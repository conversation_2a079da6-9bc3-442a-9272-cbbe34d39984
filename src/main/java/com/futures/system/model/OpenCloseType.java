package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 开平仓类型枚举
 * 对应数据字段 ocpos_type
 */
public enum OpenCloseType {
    /**
     * 0 = 开仓
     */
    OPEN("0", "开仓"),
    
    /**
     * 1 = 平仓
     */
    CLOSE("1", "平仓"),
    
    /**
     * 2 = 强平
     */
    FORCE_CLOSE("2", "强平"),
    
    /**
     * 3 = 平今
     */
    CLOSE_TODAY("3", "平今"),
    
    /**
     * 4 = 平昨
     */
    CLOSE_YESTERDAY("4", "平昨"),
    
    /**
     * 5 = 强减
     */
    FORCE_REDUCE("5", "强减");

    private final String code;
    private final String description;

    OpenCloseType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static OpenCloseType fromCode(String code) {
        for (OpenCloseType type : OpenCloseType.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown open/close type code: " + code);
    }

    /**
     * 判断是否为开仓操作
     */
    public boolean isOpen() {
        return this == OPEN;
    }

    /**
     * 判断是否为平仓操作
     */
    public boolean isClose() {
        return this == CLOSE || this == FORCE_CLOSE || this == CLOSE_TODAY || 
               this == CLOSE_YESTERDAY || this == FORCE_REDUCE;
    }
}
