package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 价格档位快照
 * 用于订单簿快照中的价格档位信息
 */
public class PriceLevelSnapshot implements Serializable {
    
    /**
     * 价格
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 总数量
     */
    @JsonProperty("total_volume")
    private BigDecimal totalVolume;
    
    /**
     * 订单数量
     */
    @JsonProperty("order_count")
    private Integer orderCount;
    
    /**
     * 买卖方向
     */
    @JsonProperty("side")
    private BuySellFlag side;
    
    /**
     * 是否包含虚拟层数据
     */
    @JsonProperty("has_virtual_data")
    private Boolean hasVirtualData;
    
    /**
     * 基础层数量
     */
    @JsonProperty("base_volume")
    private BigDecimal baseVolume;
    
    /**
     * 虚拟层数量（可能为负值）
     */
    @JsonProperty("virtual_volume")
    private BigDecimal virtualVolume;
    
    /**
     * 基础层订单数
     */
    @JsonProperty("base_order_count")
    private Integer baseOrderCount;
    
    /**
     * 虚拟层订单数
     */
    @JsonProperty("virtual_order_count")
    private Integer virtualOrderCount;

    // 构造函数
    public PriceLevelSnapshot() {
        this.orderCount = 0;
        this.totalVolume = BigDecimal.ZERO;
        this.hasVirtualData = false;
        this.baseVolume = BigDecimal.ZERO;
        this.virtualVolume = BigDecimal.ZERO;
        this.baseOrderCount = 0;
        this.virtualOrderCount = 0;
    }

    public PriceLevelSnapshot(BigDecimal price, BuySellFlag side) {
        this();
        this.price = price;
        this.side = side;
    }

    /**
     * 从PriceLevel创建快照
     */
    public static PriceLevelSnapshot fromPriceLevel(PriceLevel priceLevel, BuySellFlag side) {
        PriceLevelSnapshot snapshot = new PriceLevelSnapshot();
        snapshot.price = priceLevel.getPrice();
        snapshot.totalVolume = priceLevel.getTotalVolume();
        snapshot.orderCount = priceLevel.getOrderCount();
        snapshot.side = side;
        snapshot.baseVolume = priceLevel.getTotalVolume();
        snapshot.baseOrderCount = priceLevel.getOrderCount();
        snapshot.hasVirtualData = false;
        return snapshot;
    }

    /**
     * 添加虚拟层数据
     */
    public void addVirtualData(BigDecimal virtualVolume, int virtualOrderCount) {
        this.virtualVolume = this.virtualVolume.add(virtualVolume);
        this.virtualOrderCount += virtualOrderCount;
        this.totalVolume = this.baseVolume.add(this.virtualVolume);
        this.orderCount = this.baseOrderCount + this.virtualOrderCount;
        this.hasVirtualData = true;
    }

    /**
     * 检查档位是否有效（总量大于0或允许负值）
     */
    public boolean isValid() {
        // 在期货组合交易中，允许负值存在
        return totalVolume != null && orderCount != null && orderCount > 0;
    }

    /**
     * 检查是否为负值档位
     */
    public boolean isNegative() {
        return totalVolume != null && totalVolume.compareTo(BigDecimal.ZERO) < 0;
    }

    // Getters and Setters
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public BuySellFlag getSide() {
        return side;
    }

    public void setSide(BuySellFlag side) {
        this.side = side;
    }

    public Boolean getHasVirtualData() {
        return hasVirtualData;
    }

    public void setHasVirtualData(Boolean hasVirtualData) {
        this.hasVirtualData = hasVirtualData;
    }

    public BigDecimal getBaseVolume() {
        return baseVolume;
    }

    public void setBaseVolume(BigDecimal baseVolume) {
        this.baseVolume = baseVolume;
    }

    public BigDecimal getVirtualVolume() {
        return virtualVolume;
    }

    public void setVirtualVolume(BigDecimal virtualVolume) {
        this.virtualVolume = virtualVolume;
    }

    public Integer getBaseOrderCount() {
        return baseOrderCount;
    }

    public void setBaseOrderCount(Integer baseOrderCount) {
        this.baseOrderCount = baseOrderCount;
    }

    public Integer getVirtualOrderCount() {
        return virtualOrderCount;
    }

    public void setVirtualOrderCount(Integer virtualOrderCount) {
        this.virtualOrderCount = virtualOrderCount;
    }

    @Override
    public String toString() {
        return "PriceLevelSnapshot{" +
                "price=" + price +
                ", totalVolume=" + totalVolume +
                ", orderCount=" + orderCount +
                ", side=" + side +
                ", hasVirtualData=" + hasVirtualData +
                ", baseVolume=" + baseVolume +
                ", virtualVolume=" + virtualVolume +
                '}';
    }
}
