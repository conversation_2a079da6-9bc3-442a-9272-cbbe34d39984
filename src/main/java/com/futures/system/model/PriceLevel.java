package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 价格档位
 * 表示订单簿中某个价格的所有订单
 */
public class PriceLevel implements Serializable, Comparable<PriceLevel> {
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 总数量
     */
    private BigDecimal totalVolume;
    
    /**
     * 订单数量
     */
    private int orderCount;
    
    /**
     * 该价格档位的订单队列（按时间优先级排序）
     */
    private LinkedList<OrderInfo> orders;

    public PriceLevel(BigDecimal price) {
        this.price = price;
        this.totalVolume = BigDecimal.ZERO;
        this.orderCount = 0;
        this.orders = new LinkedList<>();
    }

    /**
     * 添加订单到价格档位
     */
    public void addOrder(OrderInfo order) {
        orders.add(order);
        totalVolume = totalVolume.add(order.getRemainingVolume());
        orderCount++;
    }

    /**
     * 从价格档位移除订单
     */
    public boolean removeOrder(String orderNumber) {
        for (int i = 0; i < orders.size(); i++) {
            OrderInfo order = orders.get(i);
            if (order.getOrderNumber().equals(orderNumber)) {
                orders.remove(i);
                totalVolume = totalVolume.subtract(order.getRemainingVolume());
                orderCount--;
                return true;
            }
        }
        return false;
    }

    /**
     * 更新订单数量
     */
    public boolean updateOrderVolume(String orderNumber, BigDecimal newRemainingVolume) {
        for (OrderInfo order : orders) {
            if (order.getOrderNumber().equals(orderNumber)) {
                BigDecimal oldVolume = order.getRemainingVolume();
                order.setRemainingVolume(newRemainingVolume);
                totalVolume = totalVolume.subtract(oldVolume).add(newRemainingVolume);
                
                // 如果剩余量为0，移除订单
                if (newRemainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    removeOrder(orderNumber);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 检查价格档位是否为空
     */
    public boolean isEmpty() {
        return orders.isEmpty() || totalVolume.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 获取第一个订单（时间优先级最高）
     */
    public OrderInfo getFirstOrder() {
        return orders.isEmpty() ? null : orders.getFirst();
    }

    /**
     * 清空价格档位
     */
    public void clear() {
        orders.clear();
        totalVolume = BigDecimal.ZERO;
        orderCount = 0;
    }

    /**
     * 深拷贝价格档位
     */
    public PriceLevel deepCopy() {
        PriceLevel copy = new PriceLevel(this.price);
        copy.totalVolume = this.totalVolume;
        copy.orderCount = this.orderCount;
        
        for (OrderInfo order : this.orders) {
            copy.orders.add(order.deepCopy());
        }
        
        return copy;
    }

    @Override
    public int compareTo(PriceLevel other) {
        return this.price.compareTo(other.price);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PriceLevel that = (PriceLevel) o;
        return Objects.equals(price, that.price);
    }

    @Override
    public int hashCode() {
        return Objects.hash(price);
    }

    // Getters and Setters
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public LinkedList<OrderInfo> getOrders() {
        return orders;
    }

    public void setOrders(LinkedList<OrderInfo> orders) {
        this.orders = orders;
    }

    @Override
    public String toString() {
        return "PriceLevel{" +
                "price=" + price +
                ", totalVolume=" + totalVolume +
                ", orderCount=" + orderCount +
                '}';
    }
}
