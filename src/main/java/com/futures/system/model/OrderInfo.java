package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 订单信息
 * 用于在订单簿中存储订单的关键信息
 */
public class OrderInfo implements Serializable {
    
    /**
     * 报单编号
     */
    private String orderNumber;
    
    /**
     * 合约代码
     */
    private String contractCode;
    
    /**
     * 委托价格
     */
    private BigDecimal orderPrice;
    
    /**
     * 委托数量
     */
    private BigDecimal orderVolume;
    
    /**
     * 剩余数量
     */
    private BigDecimal remainingVolume;
    
    /**
     * 已成交数量
     */
    private BigDecimal tradedVolume;
    
    /**
     * 买卖标志
     */
    private BuySellFlag buySellFlag;
    
    /**
     * 订单状态
     */
    private OrderStatus orderStatus;
    
    /**
     * 订单类型
     */
    private OrderType orderType;
    
    /**
     * 开平仓类型
     */
    private OpenCloseType openCloseType;
    
    /**
     * 报单时间
     */
    private LocalTime orderTime;
    
    /**
     * 会员编码
     */
    private String memberCode;
    
    /**
     * 交易编码
     */
    private String tradeCode;
    
    /**
     * 结算会员编码
     */
    private String settlementMemberCode;
    
    /**
     * 席位号
     */
    private String seatNumber;
    
    /**
     * 是否为组合订单
     */
    private boolean isCombinationOrder;
    
    /**
     * 组合订单的腿1合约代码（仅组合订单有效）
     */
    private String leg1ContractCode;
    
    /**
     * 组合订单的腿2合约代码（仅组合订单有效）
     */
    private String leg2ContractCode;

    // 构造函数
    public OrderInfo() {}

    public OrderInfo(String orderNumber, String contractCode, BigDecimal orderPrice, 
                    BigDecimal orderVolume, BuySellFlag buySellFlag, LocalTime orderTime) {
        this.orderNumber = orderNumber;
        this.contractCode = contractCode;
        this.orderPrice = orderPrice;
        this.orderVolume = orderVolume;
        this.remainingVolume = orderVolume;
        this.tradedVolume = BigDecimal.ZERO;
        this.buySellFlag = buySellFlag;
        this.orderTime = orderTime;
        this.orderStatus = OrderStatus.NOT_FILLED_IN_QUEUE;
        this.isCombinationOrder = false;
    }

    /**
     * 从单腿订单创建OrderInfo
     */
    public static OrderInfo fromSingleLegOrder(SingleLegOrder order) {
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.orderNumber = order.getOrderNumber();
        orderInfo.contractCode = order.getContractCode();
        orderInfo.orderPrice = order.getOrderPrice();
        orderInfo.orderVolume = order.getOrderVolume();
        orderInfo.remainingVolume = order.getRemainingVolume();
        orderInfo.tradedVolume = order.getTradedVolume();
        orderInfo.buySellFlag = order.getBuySellFlag();
        orderInfo.orderStatus = order.getOrderStatus();
        orderInfo.orderType = order.getOrderType();
        orderInfo.openCloseType = order.getOpenCloseType();
        orderInfo.orderTime = order.getOrderTime();
        orderInfo.memberCode = order.getMemberCode();
        orderInfo.tradeCode = order.getTradeCode();
        orderInfo.settlementMemberCode = order.getSettlementMemberCode();
        orderInfo.seatNumber = order.getSeatNumber();
        orderInfo.isCombinationOrder = false;
        return orderInfo;
    }

    /**
     * 从组合订单创建OrderInfo
     */
    public static OrderInfo fromCombinationOrder(CombinationOrder order) {
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.orderNumber = order.getOrderNumber();
        orderInfo.contractCode = order.getCombinationContractCode();
        orderInfo.orderPrice = order.getOrderPrice();
        orderInfo.orderVolume = order.getOrderVolume();
        orderInfo.remainingVolume = order.getRemainingVolume();
        orderInfo.tradedVolume = order.getTradedVolume();
        orderInfo.buySellFlag = order.getBuySellFlag();
        orderInfo.orderStatus = order.getOrderStatus();
        orderInfo.orderType = order.getOrderType();
        orderInfo.openCloseType = order.getOpenCloseType();
        orderInfo.orderTime = order.getOrderTime();
        orderInfo.memberCode = order.getMemberCode();
        orderInfo.tradeCode = order.getTradeCode();
        orderInfo.settlementMemberCode = order.getSettlementMemberCode();
        orderInfo.seatNumber = order.getSeatNumber();
        orderInfo.isCombinationOrder = true;
        orderInfo.leg1ContractCode = order.getLeg1ContractCode();
        orderInfo.leg2ContractCode = order.getLeg2ContractCode();
        return orderInfo;
    }

    /**
     * 更新订单状态和数量
     */
    public void updateFromTrade(BigDecimal tradeVolume) {
        this.tradedVolume = this.tradedVolume.add(tradeVolume);
        this.remainingVolume = this.orderVolume.subtract(this.tradedVolume);
        
        if (this.remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
            this.orderStatus = OrderStatus.FULLY_FILLED;
            this.remainingVolume = BigDecimal.ZERO;
        } else {
            this.orderStatus = OrderStatus.PARTIALLY_FILLED_IN_QUEUE;
        }
    }

    /**
     * 撤销订单
     */
    public void cancel() {
        this.orderStatus = OrderStatus.CANCELLED;
        this.remainingVolume = BigDecimal.ZERO;
    }

    /**
     * 深拷贝订单信息
     */
    public OrderInfo deepCopy() {
        OrderInfo copy = new OrderInfo();
        copy.orderNumber = this.orderNumber;
        copy.contractCode = this.contractCode;
        copy.orderPrice = this.orderPrice;
        copy.orderVolume = this.orderVolume;
        copy.remainingVolume = this.remainingVolume;
        copy.tradedVolume = this.tradedVolume;
        copy.buySellFlag = this.buySellFlag;
        copy.orderStatus = this.orderStatus;
        copy.orderType = this.orderType;
        copy.openCloseType = this.openCloseType;
        copy.orderTime = this.orderTime;
        copy.memberCode = this.memberCode;
        copy.tradeCode = this.tradeCode;
        copy.settlementMemberCode = this.settlementMemberCode;
        copy.seatNumber = this.seatNumber;
        copy.isCombinationOrder = this.isCombinationOrder;
        copy.leg1ContractCode = this.leg1ContractCode;
        copy.leg2ContractCode = this.leg2ContractCode;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderInfo orderInfo = (OrderInfo) o;
        return Objects.equals(orderNumber, orderInfo.orderNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderNumber);
    }

    // Getters and Setters
    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public BigDecimal getOrderVolume() {
        return orderVolume;
    }

    public void setOrderVolume(BigDecimal orderVolume) {
        this.orderVolume = orderVolume;
    }

    public BigDecimal getRemainingVolume() {
        return remainingVolume;
    }

    public void setRemainingVolume(BigDecimal remainingVolume) {
        this.remainingVolume = remainingVolume;
    }

    public BigDecimal getTradedVolume() {
        return tradedVolume;
    }

    public void setTradedVolume(BigDecimal tradedVolume) {
        this.tradedVolume = tradedVolume;
    }

    public BuySellFlag getBuySellFlag() {
        return buySellFlag;
    }

    public void setBuySellFlag(BuySellFlag buySellFlag) {
        this.buySellFlag = buySellFlag;
    }

    public OrderStatus getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(OrderStatus orderStatus) {
        this.orderStatus = orderStatus;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public OpenCloseType getOpenCloseType() {
        return openCloseType;
    }

    public void setOpenCloseType(OpenCloseType openCloseType) {
        this.openCloseType = openCloseType;
    }

    public LocalTime getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(LocalTime orderTime) {
        this.orderTime = orderTime;
    }

    public String getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public String getSeatNumber() {
        return seatNumber;
    }

    public void setSeatNumber(String seatNumber) {
        this.seatNumber = seatNumber;
    }

    public boolean isCombinationOrder() {
        return isCombinationOrder;
    }

    public void setCombinationOrder(boolean combinationOrder) {
        isCombinationOrder = combinationOrder;
    }

    public String getLeg1ContractCode() {
        return leg1ContractCode;
    }

    public void setLeg1ContractCode(String leg1ContractCode) {
        this.leg1ContractCode = leg1ContractCode;
    }

    public String getLeg2ContractCode() {
        return leg2ContractCode;
    }

    public void setLeg2ContractCode(String leg2ContractCode) {
        this.leg2ContractCode = leg2ContractCode;
    }

    @Override
    public String toString() {
        return "OrderInfo{" +
                "orderNumber='" + orderNumber + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", orderPrice=" + orderPrice +
                ", remainingVolume=" + remainingVolume +
                ", buySellFlag=" + buySellFlag +
                ", orderStatus=" + orderStatus +
                ", orderTime=" + orderTime +
                ", isCombinationOrder=" + isCombinationOrder +
                '}';
    }
}
