package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 盈亏计算结果
 * 按结算会员ID计算的日内盈亏
 */
public class PnLCalculation implements Serializable {
    
    /**
     * 结算会员编码
     */
    @JsonProperty("settlement_member_code")
    private String settlementMemberCode;
    
    /**
     * 交易日期
     */
    @JsonProperty("trade_date")
    private LocalDate tradeDate;
    
    /**
     * 合约代码
     */
    @JsonProperty("contract_code")
    private String contractCode;
    
    /**
     * 品种代码
     */
    @JsonProperty("commodity_code")
    private String commodityCode;
    
    /**
     * 交易所编码
     */
    @JsonProperty("exchange_code")
    private String exchangeCode;
    
    /**
     * 开仓数量
     */
    @JsonProperty("open_volume")
    private BigDecimal openVolume;
    
    /**
     * 平仓数量
     */
    @JsonProperty("close_volume")
    private BigDecimal closeVolume;
    
    /**
     * 当前持仓量
     */
    @JsonProperty("current_position")
    private BigDecimal currentPosition;
    
    /**
     * 开仓均价
     */
    @JsonProperty("average_open_price")
    private BigDecimal averageOpenPrice;
    
    /**
     * 最新成交价
     */
    @JsonProperty("latest_trade_price")
    private BigDecimal latestTradePrice;
    
    /**
     * 已实现盈亏
     */
    @JsonProperty("realized_pnl")
    private BigDecimal realizedPnL;
    
    /**
     * 浮动盈亏
     */
    @JsonProperty("unrealized_pnl")
    private BigDecimal unrealizedPnL;
    
    /**
     * 总盈亏
     */
    @JsonProperty("total_pnl")
    private BigDecimal totalPnL;
    
    /**
     * 开仓金额
     */
    @JsonProperty("open_amount")
    private BigDecimal openAmount;
    
    /**
     * 平仓金额
     */
    @JsonProperty("close_amount")
    private BigDecimal closeAmount;
    
    /**
     * 净买卖方向（多头为正，空头为负）
     */
    @JsonProperty("net_position_side")
    private BigDecimal netPositionSide;
    
    /**
     * 计算时间
     */
    @JsonProperty("calculation_time")
    private LocalDateTime calculationTime;
    
    /**
     * 更新序列号
     */
    @JsonProperty("update_sequence")
    private Long updateSequence;

    // 构造函数
    public PnLCalculation() {
        this.openVolume = BigDecimal.ZERO;
        this.closeVolume = BigDecimal.ZERO;
        this.currentPosition = BigDecimal.ZERO;
        this.averageOpenPrice = BigDecimal.ZERO;
        this.latestTradePrice = BigDecimal.ZERO;
        this.realizedPnL = BigDecimal.ZERO;
        this.unrealizedPnL = BigDecimal.ZERO;
        this.totalPnL = BigDecimal.ZERO;
        this.openAmount = BigDecimal.ZERO;
        this.closeAmount = BigDecimal.ZERO;
        this.netPositionSide = BigDecimal.ZERO;
        this.calculationTime = LocalDateTime.now();
    }

    public PnLCalculation(String settlementMemberCode, LocalDate tradeDate, String contractCode) {
        this();
        this.settlementMemberCode = settlementMemberCode;
        this.tradeDate = tradeDate;
        this.contractCode = contractCode;
    }

    /**
     * 处理开仓交易
     */
    public void processOpenTrade(BigDecimal volume, BigDecimal price, BuySellFlag side) {
        BigDecimal signedVolume = side == BuySellFlag.BUY ? volume : volume.negate();
        BigDecimal tradeAmount = volume.multiply(price);
        
        // 更新开仓信息
        this.openVolume = this.openVolume.add(volume);
        this.openAmount = this.openAmount.add(tradeAmount);
        
        // 更新持仓
        BigDecimal oldPosition = this.currentPosition;
        this.currentPosition = this.currentPosition.add(signedVolume);
        
        // 重新计算开仓均价
        if (this.currentPosition.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal totalOpenAmount = oldPosition.multiply(this.averageOpenPrice).add(signedVolume.multiply(price));
            this.averageOpenPrice = totalOpenAmount.divide(this.currentPosition, 6, BigDecimal.ROUND_HALF_UP);
        }
        
        this.netPositionSide = this.currentPosition;
        this.latestTradePrice = price;
        this.calculationTime = LocalDateTime.now();
        
        // 重新计算盈亏
        calculatePnL();
    }

    /**
     * 处理平仓交易
     */
    public void processCloseTrade(BigDecimal volume, BigDecimal price, BuySellFlag side) {
        BigDecimal signedVolume = side == BuySellFlag.BUY ? volume : volume.negate();
        BigDecimal tradeAmount = volume.multiply(price);
        
        // 更新平仓信息
        this.closeVolume = this.closeVolume.add(volume);
        this.closeAmount = this.closeAmount.add(tradeAmount);
        
        // 计算平仓盈亏
        BigDecimal closePnL = signedVolume.multiply(price.subtract(this.averageOpenPrice));
        this.realizedPnL = this.realizedPnL.add(closePnL);
        
        // 更新持仓
        this.currentPosition = this.currentPosition.add(signedVolume);
        this.netPositionSide = this.currentPosition;
        this.latestTradePrice = price;
        this.calculationTime = LocalDateTime.now();
        
        // 重新计算盈亏
        calculatePnL();
    }

    /**
     * 计算盈亏
     */
    private void calculatePnL() {
        // 计算浮动盈亏
        if (this.currentPosition.compareTo(BigDecimal.ZERO) != 0 && this.latestTradePrice.compareTo(BigDecimal.ZERO) != 0) {
            this.unrealizedPnL = this.currentPosition.multiply(this.latestTradePrice.subtract(this.averageOpenPrice));
        } else {
            this.unrealizedPnL = BigDecimal.ZERO;
        }
        
        // 计算总盈亏
        this.totalPnL = this.realizedPnL.add(this.unrealizedPnL);
    }

    /**
     * 更新最新价格（用于重新计算浮动盈亏）
     */
    public void updateLatestPrice(BigDecimal latestPrice) {
        this.latestTradePrice = latestPrice;
        calculatePnL();
        this.calculationTime = LocalDateTime.now();
    }

    /**
     * 深拷贝盈亏计算对象
     */
    public PnLCalculation deepCopy() {
        PnLCalculation copy = new PnLCalculation();
        copy.settlementMemberCode = this.settlementMemberCode;
        copy.tradeDate = this.tradeDate;
        copy.contractCode = this.contractCode;
        copy.commodityCode = this.commodityCode;
        copy.exchangeCode = this.exchangeCode;
        copy.openVolume = this.openVolume;
        copy.closeVolume = this.closeVolume;
        copy.currentPosition = this.currentPosition;
        copy.averageOpenPrice = this.averageOpenPrice;
        copy.latestTradePrice = this.latestTradePrice;
        copy.realizedPnL = this.realizedPnL;
        copy.unrealizedPnL = this.unrealizedPnL;
        copy.totalPnL = this.totalPnL;
        copy.openAmount = this.openAmount;
        copy.closeAmount = this.closeAmount;
        copy.netPositionSide = this.netPositionSide;
        copy.calculationTime = this.calculationTime;
        copy.updateSequence = this.updateSequence;
        return copy;
    }

    // Getters and Setters
    public String getSettlementMemberCode() {
        return settlementMemberCode;
    }

    public void setSettlementMemberCode(String settlementMemberCode) {
        this.settlementMemberCode = settlementMemberCode;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public String getExchangeCode() {
        return exchangeCode;
    }

    public void setExchangeCode(String exchangeCode) {
        this.exchangeCode = exchangeCode;
    }

    public BigDecimal getOpenVolume() {
        return openVolume;
    }

    public void setOpenVolume(BigDecimal openVolume) {
        this.openVolume = openVolume;
    }

    public BigDecimal getCloseVolume() {
        return closeVolume;
    }

    public void setCloseVolume(BigDecimal closeVolume) {
        this.closeVolume = closeVolume;
    }

    public BigDecimal getCurrentPosition() {
        return currentPosition;
    }

    public void setCurrentPosition(BigDecimal currentPosition) {
        this.currentPosition = currentPosition;
    }

    public BigDecimal getAverageOpenPrice() {
        return averageOpenPrice;
    }

    public void setAverageOpenPrice(BigDecimal averageOpenPrice) {
        this.averageOpenPrice = averageOpenPrice;
    }

    public BigDecimal getLatestTradePrice() {
        return latestTradePrice;
    }

    public void setLatestTradePrice(BigDecimal latestTradePrice) {
        this.latestTradePrice = latestTradePrice;
    }

    public BigDecimal getRealizedPnL() {
        return realizedPnL;
    }

    public void setRealizedPnL(BigDecimal realizedPnL) {
        this.realizedPnL = realizedPnL;
    }

    public BigDecimal getUnrealizedPnL() {
        return unrealizedPnL;
    }

    public void setUnrealizedPnL(BigDecimal unrealizedPnL) {
        this.unrealizedPnL = unrealizedPnL;
    }

    public BigDecimal getTotalPnL() {
        return totalPnL;
    }

    public void setTotalPnL(BigDecimal totalPnL) {
        this.totalPnL = totalPnL;
    }

    public BigDecimal getOpenAmount() {
        return openAmount;
    }

    public void setOpenAmount(BigDecimal openAmount) {
        this.openAmount = openAmount;
    }

    public BigDecimal getCloseAmount() {
        return closeAmount;
    }

    public void setCloseAmount(BigDecimal closeAmount) {
        this.closeAmount = closeAmount;
    }

    public BigDecimal getNetPositionSide() {
        return netPositionSide;
    }

    public void setNetPositionSide(BigDecimal netPositionSide) {
        this.netPositionSide = netPositionSide;
    }

    public LocalDateTime getCalculationTime() {
        return calculationTime;
    }

    public void setCalculationTime(LocalDateTime calculationTime) {
        this.calculationTime = calculationTime;
    }

    public Long getUpdateSequence() {
        return updateSequence;
    }

    public void setUpdateSequence(Long updateSequence) {
        this.updateSequence = updateSequence;
    }

    @Override
    public String toString() {
        return "PnLCalculation{" +
                "settlementMemberCode='" + settlementMemberCode + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", currentPosition=" + currentPosition +
                ", averageOpenPrice=" + averageOpenPrice +
                ", latestTradePrice=" + latestTradePrice +
                ", realizedPnL=" + realizedPnL +
                ", unrealizedPnL=" + unrealizedPnL +
                ", totalPnL=" + totalPnL +
                ", calculationTime=" + calculationTime +
                '}';
    }
}
