package com.futures.system.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约价格信息
 * 用于缓存腿合约的最优买卖价格，支持组合价格计算
 */
public class ContractPriceInfo implements Serializable {
    
    /**
     * 合约代码
     */
    private String contractCode;
    
    /**
     * 最优买价
     */
    private BigDecimal bestBidPrice;
    
    /**
     * 最优卖价
     */
    private BigDecimal bestAskPrice;
    
    /**
     * 最优买量
     */
    private BigDecimal bestBidVolume;
    
    /**
     * 最优卖量
     */
    private BigDecimal bestAskVolume;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 价格有效性标志
     */
    private boolean isValid;
    
    /**
     * 价格来源
     */
    private PriceSource priceSource;

    // 构造函数
    public ContractPriceInfo(String contractCode) {
        this.contractCode = contractCode;
        this.lastUpdateTime = LocalDateTime.now();
        this.isValid = false;
        this.priceSource = PriceSource.ORDER_BOOK;
        this.bestBidVolume = BigDecimal.ZERO;
        this.bestAskVolume = BigDecimal.ZERO;
    }

    /**
     * 更新买价信息
     */
    public void updateBidPrice(BigDecimal price, BigDecimal volume) {
        this.bestBidPrice = price;
        this.bestBidVolume = volume != null ? volume : BigDecimal.ZERO;
        this.lastUpdateTime = LocalDateTime.now();
        updateValidityStatus();
    }

    /**
     * 更新卖价信息
     */
    public void updateAskPrice(BigDecimal price, BigDecimal volume) {
        this.bestAskPrice = price;
        this.bestAskVolume = volume != null ? volume : BigDecimal.ZERO;
        this.lastUpdateTime = LocalDateTime.now();
        updateValidityStatus();
    }

    /**
     * 同时更新买卖价信息
     */
    public void updatePrices(BigDecimal bidPrice, BigDecimal bidVolume, 
                           BigDecimal askPrice, BigDecimal askVolume) {
        this.bestBidPrice = bidPrice;
        this.bestBidVolume = bidVolume != null ? bidVolume : BigDecimal.ZERO;
        this.bestAskPrice = askPrice;
        this.bestAskVolume = askVolume != null ? askVolume : BigDecimal.ZERO;
        this.lastUpdateTime = LocalDateTime.now();
        updateValidityStatus();
    }

    /**
     * 更新有效性状态
     */
    private void updateValidityStatus() {
        this.isValid = (bestBidPrice != null && bestBidPrice.compareTo(BigDecimal.ZERO) > 0) ||
                      (bestAskPrice != null && bestAskPrice.compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 获取中间价
     */
    public BigDecimal getMidPrice() {
        if (bestBidPrice != null && bestAskPrice != null) {
            return bestBidPrice.add(bestAskPrice).divide(BigDecimal.valueOf(2), 6, BigDecimal.ROUND_HALF_UP);
        } else if (bestBidPrice != null) {
            return bestBidPrice;
        } else if (bestAskPrice != null) {
            return bestAskPrice;
        }
        return null;
    }

    /**
     * 获取买卖价差
     */
    public BigDecimal getSpread() {
        if (bestBidPrice != null && bestAskPrice != null) {
            return bestAskPrice.subtract(bestBidPrice);
        }
        return null;
    }

    /**
     * 检查价格是否过期
     */
    public boolean isExpired(long maxAgeMillis) {
        if (lastUpdateTime == null) {
            return true;
        }
        
        LocalDateTime expireTime = lastUpdateTime.plusNanos(maxAgeMillis * 1_000_000);
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 检查价格是否合理
     */
    public boolean isPriceReasonable() {
        // 检查买价不能大于卖价
        if (bestBidPrice != null && bestAskPrice != null) {
            return bestBidPrice.compareTo(bestAskPrice) <= 0;
        }
        
        // 检查价格为正数
        if (bestBidPrice != null && bestBidPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (bestAskPrice != null && bestAskPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取指定方向的价格
     */
    public BigDecimal getPrice(BuySellFlag side) {
        if (side == BuySellFlag.BUY) {
            return bestAskPrice; // 买入时使用卖价
        } else {
            return bestBidPrice; // 卖出时使用买价
        }
    }

    /**
     * 获取指定方向的数量
     */
    public BigDecimal getVolume(BuySellFlag side) {
        if (side == BuySellFlag.BUY) {
            return bestAskVolume; // 买入时使用卖量
        } else {
            return bestBidVolume; // 卖出时使用买量
        }
    }

    /**
     * 重置价格信息
     */
    public void reset() {
        this.bestBidPrice = null;
        this.bestAskPrice = null;
        this.bestBidVolume = BigDecimal.ZERO;
        this.bestAskVolume = BigDecimal.ZERO;
        this.isValid = false;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 深拷贝价格信息
     */
    public ContractPriceInfo deepCopy() {
        ContractPriceInfo copy = new ContractPriceInfo(this.contractCode);
        copy.bestBidPrice = this.bestBidPrice;
        copy.bestAskPrice = this.bestAskPrice;
        copy.bestBidVolume = this.bestBidVolume;
        copy.bestAskVolume = this.bestAskVolume;
        copy.lastUpdateTime = this.lastUpdateTime;
        copy.isValid = this.isValid;
        copy.priceSource = this.priceSource;
        return copy;
    }

    // Getters and Setters
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public BigDecimal getBestBidPrice() {
        return bestBidPrice;
    }

    public void setBestBidPrice(BigDecimal bestBidPrice) {
        this.bestBidPrice = bestBidPrice;
        updateValidityStatus();
    }

    public BigDecimal getBestAskPrice() {
        return bestAskPrice;
    }

    public void setBestAskPrice(BigDecimal bestAskPrice) {
        this.bestAskPrice = bestAskPrice;
        updateValidityStatus();
    }

    public BigDecimal getBestBidVolume() {
        return bestBidVolume;
    }

    public void setBestBidVolume(BigDecimal bestBidVolume) {
        this.bestBidVolume = bestBidVolume;
    }

    public BigDecimal getBestAskVolume() {
        return bestAskVolume;
    }

    public void setBestAskVolume(BigDecimal bestAskVolume) {
        this.bestAskVolume = bestAskVolume;
    }

    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public PriceSource getPriceSource() {
        return priceSource;
    }

    public void setPriceSource(PriceSource priceSource) {
        this.priceSource = priceSource;
    }

    @Override
    public String toString() {
        return String.format("ContractPriceInfo{contract='%s', bid=%s(%s), ask=%s(%s), valid=%s, updated=%s}",
                contractCode, bestBidPrice, bestBidVolume, bestAskPrice, bestAskVolume, 
                isValid, lastUpdateTime);
    }

    /**
     * 价格来源枚举
     */
    public enum PriceSource {
        ORDER_BOOK("订单簿"),
        LAST_TRADE("最新成交"),
        MARKET_DATA("行情数据"),
        ESTIMATED("估算价格");

        private final String description;

        PriceSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
