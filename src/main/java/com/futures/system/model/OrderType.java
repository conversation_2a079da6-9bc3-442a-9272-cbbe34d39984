package com.futures.system.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 订单类型枚举
 * 对应数据字段 ord_type
 */
public enum OrderType {
    /**
     * 0 = 正常
     */
    NORMAL("0", "正常"),
    
    /**
     * 1 = 报价衍生
     */
    QUOTE_DERIVED("1", "报价衍生"),
    
    /**
     * 2 = 组合衍生
     */
    COMBINATION_DERIVED("2", "组合衍生"),
    
    /**
     * 3 = 组合/套利
     */
    COMBINATION_ARBITRAGE("3", "组合/套利");

    private final String code;
    private final String description;

    OrderType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @JsonCreator
    public static OrderType fromCode(String code) {
        for (OrderType type : OrderType.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown order type code: " + code);
    }
}
