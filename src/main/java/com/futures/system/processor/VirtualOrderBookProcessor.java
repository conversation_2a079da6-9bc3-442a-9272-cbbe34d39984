package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 虚拟层组合订单处理器
 * 处理组合委托订单和基础订单簿，生成包含虚拟层的合并订单簿
 */
public class VirtualOrderBookProcessor extends KeyedCoProcessFunction<String, BaseOrderBook, CombinationOrder, VirtualOrderBook> {
    
    private static final Logger LOG = LoggerFactory.getLogger(VirtualOrderBookProcessor.class);
    
    /**
     * 虚拟订单簿状态（按合约代码分区）
     */
    private transient MapState<String, VirtualOrderBook> virtualOrderBookState;
    
    /**
     * 组合订单状态（按报单编号索引）
     */
    private transient MapState<String, CombinationOrderInfo> combinationOrderState;
    
    /**
     * 腿合约价格缓存
     */
    private transient MapState<String, ContractPriceInfo> legContractPriceState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化虚拟订单簿状态
        MapStateDescriptor<String, VirtualOrderBook> virtualOrderBookDescriptor = new MapStateDescriptor<>(
                "virtualOrderBookState",
                TypeInformation.of(String.class),
                TypeInformation.of(VirtualOrderBook.class)
        );
        virtualOrderBookState = getRuntimeContext().getMapState(virtualOrderBookDescriptor);
        
        // 初始化组合订单状态
        MapStateDescriptor<String, CombinationOrderInfo> combinationOrderDescriptor = new MapStateDescriptor<>(
                "combinationOrderState",
                TypeInformation.of(String.class),
                TypeInformation.of(CombinationOrderInfo.class)
        );
        combinationOrderState = getRuntimeContext().getMapState(combinationOrderDescriptor);
        
        // 初始化腿合约价格状态
        MapStateDescriptor<String, ContractPriceInfo> legContractPriceDescriptor = new MapStateDescriptor<>(
                "legContractPriceState",
                TypeInformation.of(String.class),
                TypeInformation.of(ContractPriceInfo.class)
        );
        legContractPriceState = getRuntimeContext().getMapState(legContractPriceDescriptor);
    }
    
    @Override
    public void processElement1(BaseOrderBook baseOrderBook, Context ctx, Collector<VirtualOrderBook> out) 
            throws Exception {
        
        if (baseOrderBook == null || baseOrderBook.getContractCode() == null) {
            LOG.warn("Invalid base order book received: {}", baseOrderBook);
            return;
        }
        
        try {
            // 获取或创建虚拟订单簿
            VirtualOrderBook virtualOrderBook = getOrCreateVirtualOrderBook(baseOrderBook);
            
            // 更新腿合约价格信息
            updateLegContractPrice(baseOrderBook);
            
            // 输出更新后的虚拟订单簿
            out.collect(virtualOrderBook);
            
            LOG.debug("Updated virtual order book for contract {} with base order book", 
                    baseOrderBook.getContractCode());
            
        } catch (Exception e) {
            LOG.error("Error processing base order book: {}", baseOrderBook, e);
        }
    }
    
    @Override
    public void processElement2(CombinationOrder combinationOrder, Context ctx, Collector<VirtualOrderBook> out) 
            throws Exception {
        
        if (combinationOrder == null || combinationOrder.getCombinationContractCode() == null) {
            LOG.warn("Invalid combination order received: {}", combinationOrder);
            return;
        }
        
        try {
            // 确定目标合约（通常是腿1合约）
            String targetContract = combinationOrder.getLeg1ContractCode();
            if (targetContract == null) {
                LOG.warn("No target contract found for combination order: {}", combinationOrder);
                return;
            }
            
            // 获取虚拟订单簿
            VirtualOrderBook virtualOrderBook = virtualOrderBookState.get(targetContract);
            if (virtualOrderBook == null) {
                LOG.debug("No virtual order book found for contract {}, creating placeholder", targetContract);
                // 创建一个空的基础订单簿作为占位符
                BaseOrderBook emptyBase = new BaseOrderBook(targetContract);
                virtualOrderBook = new VirtualOrderBook(emptyBase);
                virtualOrderBookState.put(targetContract, virtualOrderBook);
            }
            
            // 处理组合订单
            boolean updated = processCombinationOrderUpdate(virtualOrderBook, combinationOrder);
            
            if (updated) {
                // 保存更新后的虚拟订单簿
                virtualOrderBookState.put(targetContract, virtualOrderBook);
                
                // 输出更新后的虚拟订单簿
                out.collect(virtualOrderBook);
                
                LOG.debug("Updated virtual order book for contract {} with combination order: {}", 
                        targetContract, combinationOrder.getOrderNumber());
            }
            
        } catch (Exception e) {
            LOG.error("Error processing combination order: {}", combinationOrder, e);
        }
    }
    
    /**
     * 获取或创建虚拟订单簿
     */
    private VirtualOrderBook getOrCreateVirtualOrderBook(BaseOrderBook baseOrderBook) throws Exception {
        String contractCode = baseOrderBook.getContractCode();
        VirtualOrderBook virtualOrderBook = virtualOrderBookState.get(contractCode);
        
        if (virtualOrderBook == null) {
            virtualOrderBook = new VirtualOrderBook(baseOrderBook);
            virtualOrderBookState.put(contractCode, virtualOrderBook);
            LOG.info("Created new virtual order book for contract: {}", contractCode);
        } else {
            // 更新基础订单簿
            virtualOrderBook = new VirtualOrderBook(baseOrderBook);
            
            // 恢复虚拟层数据
            VirtualOrderBook existingVirtual = virtualOrderBookState.get(contractCode);
            if (existingVirtual != null) {
                restoreVirtualLayer(virtualOrderBook, existingVirtual);
            }
            
            virtualOrderBookState.put(contractCode, virtualOrderBook);
        }
        
        return virtualOrderBook;
    }
    
    /**
     * 恢复虚拟层数据
     */
    private void restoreVirtualLayer(VirtualOrderBook newVirtualBook, VirtualOrderBook existingVirtual) {
        // 恢复组合订单
        for (CombinationOrderInfo orderInfo : existingVirtual.getCombinationOrderIndex().values()) {
            newVirtualBook.addCombinationOrder(orderInfo);
        }
        
        // 恢复腿合约价格
        for (ContractPriceInfo priceInfo : existingVirtual.getLegContractPrices().values()) {
            newVirtualBook.updateLegContractPrice(
                    priceInfo.getContractCode(),
                    priceInfo.getBestBidPrice(),
                    priceInfo.getBestAskPrice()
            );
        }
    }
    
    /**
     * 更新腿合约价格信息
     */
    private void updateLegContractPrice(BaseOrderBook baseOrderBook) throws Exception {
        String contractCode = baseOrderBook.getContractCode();
        ContractPriceInfo priceInfo = legContractPriceState.get(contractCode);
        
        if (priceInfo == null) {
            priceInfo = new ContractPriceInfo(contractCode);
        }
        
        // 更新价格信息
        priceInfo.updatePrices(
                baseOrderBook.getBestBidPrice(),
                baseOrderBook.getTotalBidVolume(),
                baseOrderBook.getBestAskPrice(),
                baseOrderBook.getTotalAskVolume()
        );
        
        legContractPriceState.put(contractCode, priceInfo);
        
        // 通知所有相关的虚拟订单簿
        notifyVirtualOrderBooksOfPriceUpdate(contractCode, priceInfo);
    }
    
    /**
     * 通知虚拟订单簿价格更新
     */
    private void notifyVirtualOrderBooksOfPriceUpdate(String contractCode, ContractPriceInfo priceInfo) throws Exception {
        // 遍历所有虚拟订单簿，找到使用此合约作为腿的组合订单
        for (String virtualContractCode : virtualOrderBookState.keys()) {
            VirtualOrderBook virtualOrderBook = virtualOrderBookState.get(virtualContractCode);
            if (virtualOrderBook != null) {
                virtualOrderBook.updateLegContractPrice(
                        contractCode,
                        priceInfo.getBestBidPrice(),
                        priceInfo.getBestAskPrice()
                );
                virtualOrderBookState.put(virtualContractCode, virtualOrderBook);
            }
        }
    }
    
    /**
     * 处理组合订单更新
     */
    private boolean processCombinationOrderUpdate(VirtualOrderBook virtualOrderBook, CombinationOrder order) throws Exception {
        String orderNumber = order.getOrderNumber();
        CombinationOrderInfo existingOrder = combinationOrderState.get(orderNumber);
        CombinationOrderInfo newOrderInfo = CombinationOrderInfo.fromCombinationOrder(order);
        
        boolean updated = false;
        
        if (existingOrder == null) {
            // 新组合订单
            updated = handleNewCombinationOrder(virtualOrderBook, newOrderInfo);
        } else {
            // 组合订单更新
            updated = handleCombinationOrderUpdate(virtualOrderBook, existingOrder, newOrderInfo);
        }
        
        if (updated) {
            // 更新组合订单状态
            if (newOrderInfo.getOrderStatus().isFinished()) {
                combinationOrderState.remove(orderNumber);
            } else {
                combinationOrderState.put(orderNumber, newOrderInfo);
            }
        }
        
        return updated;
    }
    
    /**
     * 处理新组合订单
     */
    private boolean handleNewCombinationOrder(VirtualOrderBook virtualOrderBook, CombinationOrderInfo orderInfo) {
        if (!orderInfo.getOrderStatus().isInQueue()) {
            LOG.debug("New combination order not in queue, ignoring: {}", orderInfo);
            return false;
        }
        
        boolean success = virtualOrderBook.addCombinationOrder(orderInfo);
        if (success) {
            LOG.debug("Added new combination order to virtual book: {}", orderInfo);
        } else {
            LOG.warn("Failed to add new combination order: {}", orderInfo);
        }
        
        return success;
    }
    
    /**
     * 处理组合订单更新
     */
    private boolean handleCombinationOrderUpdate(VirtualOrderBook virtualOrderBook, 
                                                CombinationOrderInfo existingOrder, 
                                                CombinationOrderInfo newOrderInfo) {
        OrderStatus oldStatus = existingOrder.getOrderStatus();
        OrderStatus newStatus = newOrderInfo.getOrderStatus();
        
        // 检查订单状态变化
        if (oldStatus != newStatus) {
            if (newStatus.isFinished()) {
                // 订单完成，从虚拟订单簿移除
                boolean success = virtualOrderBook.removeCombinationOrder(existingOrder.getOrderNumber());
                if (success) {
                    LOG.debug("Removed finished combination order: {} (status: {})", 
                            existingOrder.getOrderNumber(), newStatus);
                }
                return success;
            } else if (newStatus.isInQueue() && !oldStatus.isInQueue()) {
                // 订单重新进入队列
                return virtualOrderBook.addCombinationOrder(newOrderInfo);
            } else if (!newStatus.isInQueue() && oldStatus.isInQueue()) {
                // 订单离开队列
                return virtualOrderBook.removeCombinationOrder(existingOrder.getOrderNumber());
            }
        }
        
        // 检查数量变化
        if (newStatus.isInQueue() && 
            !existingOrder.getRemainingVolume().equals(newOrderInfo.getRemainingVolume())) {
            return virtualOrderBook.updateCombinationOrder(newOrderInfo);
        }
        
        return false;
    }
}
