package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 盈亏汇总处理器
 * 按结算会员汇总所有合约的盈亏情况
 */
public class PnLAggregationProcessor extends KeyedProcessFunction<String, PnLCalculation, PnLSummary> {
    
    private static final Logger LOG = LoggerFactory.getLogger(PnLAggregationProcessor.class);
    
    /**
     * 按合约的盈亏计算状态
     */
    private transient MapState<String, PnLCalculation> contractPnLMap;
    
    /**
     * 最后汇总时间
     */
    private transient ValueState<LocalDateTime> lastSummaryTimeState;
    
    /**
     * 汇总序列号
     */
    private transient ValueState<Long> summarySequenceState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化合约盈亏状态
        MapStateDescriptor<String, PnLCalculation> contractPnLDescriptor = new MapStateDescriptor<>(
                "contractPnLMap",
                TypeInformation.of(String.class),
                TypeInformation.of(PnLCalculation.class)
        );
        contractPnLMap = getRuntimeContext().getMapState(contractPnLDescriptor);
        
        // 初始化最后汇总时间
        ValueStateDescriptor<LocalDateTime> lastSummaryTimeDescriptor = new ValueStateDescriptor<>(
                "lastSummaryTime",
                TypeInformation.of(LocalDateTime.class)
        );
        lastSummaryTimeState = getRuntimeContext().getState(lastSummaryTimeDescriptor);
        
        // 初始化汇总序列号
        ValueStateDescriptor<Long> summarySequenceDescriptor = new ValueStateDescriptor<>(
                "summarySequence",
                TypeInformation.of(Long.class)
        );
        summarySequenceState = getRuntimeContext().getState(summarySequenceDescriptor);
    }
    
    @Override
    public void processElement(PnLCalculation calculation, Context ctx, Collector<PnLSummary> out) 
            throws Exception {
        
        if (calculation == null || calculation.getSettlementMemberCode() == null || 
            calculation.getContractCode() == null) {
            LOG.warn("Invalid PnL calculation received: {}", calculation);
            return;
        }
        
        try {
            String contractCode = calculation.getContractCode();
            
            // 更新合约盈亏
            contractPnLMap.put(contractCode, calculation);
            
            // 生成汇总报告
            PnLSummary summary = generateSummary(calculation.getSettlementMemberCode());
            
            // 输出汇总结果
            out.collect(summary);
            
            LOG.debug("Generated PnL summary for member {}: totalPnL={}, contracts={}", 
                    calculation.getSettlementMemberCode(), summary.getTotalPnL(), 
                    summary.getContractPnLMap().size());
            
        } catch (Exception e) {
            LOG.error("Error processing PnL calculation for aggregation: {}", calculation, e);
        }
    }
    
    /**
     * 生成盈亏汇总
     */
    private PnLSummary generateSummary(String settlementMemberCode) throws Exception {
        PnLSummary summary = new PnLSummary(settlementMemberCode);
        
        // 设置汇总元数据
        summary.setSummaryTime(LocalDateTime.now());
        summary.setSummarySequence(getNextSummarySequence());
        
        // 汇总所有合约的盈亏
        Map<String, PnLCalculation> contractPnLs = new HashMap<>();
        BigDecimal totalRealizedPnL = BigDecimal.ZERO;
        BigDecimal totalUnrealizedPnL = BigDecimal.ZERO;
        BigDecimal totalOpenAmount = BigDecimal.ZERO;
        BigDecimal totalCloseAmount = BigDecimal.ZERO;
        int totalContracts = 0;
        int activeContracts = 0;
        
        for (String contractCode : contractPnLMap.keys()) {
            PnLCalculation contractPnL = contractPnLMap.get(contractCode);
            if (contractPnL != null) {
                contractPnLs.put(contractCode, contractPnL);
                
                totalRealizedPnL = totalRealizedPnL.add(contractPnL.getRealizedPnL());
                totalUnrealizedPnL = totalUnrealizedPnL.add(contractPnL.getUnrealizedPnL());
                totalOpenAmount = totalOpenAmount.add(contractPnL.getOpenAmount());
                totalCloseAmount = totalCloseAmount.add(contractPnL.getCloseAmount());
                totalContracts++;
                
                if (contractPnL.getCurrentPosition().compareTo(BigDecimal.ZERO) != 0) {
                    activeContracts++;
                }
            }
        }
        
        // 设置汇总数据
        summary.setContractPnLMap(contractPnLs);
        summary.setTotalRealizedPnL(totalRealizedPnL);
        summary.setTotalUnrealizedPnL(totalUnrealizedPnL);
        summary.setTotalPnL(totalRealizedPnL.add(totalUnrealizedPnL));
        summary.setTotalOpenAmount(totalOpenAmount);
        summary.setTotalCloseAmount(totalCloseAmount);
        summary.setTotalContracts(totalContracts);
        summary.setActiveContracts(activeContracts);
        
        // 计算风险指标
        calculateRiskMetrics(summary);
        
        // 更新最后汇总时间
        lastSummaryTimeState.update(summary.getSummaryTime());
        
        return summary;
    }
    
    /**
     * 计算风险指标
     */
    private void calculateRiskMetrics(PnLSummary summary) {
        BigDecimal totalOpenAmount = summary.getTotalOpenAmount();
        
        if (totalOpenAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 计算总收益率
            BigDecimal totalReturnRate = summary.getTotalPnL()
                    .divide(totalOpenAmount, 6, BigDecimal.ROUND_HALF_UP);
            summary.setTotalReturnRate(totalReturnRate);
            
            // 计算已实现收益率
            BigDecimal realizedReturnRate = summary.getTotalRealizedPnL()
                    .divide(totalOpenAmount, 6, BigDecimal.ROUND_HALF_UP);
            summary.setRealizedReturnRate(realizedReturnRate);
        } else {
            summary.setTotalReturnRate(BigDecimal.ZERO);
            summary.setRealizedReturnRate(BigDecimal.ZERO);
        }
        
        // 计算最大单合约盈亏
        BigDecimal maxContractPnL = BigDecimal.ZERO;
        BigDecimal minContractPnL = BigDecimal.ZERO;
        
        for (PnLCalculation contractPnL : summary.getContractPnLMap().values()) {
            BigDecimal contractTotal = contractPnL.getTotalPnL();
            if (contractTotal.compareTo(maxContractPnL) > 0) {
                maxContractPnL = contractTotal;
            }
            if (contractTotal.compareTo(minContractPnL) < 0) {
                minContractPnL = contractTotal;
            }
        }
        
        summary.setMaxContractPnL(maxContractPnL);
        summary.setMinContractPnL(minContractPnL);
    }
    
    /**
     * 获取下一个汇总序列号
     */
    private long getNextSummarySequence() throws Exception {
        Long currentSequence = summarySequenceState.value();
        if (currentSequence == null) {
            currentSequence = 0L;
        }
        
        long nextSequence = currentSequence + 1;
        summarySequenceState.update(nextSequence);
        
        return nextSequence;
    }
}
