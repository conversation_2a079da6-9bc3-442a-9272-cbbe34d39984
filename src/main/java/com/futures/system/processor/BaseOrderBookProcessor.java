package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础层订单簿处理器
 * 基于单腿委托数据构建和维护基础订单簿
 */
public class BaseOrderBookProcessor extends KeyedProcessFunction<String, SingleLegOrder, BaseOrderBook> {
    
    private static final Logger LOG = LoggerFactory.getLogger(BaseOrderBookProcessor.class);
    
    /**
     * 订单簿状态（按合约代码分区）
     */
    private transient MapState<String, BaseOrderBook> orderBookState;
    
    /**
     * 订单状态（按报单编号索引）
     */
    private transient MapState<String, OrderInfo> orderState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化订单簿状态
        MapStateDescriptor<String, BaseOrderBook> orderBookDescriptor = new MapStateDescriptor<>(
                "orderBookState",
                TypeInformation.of(String.class),
                TypeInformation.of(BaseOrderBook.class)
        );
        orderBookState = getRuntimeContext().getMapState(orderBookDescriptor);
        
        // 初始化订单状态
        MapStateDescriptor<String, OrderInfo> orderDescriptor = new MapStateDescriptor<>(
                "orderState",
                TypeInformation.of(String.class),
                TypeInformation.of(OrderInfo.class)
        );
        orderState = getRuntimeContext().getMapState(orderDescriptor);
    }
    
    @Override
    public void processElement(SingleLegOrder order, Context ctx, Collector<BaseOrderBook> out) 
            throws Exception {
        
        if (order == null || order.getContractCode() == null || order.getOrderNumber() == null) {
            LOG.warn("Invalid order received: {}", order);
            return;
        }
        
        try {
            // 获取或创建订单簿
            BaseOrderBook orderBook = orderBookState.get(order.getContractCode());
            if (orderBook == null) {
                orderBook = new BaseOrderBook(order.getContractCode());
                LOG.info("Created new order book for contract: {}", order.getContractCode());
            }
            
            // 处理订单
            boolean updated = processOrderUpdate(orderBook, order);
            
            if (updated) {
                // 保存更新后的订单簿
                orderBookState.put(order.getContractCode(), orderBook);
                
                // 输出更新后的订单簿
                out.collect(orderBook);
                
                LOG.debug("Updated order book for contract {}: {}", order.getContractCode(), orderBook);
            }
            
        } catch (Exception e) {
            LOG.error("Error processing order: {}", order, e);
        }
    }
    
    /**
     * 处理订单更新
     */
    private boolean processOrderUpdate(BaseOrderBook orderBook, SingleLegOrder order) throws Exception {
        String orderNumber = order.getOrderNumber();
        OrderInfo existingOrder = orderState.get(orderNumber);
        OrderInfo newOrderInfo = OrderInfo.fromSingleLegOrder(order);
        
        boolean updated = false;
        
        if (existingOrder == null) {
            // 新订单
            updated = handleNewOrder(orderBook, newOrderInfo);
        } else {
            // 订单更新
            updated = handleOrderUpdate(orderBook, existingOrder, newOrderInfo);
        }
        
        if (updated) {
            // 更新订单状态
            if (newOrderInfo.getOrderStatus().isFinished()) {
                orderState.remove(orderNumber);
            } else {
                orderState.put(orderNumber, newOrderInfo);
            }
        }
        
        return updated;
    }
    
    /**
     * 处理新订单
     */
    private boolean handleNewOrder(BaseOrderBook orderBook, OrderInfo orderInfo) {
        if (!orderInfo.getOrderStatus().isInQueue()) {
            LOG.debug("New order not in queue, ignoring: {}", orderInfo);
            return false;
        }
        
        boolean success = orderBook.addOrder(orderInfo);
        if (success) {
            LOG.debug("Added new order to book: {}", orderInfo);
        } else {
            LOG.warn("Failed to add new order: {}", orderInfo);
        }
        
        return success;
    }
    
    /**
     * 处理订单更新
     */
    private boolean handleOrderUpdate(BaseOrderBook orderBook, OrderInfo existingOrder, OrderInfo newOrderInfo) {
        OrderStatus oldStatus = existingOrder.getOrderStatus();
        OrderStatus newStatus = newOrderInfo.getOrderStatus();
        
        // 检查订单状态变化
        if (oldStatus != newStatus) {
            return handleOrderStatusChange(orderBook, existingOrder, newOrderInfo);
        }
        
        // 检查数量变化
        if (!existingOrder.getRemainingVolume().equals(newOrderInfo.getRemainingVolume())) {
            return handleOrderVolumeChange(orderBook, existingOrder, newOrderInfo);
        }
        
        // 检查价格变化
        if (!existingOrder.getOrderPrice().equals(newOrderInfo.getOrderPrice())) {
            return handleOrderPriceChange(orderBook, existingOrder, newOrderInfo);
        }
        
        // 没有实质性变化
        return false;
    }
    
    /**
     * 处理订单状态变化
     */
    private boolean handleOrderStatusChange(BaseOrderBook orderBook, OrderInfo existingOrder, OrderInfo newOrderInfo) {
        String orderNumber = existingOrder.getOrderNumber();
        
        if (newOrderInfo.getOrderStatus().isFinished()) {
            // 订单完成（全部成交、撤单等），从订单簿移除
            boolean success = orderBook.removeOrder(orderNumber);
            if (success) {
                LOG.debug("Removed finished order: {} (status: {})", orderNumber, newOrderInfo.getOrderStatus());
            }
            return success;
        } else if (newOrderInfo.getOrderStatus().isInQueue() && !existingOrder.getOrderStatus().isInQueue()) {
            // 订单重新进入队列
            boolean success = orderBook.addOrder(newOrderInfo);
            if (success) {
                LOG.debug("Re-added order to queue: {}", orderNumber);
            }
            return success;
        } else if (!newOrderInfo.getOrderStatus().isInQueue() && existingOrder.getOrderStatus().isInQueue()) {
            // 订单离开队列
            boolean success = orderBook.removeOrder(orderNumber);
            if (success) {
                LOG.debug("Removed order from queue: {}", orderNumber);
            }
            return success;
        }
        
        return false;
    }
    
    /**
     * 处理订单数量变化
     */
    private boolean handleOrderVolumeChange(BaseOrderBook orderBook, OrderInfo existingOrder, OrderInfo newOrderInfo) {
        if (!newOrderInfo.getOrderStatus().isInQueue()) {
            return false;
        }
        
        boolean success = orderBook.updateOrder(newOrderInfo);
        if (success) {
            LOG.debug("Updated order volume: {} from {} to {}", 
                    newOrderInfo.getOrderNumber(), 
                    existingOrder.getRemainingVolume(), 
                    newOrderInfo.getRemainingVolume());
        }
        return success;
    }
    
    /**
     * 处理订单价格变化
     */
    private boolean handleOrderPriceChange(BaseOrderBook orderBook, OrderInfo existingOrder, OrderInfo newOrderInfo) {
        if (!newOrderInfo.getOrderStatus().isInQueue()) {
            return false;
        }
        
        // 价格变化需要先移除再添加
        orderBook.removeOrder(existingOrder.getOrderNumber());
        boolean success = orderBook.addOrder(newOrderInfo);
        
        if (success) {
            LOG.debug("Updated order price: {} from {} to {}", 
                    newOrderInfo.getOrderNumber(), 
                    existingOrder.getOrderPrice(), 
                    newOrderInfo.getOrderPrice());
        }
        return success;
    }
}
