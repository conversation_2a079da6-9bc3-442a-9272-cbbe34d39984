package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础层订单簿管理器
 * 整合订单处理、成交处理和快照生成，提供完整的基础层订单簿功能
 */
public class BaseOrderBookManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(BaseOrderBookManager.class);
    
    /**
     * 创建基础层订单簿处理流水线
     * 
     * @param singleLegOrderStream 单腿委托数据流
     * @param tradeDetailStream 成交明细数据流
     * @return 订单簿快照数据流
     */
    public static SingleOutputStreamOperator<OrderBookSnapshot> createBaseOrderBookPipeline(
            DataStream<SingleLegOrder> singleLegOrderStream,
            DataStream<TradeDetail> tradeDetailStream) {
        
        LOG.info("Creating base order book processing pipeline");
        
        // 1. 合并订单和成交事件，按合约代码分区
        SingleOutputStreamOperator<BaseOrderBook> mergedOrderBookStream = singleLegOrderStream
                .keyBy(SingleLegOrder::getContractCode)
                .connect(tradeDetailStream.keyBy(TradeDetail::getContractCode))
                .process(new OrderBookEventMerger())
                .name("OrderBookEventMerger");
        
        // 2. 生成定时快照
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = mergedOrderBookStream
                .keyBy(BaseOrderBook::getContractCode)
                .process(new OrderBookSnapshotGenerator())
                .name("OrderBookSnapshotGenerator");
        
        LOG.info("Base order book pipeline created successfully");
        
        return snapshotStream;
    }
    
    /**
     * 创建分离的订单簿处理流水线（订单和成交分别处理）
     * 
     * @param singleLegOrderStream 单腿委托数据流
     * @param tradeDetailStream 成交明细数据流
     * @return 处理结果
     */
    public static BaseOrderBookStreams createSeparateOrderBookPipeline(
            DataStream<SingleLegOrder> singleLegOrderStream,
            DataStream<TradeDetail> tradeDetailStream) {
        
        LOG.info("Creating separate order book processing pipeline");
        
        // 1. 处理订单事件
        SingleOutputStreamOperator<BaseOrderBook> orderBookFromOrders = singleLegOrderStream
                .keyBy(SingleLegOrder::getContractCode)
                .process(new BaseOrderBookProcessor())
                .name("BaseOrderBookProcessor");
        
        // 2. 处理成交事件
        SingleOutputStreamOperator<BaseOrderBook> orderBookFromTrades = tradeDetailStream
                .keyBy(TradeDetail::getContractCode)
                .process(new TradeProcessor())
                .name("TradeProcessor");
        
        // 3. 合并两个订单簿流
        DataStream<BaseOrderBook> mergedOrderBookStream = orderBookFromOrders
                .union(orderBookFromTrades)
                .name("UnionOrderBooks");
        
        // 4. 生成定时快照
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = mergedOrderBookStream
                .keyBy(BaseOrderBook::getContractCode)
                .process(new OrderBookSnapshotGenerator())
                .name("OrderBookSnapshotGenerator");
        
        LOG.info("Separate order book pipeline created successfully");
        
        return new BaseOrderBookStreams(
                orderBookFromOrders,
                orderBookFromTrades,
                mergedOrderBookStream,
                snapshotStream
        );
    }
    
    /**
     * 基础层订单簿数据流集合
     */
    public static class BaseOrderBookStreams {
        private final SingleOutputStreamOperator<BaseOrderBook> orderBookFromOrders;
        private final SingleOutputStreamOperator<BaseOrderBook> orderBookFromTrades;
        private final DataStream<BaseOrderBook> mergedOrderBookStream;
        private final SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream;
        
        public BaseOrderBookStreams(
                SingleOutputStreamOperator<BaseOrderBook> orderBookFromOrders,
                SingleOutputStreamOperator<BaseOrderBook> orderBookFromTrades,
                DataStream<BaseOrderBook> mergedOrderBookStream,
                SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream) {
            this.orderBookFromOrders = orderBookFromOrders;
            this.orderBookFromTrades = orderBookFromTrades;
            this.mergedOrderBookStream = mergedOrderBookStream;
            this.snapshotStream = snapshotStream;
        }
        
        public SingleOutputStreamOperator<BaseOrderBook> getOrderBookFromOrders() {
            return orderBookFromOrders;
        }
        
        public SingleOutputStreamOperator<BaseOrderBook> getOrderBookFromTrades() {
            return orderBookFromTrades;
        }
        
        public DataStream<BaseOrderBook> getMergedOrderBookStream() {
            return mergedOrderBookStream;
        }
        
        public SingleOutputStreamOperator<OrderBookSnapshot> getSnapshotStream() {
            return snapshotStream;
        }
    }
    
    /**
     * 配置订单簿处理器参数
     */
    public static class OrderBookConfig {
        private int snapshotDepth = 5;
        private long snapshotIntervalMs = 500L;
        private boolean enableMetrics = true;
        private int parallelism = 1;
        
        public int getSnapshotDepth() {
            return snapshotDepth;
        }
        
        public void setSnapshotDepth(int snapshotDepth) {
            this.snapshotDepth = snapshotDepth;
        }
        
        public long getSnapshotIntervalMs() {
            return snapshotIntervalMs;
        }
        
        public void setSnapshotIntervalMs(long snapshotIntervalMs) {
            this.snapshotIntervalMs = snapshotIntervalMs;
        }
        
        public boolean isEnableMetrics() {
            return enableMetrics;
        }
        
        public void setEnableMetrics(boolean enableMetrics) {
            this.enableMetrics = enableMetrics;
        }
        
        public int getParallelism() {
            return parallelism;
        }
        
        public void setParallelism(int parallelism) {
            this.parallelism = parallelism;
        }
    }
}
