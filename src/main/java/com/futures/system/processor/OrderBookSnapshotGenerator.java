package com.futures.system.processor;

import com.futures.system.model.*;
import com.futures.system.utils.TimestampUtils;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单簿快照生成器
 * 每0.5秒生成一次订单簿快照，确保精确的定时输出
 */
public class OrderBookSnapshotGenerator extends KeyedProcessFunction<String, BaseOrderBook, OrderBookSnapshot> {
    
    private static final Logger LOG = LoggerFactory.getLogger(OrderBookSnapshotGenerator.class);
    
    /**
     * 快照间隔：500毫秒
     */
    private static final long SNAPSHOT_INTERVAL_MS = 500L;
    
    /**
     * 快照深度：默认5档
     */
    private static final int DEFAULT_SNAPSHOT_DEPTH = 5;
    
    /**
     * 当前订单簿状态
     */
    private transient ValueState<BaseOrderBook> currentOrderBookState;
    
    /**
     * 下一次快照时间
     */
    private transient ValueState<Long> nextSnapshotTimeState;
    
    /**
     * 快照序列号
     */
    private transient ValueState<Long> snapshotSequenceState;
    
    /**
     * 全局快照序列号（静态计数器）
     */
    private static final AtomicLong globalSnapshotSequence = new AtomicLong(0);
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化当前订单簿状态
        ValueStateDescriptor<BaseOrderBook> orderBookDescriptor = new ValueStateDescriptor<>(
                "currentOrderBook",
                TypeInformation.of(BaseOrderBook.class)
        );
        currentOrderBookState = getRuntimeContext().getState(orderBookDescriptor);
        
        // 初始化下一次快照时间状态
        ValueStateDescriptor<Long> nextSnapshotTimeDescriptor = new ValueStateDescriptor<>(
                "nextSnapshotTime",
                TypeInformation.of(Long.class)
        );
        nextSnapshotTimeState = getRuntimeContext().getState(nextSnapshotTimeDescriptor);
        
        // 初始化快照序列号状态
        ValueStateDescriptor<Long> snapshotSequenceDescriptor = new ValueStateDescriptor<>(
                "snapshotSequence",
                TypeInformation.of(Long.class)
        );
        snapshotSequenceState = getRuntimeContext().getState(snapshotSequenceDescriptor);
    }
    
    @Override
    public void processElement(BaseOrderBook orderBook, Context ctx, Collector<OrderBookSnapshot> out) 
            throws Exception {
        
        if (orderBook == null) {
            return;
        }
        
        // 更新当前订单簿状态
        currentOrderBookState.update(orderBook);
        
        // 获取当前处理时间
        long currentTime = ctx.timerService().currentProcessingTime();
        
        // 检查是否需要设置定时器
        Long nextSnapshotTime = nextSnapshotTimeState.value();
        if (nextSnapshotTime == null) {
            // 首次处理，设置下一个对齐的快照时间
            long alignedTime = TimestampUtils.getNext500msAlignedTimestamp(currentTime);
            nextSnapshotTimeState.update(alignedTime);
            ctx.timerService().registerProcessingTimeTimer(alignedTime);
            
            LOG.info("Registered first snapshot timer for contract {} at {}", 
                    orderBook.getContractCode(), TimestampUtils.formatTimestamp(alignedTime));
        }
        
        LOG.debug("Updated order book for contract {}: {}", orderBook.getContractCode(), orderBook);
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) 
            throws Exception {
        
        // 获取当前订单簿
        BaseOrderBook orderBook = currentOrderBookState.value();
        if (orderBook == null) {
            LOG.debug("No order book available for snapshot at {}", TimestampUtils.formatTimestamp(timestamp));
            scheduleNextSnapshot(timestamp, ctx);
            return;
        }
        
        try {
            // 生成快照
            OrderBookSnapshot snapshot = generateSnapshot(orderBook, timestamp);
            
            // 输出快照
            out.collect(snapshot);
            
            LOG.debug("Generated snapshot for contract {} at {}: bestBid={}, bestAsk={}", 
                    orderBook.getContractCode(), 
                    TimestampUtils.formatTimestamp(timestamp),
                    snapshot.getBestBidPrice(),
                    snapshot.getBestAskPrice());
            
        } catch (Exception e) {
            LOG.error("Error generating snapshot for contract {} at {}", 
                    orderBook.getContractCode(), TimestampUtils.formatTimestamp(timestamp), e);
        }
        
        // 安排下一次快照
        scheduleNextSnapshot(timestamp, ctx);
    }
    
    /**
     * 生成订单簿快照
     */
    private OrderBookSnapshot generateSnapshot(BaseOrderBook orderBook, long timestamp) throws Exception {
        // 创建快照对象
        OrderBookSnapshot snapshot = new OrderBookSnapshot(orderBook.getContractCode());
        
        // 设置基本信息
        snapshot.setSnapshotTime(TimestampUtils.fromTimestamp(timestamp));
        snapshot.setSnapshotSequence(getNextSnapshotSequence());
        snapshot.setTradeDate(TimestampUtils.getCurrentTradeDate().toString());
        snapshot.setExchangeCode("UNKNOWN"); // 可以从订单簿中获取
        snapshot.setCommodityCode("UNKNOWN"); // 可以从合约代码解析
        
        // 获取买方档位
        List<PriceLevel> bidLevels = orderBook.getTopBidLevels(DEFAULT_SNAPSHOT_DEPTH);
        for (PriceLevel level : bidLevels) {
            PriceLevelSnapshot levelSnapshot = PriceLevelSnapshot.fromPriceLevel(level, BuySellFlag.BUY);
            snapshot.addBidLevel(levelSnapshot);
        }
        
        // 获取卖方档位
        List<PriceLevel> askLevels = orderBook.getTopAskLevels(DEFAULT_SNAPSHOT_DEPTH);
        for (PriceLevel level : askLevels) {
            PriceLevelSnapshot levelSnapshot = PriceLevelSnapshot.fromPriceLevel(level, BuySellFlag.SELL);
            snapshot.addAskLevel(levelSnapshot);
        }
        
        // 设置基础层档位数量
        snapshot.setBaseBidLevelsCount(bidLevels.size());
        snapshot.setBaseAskLevelsCount(askLevels.size());
        snapshot.setHasVirtualLayer(false); // 基础层快照
        
        // 计算统计信息
        snapshot.calculateStatistics();
        
        return snapshot;
    }
    
    /**
     * 安排下一次快照
     */
    private void scheduleNextSnapshot(long currentTimestamp, OnTimerContext ctx) throws Exception {
        long nextSnapshotTime = currentTimestamp + SNAPSHOT_INTERVAL_MS;
        nextSnapshotTimeState.update(nextSnapshotTime);
        ctx.timerService().registerProcessingTimeTimer(nextSnapshotTime);
        
        LOG.debug("Scheduled next snapshot at {}", TimestampUtils.formatTimestamp(nextSnapshotTime));
    }
    
    /**
     * 获取下一个快照序列号
     */
    private long getNextSnapshotSequence() throws Exception {
        Long currentSequence = snapshotSequenceState.value();
        if (currentSequence == null) {
            currentSequence = 0L;
        }
        
        long nextSequence = currentSequence + 1;
        snapshotSequenceState.update(nextSequence);
        
        return nextSequence;
    }
    
    /**
     * 获取全局快照序列号
     */
    public static long getGlobalSnapshotSequence() {
        return globalSnapshotSequence.incrementAndGet();
    }
}
