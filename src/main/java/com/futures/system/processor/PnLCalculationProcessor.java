package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 盈亏计算处理器
 * 基于成交明细实时计算结算会员的盈亏状态
 */
public class PnLCalculationProcessor extends KeyedProcessFunction<String, TradeDetail, PnLCalculation> {
    
    private static final Logger LOG = LoggerFactory.getLogger(PnLCalculationProcessor.class);
    
    /**
     * 盈亏状态（按合约代码分区）
     */
    private transient MapState<String, PnLState> pnlStateMap;
    
    /**
     * 最新价格缓存（按合约代码索引）
     */
    private transient MapState<String, java.math.BigDecimal> latestPriceMap;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化盈亏状态
        MapStateDescriptor<String, PnLState> pnlStateDescriptor = new MapStateDescriptor<>(
                "pnlStateMap",
                TypeInformation.of(String.class),
                TypeInformation.of(PnLState.class)
        );
        pnlStateMap = getRuntimeContext().getMapState(pnlStateDescriptor);
        
        // 初始化最新价格缓存
        MapStateDescriptor<String, java.math.BigDecimal> latestPriceDescriptor = new MapStateDescriptor<>(
                "latestPriceMap",
                TypeInformation.of(String.class),
                TypeInformation.of(java.math.BigDecimal.class)
        );
        latestPriceMap = getRuntimeContext().getMapState(latestPriceDescriptor);
    }
    
    @Override
    public void processElement(TradeDetail trade, Context ctx, Collector<PnLCalculation> out) 
            throws Exception {
        
        if (trade == null || trade.getSettlementMemberCode() == null || 
            trade.getContractCode() == null || trade.getTradeNumber() == null) {
            LOG.warn("Invalid trade detail received: {}", trade);
            return;
        }
        
        // 只处理普通成交
        if (trade.getTradeType() != TradeType.NORMAL) {
            LOG.debug("Ignoring non-normal trade: {}", trade.getTradeType());
            return;
        }
        
        try {
            String contractCode = trade.getContractCode();
            
            // 获取或创建盈亏状态
            PnLState pnlState = getOrCreatePnLState(trade);
            
            // 处理成交
            boolean updated = pnlState.processTrade(trade);
            
            if (updated) {
                // 保存更新后的状态
                pnlStateMap.put(contractCode, pnlState);
                
                // 更新最新价格缓存
                latestPriceMap.put(contractCode, trade.getTradePrice());
                
                // 生成盈亏计算结果
                PnLCalculation calculation = pnlState.toPnLCalculation();
                
                // 输出结果
                out.collect(calculation);
                
                LOG.debug("Updated PnL for member {} contract {}: {}", 
                        trade.getSettlementMemberCode(), contractCode, calculation);
            } else {
                LOG.debug("Trade already processed or invalid: {}", trade.getTradeNumber());
            }
            
        } catch (Exception e) {
            LOG.error("Error processing trade for PnL calculation: {}", trade, e);
        }
    }
    
    /**
     * 获取或创建盈亏状态
     */
    private PnLState getOrCreatePnLState(TradeDetail trade) throws Exception {
        String contractCode = trade.getContractCode();
        PnLState pnlState = pnlStateMap.get(contractCode);
        
        if (pnlState == null) {
            pnlState = new PnLState(
                    trade.getSettlementMemberCode(),
                    contractCode,
                    trade.getTradeDate()
            );
            LOG.info("Created new PnL state for member {} contract {}", 
                    trade.getSettlementMemberCode(), contractCode);
        }
        
        return pnlState;
    }
}
