package com.futures.system.processor;

import com.futures.system.model.*;
import com.futures.system.utils.TimestampUtils;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

/**
 * 虚拟层订单簿快照生成器
 * 每0.5秒生成一次包含虚拟层的合并订单簿快照
 */
public class VirtualOrderBookSnapshotGenerator extends KeyedProcessFunction<String, VirtualOrderBook, OrderBookSnapshot> {
    
    private static final Logger LOG = LoggerFactory.getLogger(VirtualOrderBookSnapshotGenerator.class);
    
    /**
     * 快照间隔：500毫秒
     */
    private static final long SNAPSHOT_INTERVAL_MS = 500L;
    
    /**
     * 快照深度：默认5档
     */
    private static final int DEFAULT_SNAPSHOT_DEPTH = 5;
    
    /**
     * 当前虚拟订单簿状态
     */
    private transient ValueState<VirtualOrderBook> currentVirtualOrderBookState;
    
    /**
     * 下一次快照时间
     */
    private transient ValueState<Long> nextSnapshotTimeState;
    
    /**
     * 快照序列号
     */
    private transient ValueState<Long> snapshotSequenceState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化当前虚拟订单簿状态
        ValueStateDescriptor<VirtualOrderBook> virtualOrderBookDescriptor = new ValueStateDescriptor<>(
                "currentVirtualOrderBook",
                TypeInformation.of(VirtualOrderBook.class)
        );
        currentVirtualOrderBookState = getRuntimeContext().getState(virtualOrderBookDescriptor);
        
        // 初始化下一次快照时间状态
        ValueStateDescriptor<Long> nextSnapshotTimeDescriptor = new ValueStateDescriptor<>(
                "nextSnapshotTime",
                TypeInformation.of(Long.class)
        );
        nextSnapshotTimeState = getRuntimeContext().getState(nextSnapshotTimeDescriptor);
        
        // 初始化快照序列号状态
        ValueStateDescriptor<Long> snapshotSequenceDescriptor = new ValueStateDescriptor<>(
                "snapshotSequence",
                TypeInformation.of(Long.class)
        );
        snapshotSequenceState = getRuntimeContext().getState(snapshotSequenceDescriptor);
    }
    
    @Override
    public void processElement(VirtualOrderBook virtualOrderBook, Context ctx, Collector<OrderBookSnapshot> out) 
            throws Exception {
        
        if (virtualOrderBook == null) {
            return;
        }
        
        // 更新当前虚拟订单簿状态
        currentVirtualOrderBookState.update(virtualOrderBook);
        
        // 获取当前处理时间
        long currentTime = ctx.timerService().currentProcessingTime();
        
        // 检查是否需要设置定时器
        Long nextSnapshotTime = nextSnapshotTimeState.value();
        if (nextSnapshotTime == null) {
            // 首次处理，设置下一个对齐的快照时间
            long alignedTime = TimestampUtils.getNext500msAlignedTimestamp(currentTime);
            nextSnapshotTimeState.update(alignedTime);
            ctx.timerService().registerProcessingTimeTimer(alignedTime);
            
            LOG.info("Registered first virtual snapshot timer for contract {} at {}", 
                    virtualOrderBook.getBaseOrderBook().getContractCode(), 
                    TimestampUtils.formatTimestamp(alignedTime));
        }
        
        LOG.debug("Updated virtual order book for contract {}: {}", 
                virtualOrderBook.getBaseOrderBook().getContractCode(), virtualOrderBook);
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) 
            throws Exception {
        
        // 获取当前虚拟订单簿
        VirtualOrderBook virtualOrderBook = currentVirtualOrderBookState.value();
        if (virtualOrderBook == null) {
            LOG.debug("No virtual order book available for snapshot at {}", 
                    TimestampUtils.formatTimestamp(timestamp));
            scheduleNextSnapshot(timestamp, ctx);
            return;
        }
        
        try {
            // 生成合并快照
            OrderBookSnapshot snapshot = generateMergedSnapshot(virtualOrderBook, timestamp);
            
            // 输出快照
            out.collect(snapshot);
            
            LOG.debug("Generated virtual snapshot for contract {} at {}: bestBid={}, bestAsk={}, hasVirtual={}", 
                    virtualOrderBook.getBaseOrderBook().getContractCode(),
                    TimestampUtils.formatTimestamp(timestamp),
                    snapshot.getBestBidPrice(),
                    snapshot.getBestAskPrice(),
                    snapshot.getHasVirtualLayer());
            
        } catch (Exception e) {
            LOG.error("Error generating virtual snapshot for contract {} at {}", 
                    virtualOrderBook.getBaseOrderBook().getContractCode(), 
                    TimestampUtils.formatTimestamp(timestamp), e);
        }
        
        // 安排下一次快照
        scheduleNextSnapshot(timestamp, ctx);
    }
    
    /**
     * 生成合并的订单簿快照
     */
    private OrderBookSnapshot generateMergedSnapshot(VirtualOrderBook virtualOrderBook, long timestamp) throws Exception {
        // 使用虚拟订单簿的内置方法生成合并快照
        OrderBookSnapshot snapshot = virtualOrderBook.generateMergedSnapshot(DEFAULT_SNAPSHOT_DEPTH);
        
        // 更新快照元数据
        snapshot.setSnapshotTime(TimestampUtils.fromTimestamp(timestamp));
        snapshot.setSnapshotSequence(getNextSnapshotSequence());
        snapshot.setTradeDate(TimestampUtils.getCurrentTradeDate().toString());
        
        // 设置虚拟层信息
        snapshot.setHasVirtualLayer(!virtualOrderBook.isVirtualLayerEmpty());
        
        // 设置基础层档位数量
        BaseOrderBook baseOrderBook = virtualOrderBook.getBaseOrderBook();
        snapshot.setBaseBidLevelsCount(Math.min(baseOrderBook.getBidLevels().size(), DEFAULT_SNAPSHOT_DEPTH));
        snapshot.setBaseAskLevelsCount(Math.min(baseOrderBook.getAskLevels().size(), DEFAULT_SNAPSHOT_DEPTH));
        
        return snapshot;
    }
    
    /**
     * 安排下一次快照
     */
    private void scheduleNextSnapshot(long currentTimestamp, OnTimerContext ctx) throws Exception {
        long nextSnapshotTime = currentTimestamp + SNAPSHOT_INTERVAL_MS;
        nextSnapshotTimeState.update(nextSnapshotTime);
        ctx.timerService().registerProcessingTimeTimer(nextSnapshotTime);
        
        LOG.debug("Scheduled next virtual snapshot at {}", TimestampUtils.formatTimestamp(nextSnapshotTime));
    }
    
    /**
     * 获取下一个快照序列号
     */
    private long getNextSnapshotSequence() throws Exception {
        Long currentSequence = snapshotSequenceState.value();
        if (currentSequence == null) {
            currentSequence = 0L;
        }
        
        long nextSequence = currentSequence + 1;
        snapshotSequenceState.update(nextSequence);
        
        return nextSequence;
    }
}
