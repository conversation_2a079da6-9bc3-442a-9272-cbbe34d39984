package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * 盈亏价格更新处理器
 * 基于订单簿快照更新盈亏计算中的最新价格，重新计算浮动盈亏
 */
public class PnLPriceUpdateProcessor extends KeyedProcessFunction<String, OrderBookSnapshot, PnLCalculation> {
    
    private static final Logger LOG = LoggerFactory.getLogger(PnLPriceUpdateProcessor.class);
    
    /**
     * 盈亏状态（按合约代码分区）
     */
    private transient MapState<String, PnLState> pnlStateMap;
    
    /**
     * 价格更新阈值（避免频繁更新）
     */
    private static final BigDecimal PRICE_UPDATE_THRESHOLD = new BigDecimal("0.0001");
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化盈亏状态
        MapStateDescriptor<String, PnLState> pnlStateDescriptor = new MapStateDescriptor<>(
                "pnlStateMap",
                TypeInformation.of(String.class),
                TypeInformation.of(PnLState.class)
        );
        pnlStateMap = getRuntimeContext().getMapState(pnlStateDescriptor);
    }
    
    @Override
    public void processElement(OrderBookSnapshot snapshot, Context ctx, Collector<PnLCalculation> out) 
            throws Exception {
        
        if (snapshot == null || snapshot.getContractCode() == null) {
            LOG.warn("Invalid order book snapshot received: {}", snapshot);
            return;
        }
        
        try {
            String contractCode = snapshot.getContractCode();
            
            // 获取最新价格（使用中间价）
            BigDecimal latestPrice = calculateLatestPrice(snapshot);
            if (latestPrice == null) {
                LOG.debug("No valid price found in snapshot for contract: {}", contractCode);
                return;
            }
            
            // 获取盈亏状态
            PnLState pnlState = pnlStateMap.get(contractCode);
            if (pnlState == null || !pnlState.hasActivity()) {
                LOG.debug("No PnL state or activity found for contract: {}", contractCode);
                return;
            }
            
            // 检查价格是否有显著变化
            if (!isPriceChangeSignificant(pnlState.getLatestTradePrice(), latestPrice)) {
                return;
            }
            
            // 更新价格并重新计算盈亏
            pnlState.updateLatestPrice(latestPrice);
            
            // 保存更新后的状态
            pnlStateMap.put(contractCode, pnlState);
            
            // 生成更新后的盈亏计算结果
            PnLCalculation calculation = pnlState.toPnLCalculation();
            
            // 输出结果
            out.collect(calculation);
            
            LOG.debug("Updated PnL price for member {} contract {} to {}: unrealizedPnL={}", 
                    pnlState.getSettlementMemberCode(), contractCode, latestPrice, 
                    calculation.getUnrealizedPnL());
            
        } catch (Exception e) {
            LOG.error("Error processing order book snapshot for PnL price update: {}", snapshot, e);
        }
    }
    
    /**
     * 计算最新价格
     */
    private BigDecimal calculateLatestPrice(OrderBookSnapshot snapshot) {
        BigDecimal bestBid = snapshot.getBestBidPrice();
        BigDecimal bestAsk = snapshot.getBestAskPrice();
        
        if (bestBid != null && bestAsk != null) {
            // 使用中间价
            return bestBid.add(bestAsk).divide(BigDecimal.valueOf(2), 6, BigDecimal.ROUND_HALF_UP);
        } else if (bestBid != null) {
            // 只有买价
            return bestBid;
        } else if (bestAsk != null) {
            // 只有卖价
            return bestAsk;
        }
        
        return null;
    }
    
    /**
     * 检查价格变化是否显著
     */
    private boolean isPriceChangeSignificant(BigDecimal oldPrice, BigDecimal newPrice) {
        if (oldPrice == null) {
            return true;
        }
        
        if (newPrice == null) {
            return false;
        }
        
        BigDecimal priceDiff = newPrice.subtract(oldPrice).abs();
        BigDecimal threshold = oldPrice.multiply(PRICE_UPDATE_THRESHOLD);
        
        return priceDiff.compareTo(threshold) > 0;
    }
}
