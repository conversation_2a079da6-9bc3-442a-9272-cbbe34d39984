package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 盈亏计算管理器
 * 整合成交明细处理、价格更新和盈亏汇总，提供完整的盈亏计算功能
 */
public class PnLCalculationManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(PnLCalculationManager.class);
    
    /**
     * 创建完整的盈亏计算流水线
     * 
     * @param tradeDetailStream 成交明细数据流
     * @param orderBookSnapshotStream 订单簿快照数据流（用于价格更新）
     * @return 盈亏计算结果流
     */
    public static PnLCalculationStreams createPnLCalculationPipeline(
            DataStream<TradeDetail> tradeDetailStream,
            DataStream<OrderBookSnapshot> orderBookSnapshotStream) {
        
        LOG.info("Creating PnL calculation processing pipeline");
        
        // 1. 基于成交明细计算盈亏
        SingleOutputStreamOperator<PnLCalculation> tradeBasedPnLStream = tradeDetailStream
                .keyBy(trade -> trade.getSettlementMemberCode() + ":" + trade.getContractCode())
                .process(new PnLCalculationProcessor())
                .name("PnLCalculationProcessor");
        
        // 2. 基于订单簿快照更新价格
        SingleOutputStreamOperator<PnLCalculation> priceUpdatedPnLStream = orderBookSnapshotStream
                .keyBy(snapshot -> snapshot.getContractCode())
                .process(new PnLPriceUpdateProcessor())
                .name("PnLPriceUpdateProcessor");
        
        // 3. 合并两个盈亏流
        DataStream<PnLCalculation> mergedPnLStream = tradeBasedPnLStream
                .union(priceUpdatedPnLStream)
                .name("MergedPnLStream");
        
        // 4. 按结算会员汇总盈亏
        SingleOutputStreamOperator<PnLSummary> pnlSummaryStream = mergedPnLStream
                .keyBy(PnLCalculation::getSettlementMemberCode)
                .process(new PnLAggregationProcessor())
                .name("PnLAggregationProcessor");
        
        LOG.info("PnL calculation pipeline created successfully");
        
        return new PnLCalculationStreams(
                tradeBasedPnLStream,
                priceUpdatedPnLStream,
                mergedPnLStream,
                pnlSummaryStream
        );
    }
    
    /**
     * 创建简化的盈亏计算流水线（仅基于成交明细）
     * 
     * @param tradeDetailStream 成交明细数据流
     * @return 盈亏计算结果流
     */
    public static SimplePnLStreams createSimplePnLCalculationPipeline(
            DataStream<TradeDetail> tradeDetailStream) {
        
        LOG.info("Creating simple PnL calculation pipeline");
        
        // 1. 基于成交明细计算盈亏
        SingleOutputStreamOperator<PnLCalculation> pnlCalculationStream = tradeDetailStream
                .keyBy(trade -> trade.getSettlementMemberCode() + ":" + trade.getContractCode())
                .process(new PnLCalculationProcessor())
                .name("SimplePnLCalculationProcessor");
        
        // 2. 按结算会员汇总盈亏
        SingleOutputStreamOperator<PnLSummary> pnlSummaryStream = pnlCalculationStream
                .keyBy(PnLCalculation::getSettlementMemberCode)
                .process(new PnLAggregationProcessor())
                .name("SimplePnLAggregationProcessor");
        
        LOG.info("Simple PnL calculation pipeline created successfully");
        
        return new SimplePnLStreams(pnlCalculationStream, pnlSummaryStream);
    }
    
    /**
     * 完整盈亏计算数据流集合
     */
    public static class PnLCalculationStreams {
        private final SingleOutputStreamOperator<PnLCalculation> tradeBasedPnLStream;
        private final SingleOutputStreamOperator<PnLCalculation> priceUpdatedPnLStream;
        private final DataStream<PnLCalculation> mergedPnLStream;
        private final SingleOutputStreamOperator<PnLSummary> pnlSummaryStream;
        
        public PnLCalculationStreams(
                SingleOutputStreamOperator<PnLCalculation> tradeBasedPnLStream,
                SingleOutputStreamOperator<PnLCalculation> priceUpdatedPnLStream,
                DataStream<PnLCalculation> mergedPnLStream,
                SingleOutputStreamOperator<PnLSummary> pnlSummaryStream) {
            this.tradeBasedPnLStream = tradeBasedPnLStream;
            this.priceUpdatedPnLStream = priceUpdatedPnLStream;
            this.mergedPnLStream = mergedPnLStream;
            this.pnlSummaryStream = pnlSummaryStream;
        }
        
        public SingleOutputStreamOperator<PnLCalculation> getTradeBasedPnLStream() {
            return tradeBasedPnLStream;
        }
        
        public SingleOutputStreamOperator<PnLCalculation> getPriceUpdatedPnLStream() {
            return priceUpdatedPnLStream;
        }
        
        public DataStream<PnLCalculation> getMergedPnLStream() {
            return mergedPnLStream;
        }
        
        public SingleOutputStreamOperator<PnLSummary> getPnlSummaryStream() {
            return pnlSummaryStream;
        }
    }
    
    /**
     * 简化盈亏计算数据流集合
     */
    public static class SimplePnLStreams {
        private final SingleOutputStreamOperator<PnLCalculation> pnlCalculationStream;
        private final SingleOutputStreamOperator<PnLSummary> pnlSummaryStream;
        
        public SimplePnLStreams(
                SingleOutputStreamOperator<PnLCalculation> pnlCalculationStream,
                SingleOutputStreamOperator<PnLSummary> pnlSummaryStream) {
            this.pnlCalculationStream = pnlCalculationStream;
            this.pnlSummaryStream = pnlSummaryStream;
        }
        
        public SingleOutputStreamOperator<PnLCalculation> getPnlCalculationStream() {
            return pnlCalculationStream;
        }
        
        public SingleOutputStreamOperator<PnLSummary> getPnlSummaryStream() {
            return pnlSummaryStream;
        }
    }
    
    /**
     * 盈亏计算配置
     */
    public static class PnLCalculationConfig {
        private boolean enablePriceUpdate = true;
        private boolean enableAggregation = true;
        private int parallelism = 1;
        private long priceUpdateIntervalMs = 1000L;
        
        public boolean isEnablePriceUpdate() {
            return enablePriceUpdate;
        }
        
        public void setEnablePriceUpdate(boolean enablePriceUpdate) {
            this.enablePriceUpdate = enablePriceUpdate;
        }
        
        public boolean isEnableAggregation() {
            return enableAggregation;
        }
        
        public void setEnableAggregation(boolean enableAggregation) {
            this.enableAggregation = enableAggregation;
        }
        
        public int getParallelism() {
            return parallelism;
        }
        
        public void setParallelism(int parallelism) {
            this.parallelism = parallelism;
        }
        
        public long getPriceUpdateIntervalMs() {
            return priceUpdateIntervalMs;
        }
        
        public void setPriceUpdateIntervalMs(long priceUpdateIntervalMs) {
            this.priceUpdateIntervalMs = priceUpdateIntervalMs;
        }
    }
}
