package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 成交处理器
 * 处理成交明细对订单簿的影响，更新订单状态和剩余量
 */
public class TradeProcessor extends KeyedProcessFunction<String, TradeDetail, BaseOrderBook> {
    
    private static final Logger LOG = LoggerFactory.getLogger(TradeProcessor.class);
    
    /**
     * 订单簿状态（按合约代码分区）
     */
    private transient MapState<String, BaseOrderBook> orderBookState;
    
    /**
     * 已处理成交记录（防重复处理）
     */
    private transient MapState<String, Boolean> processedTradesState;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化订单簿状态
        MapStateDescriptor<String, BaseOrderBook> orderBookDescriptor = new MapStateDescriptor<>(
                "orderBookState",
                TypeInformation.of(String.class),
                TypeInformation.of(BaseOrderBook.class)
        );
        orderBookState = getRuntimeContext().getMapState(orderBookDescriptor);
        
        // 初始化已处理成交状态
        MapStateDescriptor<String, Boolean> processedTradesDescriptor = new MapStateDescriptor<>(
                "processedTradesState",
                TypeInformation.of(String.class),
                TypeInformation.of(Boolean.class)
        );
        processedTradesState = getRuntimeContext().getMapState(processedTradesDescriptor);
    }
    
    @Override
    public void processElement(TradeDetail trade, Context ctx, Collector<BaseOrderBook> out) 
            throws Exception {
        
        if (trade == null || trade.getContractCode() == null || 
            trade.getOrderNumber() == null || trade.getTradeNumber() == null) {
            LOG.warn("Invalid trade received: {}", trade);
            return;
        }
        
        // 检查是否已处理过此成交
        String tradeKey = trade.getTradeNumber();
        if (processedTradesState.contains(tradeKey)) {
            LOG.debug("Trade already processed, skipping: {}", tradeKey);
            return;
        }
        
        try {
            // 获取订单簿
            BaseOrderBook orderBook = orderBookState.get(trade.getContractCode());
            if (orderBook == null) {
                LOG.warn("No order book found for contract: {}, creating new one", trade.getContractCode());
                orderBook = new BaseOrderBook(trade.getContractCode());
            }
            
            // 处理成交
            boolean updated = processTradeExecution(orderBook, trade);
            
            if (updated) {
                // 保存更新后的订单簿
                orderBookState.put(trade.getContractCode(), orderBook);
                
                // 标记成交已处理
                processedTradesState.put(tradeKey, true);
                
                // 输出更新后的订单簿
                out.collect(orderBook);
                
                LOG.debug("Processed trade for contract {}: {}", trade.getContractCode(), trade);
            }
            
        } catch (Exception e) {
            LOG.error("Error processing trade: {}", trade, e);
        }
    }
    
    /**
     * 处理成交执行
     */
    private boolean processTradeExecution(BaseOrderBook orderBook, TradeDetail trade) {
        String orderNumber = trade.getOrderNumber();
        
        // 检查订单是否在订单簿中
        if (!orderBook.getOrderIndex().containsKey(orderNumber)) {
            LOG.debug("Order not found in book, trade may be for historical order: {}", orderNumber);
            return false;
        }
        
        // 验证成交数据
        if (!validateTrade(trade)) {
            LOG.warn("Invalid trade data: {}", trade);
            return false;
        }
        
        // 处理成交
        boolean success = orderBook.processTrade(orderNumber, trade.getTradeVolume());
        
        if (success) {
            LOG.debug("Successfully processed trade: order={}, volume={}, price={}", 
                    orderNumber, trade.getTradeVolume(), trade.getTradePrice());
        } else {
            LOG.warn("Failed to process trade: order={}, volume={}", 
                    orderNumber, trade.getTradeVolume());
        }
        
        return success;
    }
    
    /**
     * 验证成交数据
     */
    private boolean validateTrade(TradeDetail trade) {
        // 检查成交量
        if (trade.getTradeVolume() == null || trade.getTradeVolume().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            LOG.warn("Invalid trade volume: {}", trade.getTradeVolume());
            return false;
        }
        
        // 检查成交价格
        if (trade.getTradePrice() == null || trade.getTradePrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            LOG.warn("Invalid trade price: {}", trade.getTradePrice());
            return false;
        }
        
        // 检查成交类型
        if (trade.getTradeType() == null) {
            LOG.warn("Missing trade type");
            return false;
        }
        
        // 只处理普通成交，忽略衍生成交
        if (trade.getTradeType() != TradeType.NORMAL) {
            LOG.debug("Ignoring non-normal trade type: {}", trade.getTradeType());
            return false;
        }
        
        return true;
    }
    
    /**
     * 清理过期的已处理成交记录（可选的清理逻辑）
     */
    public void cleanupProcessedTrades() {
        // 这里可以实现清理逻辑，比如定期清理超过一定时间的记录
        // 为了简化，暂时不实现
    }
}
