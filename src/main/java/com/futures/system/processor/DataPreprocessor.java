package com.futures.system.processor;

import com.futures.system.model.*;
import com.futures.system.utils.DataCleaningUtils;
import com.futures.system.utils.DataValidationUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据预处理器
 * 负责数据清洗、验证和异常处理
 */
public class DataPreprocessor {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataPreprocessor.class);
    
    // 侧输出标签，用于处理无效数据
    public static final OutputTag<SingleLegOrder> INVALID_SINGLE_LEG_ORDER_TAG = 
            new OutputTag<SingleLegOrder>("invalid-single-leg-orders") {};
    
    public static final OutputTag<CombinationOrder> INVALID_COMBINATION_ORDER_TAG = 
            new OutputTag<CombinationOrder>("invalid-combination-orders") {};
    
    public static final OutputTag<TradeDetail> INVALID_TRADE_DETAIL_TAG = 
            new OutputTag<TradeDetail>("invalid-trade-details") {};
    
    /**
     * 预处理单腿委托订单数据流
     */
    public static SingleOutputStreamOperator<SingleLegOrder> preprocessSingleLegOrders(
            DataStream<SingleLegOrder> inputStream) {
        
        return inputStream
                // 数据清洗
                .map(new SingleLegOrderCleaningFunction())
                .name("SingleLegOrder-Cleaning")
                
                // 数据验证和过滤
                .process(new SingleLegOrderValidationProcessFunction())
                .name("SingleLegOrder-Validation")
                
                // 过滤空值
                .filter(new FilterFunction<SingleLegOrder>() {
                    @Override
                    public boolean filter(SingleLegOrder order) throws Exception {
                        return order != null;
                    }
                })
                .name("SingleLegOrder-NullFilter");
    }
    
    /**
     * 预处理组合委托订单数据流
     */
    public static SingleOutputStreamOperator<CombinationOrder> preprocessCombinationOrders(
            DataStream<CombinationOrder> inputStream) {
        
        return inputStream
                // 数据清洗
                .map(new CombinationOrderCleaningFunction())
                .name("CombinationOrder-Cleaning")
                
                // 数据验证和过滤
                .process(new CombinationOrderValidationProcessFunction())
                .name("CombinationOrder-Validation")
                
                // 过滤空值
                .filter(new FilterFunction<CombinationOrder>() {
                    @Override
                    public boolean filter(CombinationOrder order) throws Exception {
                        return order != null;
                    }
                })
                .name("CombinationOrder-NullFilter");
    }
    
    /**
     * 预处理成交明细数据流
     */
    public static SingleOutputStreamOperator<TradeDetail> preprocessTradeDetails(
            DataStream<TradeDetail> inputStream) {
        
        return inputStream
                // 数据清洗
                .map(new TradeDetailCleaningFunction())
                .name("TradeDetail-Cleaning")
                
                // 数据验证和过滤
                .process(new TradeDetailValidationProcessFunction())
                .name("TradeDetail-Validation")
                
                // 过滤空值
                .filter(new FilterFunction<TradeDetail>() {
                    @Override
                    public boolean filter(TradeDetail trade) throws Exception {
                        return trade != null;
                    }
                })
                .name("TradeDetail-NullFilter");
    }
    
    /**
     * 单腿委托订单清洗函数
     */
    private static class SingleLegOrderCleaningFunction implements MapFunction<SingleLegOrder, SingleLegOrder> {
        @Override
        public SingleLegOrder map(SingleLegOrder order) throws Exception {
            if (order == null) {
                return null;
            }
            
            try {
                return DataCleaningUtils.cleanSingleLegOrder(order);
            } catch (Exception e) {
                LOG.error("Error cleaning SingleLegOrder: {}", order, e);
                return null;
            }
        }
    }
    
    /**
     * 组合委托订单清洗函数
     */
    private static class CombinationOrderCleaningFunction implements MapFunction<CombinationOrder, CombinationOrder> {
        @Override
        public CombinationOrder map(CombinationOrder order) throws Exception {
            if (order == null) {
                return null;
            }
            
            try {
                return DataCleaningUtils.cleanCombinationOrder(order);
            } catch (Exception e) {
                LOG.error("Error cleaning CombinationOrder: {}", order, e);
                return null;
            }
        }
    }
    
    /**
     * 成交明细清洗函数
     */
    private static class TradeDetailCleaningFunction implements MapFunction<TradeDetail, TradeDetail> {
        @Override
        public TradeDetail map(TradeDetail trade) throws Exception {
            if (trade == null) {
                return null;
            }
            
            try {
                return DataCleaningUtils.cleanTradeDetail(trade);
            } catch (Exception e) {
                LOG.error("Error cleaning TradeDetail: {}", trade, e);
                return null;
            }
        }
    }
    
    /**
     * 预处理所有数据流
     */
    public static class PreprocessedDataStreams {
        private final SingleOutputStreamOperator<SingleLegOrder> singleLegOrderStream;
        private final SingleOutputStreamOperator<CombinationOrder> combinationOrderStream;
        private final SingleOutputStreamOperator<TradeDetail> tradeDetailStream;
        
        public PreprocessedDataStreams(
                SingleOutputStreamOperator<SingleLegOrder> singleLegOrderStream,
                SingleOutputStreamOperator<CombinationOrder> combinationOrderStream,
                SingleOutputStreamOperator<TradeDetail> tradeDetailStream) {
            this.singleLegOrderStream = singleLegOrderStream;
            this.combinationOrderStream = combinationOrderStream;
            this.tradeDetailStream = tradeDetailStream;
        }
        
        public SingleOutputStreamOperator<SingleLegOrder> getSingleLegOrderStream() {
            return singleLegOrderStream;
        }
        
        public SingleOutputStreamOperator<CombinationOrder> getCombinationOrderStream() {
            return combinationOrderStream;
        }
        
        public SingleOutputStreamOperator<TradeDetail> getTradeDetailStream() {
            return tradeDetailStream;
        }
        
        /**
         * 获取无效单腿订单侧输出流
         */
        public DataStream<SingleLegOrder> getInvalidSingleLegOrders() {
            return singleLegOrderStream.getSideOutput(INVALID_SINGLE_LEG_ORDER_TAG);
        }
        
        /**
         * 获取无效组合订单侧输出流
         */
        public DataStream<CombinationOrder> getInvalidCombinationOrders() {
            return combinationOrderStream.getSideOutput(INVALID_COMBINATION_ORDER_TAG);
        }
        
        /**
         * 获取无效成交明细侧输出流
         */
        public DataStream<TradeDetail> getInvalidTradeDetails() {
            return tradeDetailStream.getSideOutput(INVALID_TRADE_DETAIL_TAG);
        }
    }
    
    /**
     * 预处理所有数据流的便捷方法
     */
    public static PreprocessedDataStreams preprocessAllStreams(
            DataStream<SingleLegOrder> singleLegOrderStream,
            DataStream<CombinationOrder> combinationOrderStream,
            DataStream<TradeDetail> tradeDetailStream) {
        
        SingleOutputStreamOperator<SingleLegOrder> cleanedSingleLegOrders = 
                preprocessSingleLegOrders(singleLegOrderStream);
        
        SingleOutputStreamOperator<CombinationOrder> cleanedCombinationOrders = 
                preprocessCombinationOrders(combinationOrderStream);
        
        SingleOutputStreamOperator<TradeDetail> cleanedTradeDetails = 
                preprocessTradeDetails(tradeDetailStream);
        
        return new PreprocessedDataStreams(
                cleanedSingleLegOrders, cleanedCombinationOrders, cleanedTradeDetails);
    }

    /**
     * 单腿委托订单验证ProcessFunction
     */
    private static class SingleLegOrderValidationProcessFunction
            extends ProcessFunction<SingleLegOrder, SingleLegOrder> {

        @Override
        public void processElement(SingleLegOrder order, Context ctx, Collector<SingleLegOrder> out)
                throws Exception {
            if (order == null) {
                return;
            }

            DataValidationUtils.ValidationResult result =
                    DataValidationUtils.validateSingleLegOrder(order);

            if (result.isValid()) {
                out.collect(order);
            } else {
                LOG.warn("Invalid SingleLegOrder: {} - {}", order, result.getErrorMessage());
                ctx.output(INVALID_SINGLE_LEG_ORDER_TAG, order);
            }
        }
    }

    /**
     * 组合委托订单验证ProcessFunction
     */
    private static class CombinationOrderValidationProcessFunction
            extends ProcessFunction<CombinationOrder, CombinationOrder> {

        @Override
        public void processElement(CombinationOrder order, Context ctx, Collector<CombinationOrder> out)
                throws Exception {
            if (order == null) {
                return;
            }

            DataValidationUtils.ValidationResult result =
                    DataValidationUtils.validateCombinationOrder(order);

            if (result.isValid()) {
                out.collect(order);
            } else {
                LOG.warn("Invalid CombinationOrder: {} - {}", order, result.getErrorMessage());
                ctx.output(INVALID_COMBINATION_ORDER_TAG, order);
            }
        }
    }

    /**
     * 成交明细验证ProcessFunction
     */
    private static class TradeDetailValidationProcessFunction
            extends ProcessFunction<TradeDetail, TradeDetail> {

        @Override
        public void processElement(TradeDetail trade, Context ctx, Collector<TradeDetail> out)
                throws Exception {
            if (trade == null) {
                return;
            }

            DataValidationUtils.ValidationResult result =
                    DataValidationUtils.validateTradeDetail(trade);

            if (result.isValid()) {
                out.collect(trade);
            } else {
                LOG.warn("Invalid TradeDetail: {} - {}", trade, result.getErrorMessage());
                ctx.output(INVALID_TRADE_DETAIL_TAG, trade);
            }
        }
    }
}
