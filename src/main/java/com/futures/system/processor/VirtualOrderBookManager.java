package com.futures.system.processor;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 虚拟层订单簿管理器
 * 整合基础订单簿和组合委托处理，提供完整的虚拟层订单簿功能
 */
public class VirtualOrderBookManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(VirtualOrderBookManager.class);
    
    /**
     * 创建虚拟层订单簿处理流水线
     * 
     * @param baseOrderBookStream 基础订单簿数据流
     * @param combinationOrderStream 组合委托数据流
     * @return 虚拟层订单簿快照数据流
     */
    public static SingleOutputStreamOperator<OrderBookSnapshot> createVirtualOrderBookPipeline(
            DataStream<BaseOrderBook> baseOrderBookStream,
            DataStream<CombinationOrder> combinationOrderStream) {
        
        LOG.info("Creating virtual order book processing pipeline");
        
        // 1. 合并基础订单簿和组合委托，按合约代码分区
        SingleOutputStreamOperator<VirtualOrderBook> virtualOrderBookStream = baseOrderBookStream
                .keyBy(BaseOrderBook::getContractCode)
                .connect(combinationOrderStream.keyBy(order -> order.getLeg1ContractCode())) // 按腿1合约分区
                .process(new VirtualOrderBookProcessor())
                .name("VirtualOrderBookProcessor");
        
        // 2. 生成虚拟层定时快照
        SingleOutputStreamOperator<OrderBookSnapshot> snapshotStream = virtualOrderBookStream
                .keyBy(virtualBook -> virtualBook.getBaseOrderBook().getContractCode())
                .process(new VirtualOrderBookSnapshotGenerator())
                .name("VirtualOrderBookSnapshotGenerator");
        
        LOG.info("Virtual order book pipeline created successfully");
        
        return snapshotStream;
    }
    
    /**
     * 创建完整的订单簿处理流水线（基础层+虚拟层）
     * 
     * @param singleLegOrderStream 单腿委托数据流
     * @param combinationOrderStream 组合委托数据流
     * @param tradeDetailStream 成交明细数据流
     * @return 完整的订单簿处理结果
     */
    public static CompleteOrderBookStreams createCompleteOrderBookPipeline(
            DataStream<SingleLegOrder> singleLegOrderStream,
            DataStream<CombinationOrder> combinationOrderStream,
            DataStream<TradeDetail> tradeDetailStream) {
        
        LOG.info("Creating complete order book processing pipeline");
        
        // 1. 创建基础层订单簿
        SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream = 
                BaseOrderBookManager.createBaseOrderBookPipeline(singleLegOrderStream, tradeDetailStream);
        
        // 2. 提取基础订单簿流（用于虚拟层处理）
        SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream = singleLegOrderStream
                .keyBy(SingleLegOrder::getContractCode)
                .connect(tradeDetailStream.keyBy(TradeDetail::getContractCode))
                .process(new OrderBookEventMerger())
                .name("BaseOrderBookForVirtual");
        
        // 3. 创建虚拟层订单簿
        SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream = 
                createVirtualOrderBookPipeline(baseOrderBookStream, combinationOrderStream);
        
        LOG.info("Complete order book pipeline created successfully");
        
        return new CompleteOrderBookStreams(
                baseSnapshotStream,
                virtualSnapshotStream,
                baseOrderBookStream
        );
    }
    
    /**
     * 创建分层的订单簿处理流水线
     * 
     * @param baseOrderBookStream 基础订单簿数据流
     * @param combinationOrderStream 组合委托数据流
     * @return 分层处理结果
     */
    public static LayeredOrderBookStreams createLayeredOrderBookPipeline(
            DataStream<BaseOrderBook> baseOrderBookStream,
            DataStream<CombinationOrder> combinationOrderStream) {
        
        LOG.info("Creating layered order book processing pipeline");
        
        // 1. 基础层快照
        SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream = baseOrderBookStream
                .keyBy(BaseOrderBook::getContractCode)
                .process(new OrderBookSnapshotGenerator())
                .name("BaseOrderBookSnapshot");
        
        // 2. 虚拟层处理
        SingleOutputStreamOperator<VirtualOrderBook> virtualOrderBookStream = baseOrderBookStream
                .keyBy(BaseOrderBook::getContractCode)
                .connect(combinationOrderStream.keyBy(order -> order.getLeg1ContractCode()))
                .process(new VirtualOrderBookProcessor())
                .name("VirtualOrderBookProcessor");
        
        // 3. 虚拟层快照
        SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream = virtualOrderBookStream
                .keyBy(virtualBook -> virtualBook.getBaseOrderBook().getContractCode())
                .process(new VirtualOrderBookSnapshotGenerator())
                .name("VirtualOrderBookSnapshot");
        
        LOG.info("Layered order book pipeline created successfully");
        
        return new LayeredOrderBookStreams(
                baseSnapshotStream,
                virtualSnapshotStream,
                virtualOrderBookStream
        );
    }
    
    /**
     * 完整订单簿数据流集合
     */
    public static class CompleteOrderBookStreams {
        private final SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream;
        private final SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream;
        private final SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream;
        
        public CompleteOrderBookStreams(
                SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream,
                SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream,
                SingleOutputStreamOperator<BaseOrderBook> baseOrderBookStream) {
            this.baseSnapshotStream = baseSnapshotStream;
            this.virtualSnapshotStream = virtualSnapshotStream;
            this.baseOrderBookStream = baseOrderBookStream;
        }
        
        public SingleOutputStreamOperator<OrderBookSnapshot> getBaseSnapshotStream() {
            return baseSnapshotStream;
        }
        
        public SingleOutputStreamOperator<OrderBookSnapshot> getVirtualSnapshotStream() {
            return virtualSnapshotStream;
        }
        
        public SingleOutputStreamOperator<BaseOrderBook> getBaseOrderBookStream() {
            return baseOrderBookStream;
        }
    }
    
    /**
     * 分层订单簿数据流集合
     */
    public static class LayeredOrderBookStreams {
        private final SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream;
        private final SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream;
        private final SingleOutputStreamOperator<VirtualOrderBook> virtualOrderBookStream;
        
        public LayeredOrderBookStreams(
                SingleOutputStreamOperator<OrderBookSnapshot> baseSnapshotStream,
                SingleOutputStreamOperator<OrderBookSnapshot> virtualSnapshotStream,
                SingleOutputStreamOperator<VirtualOrderBook> virtualOrderBookStream) {
            this.baseSnapshotStream = baseSnapshotStream;
            this.virtualSnapshotStream = virtualSnapshotStream;
            this.virtualOrderBookStream = virtualOrderBookStream;
        }
        
        public SingleOutputStreamOperator<OrderBookSnapshot> getBaseSnapshotStream() {
            return baseSnapshotStream;
        }
        
        public SingleOutputStreamOperator<OrderBookSnapshot> getVirtualSnapshotStream() {
            return virtualSnapshotStream;
        }
        
        public SingleOutputStreamOperator<VirtualOrderBook> getVirtualOrderBookStream() {
            return virtualOrderBookStream;
        }
    }
    
    /**
     * 虚拟层配置
     */
    public static class VirtualLayerConfig {
        private int snapshotDepth = 5;
        private long snapshotIntervalMs = 500L;
        private boolean enableNegativeVolume = true;
        private boolean enablePriceRecalculation = true;
        private long priceUpdateTimeoutMs = 5000L;
        
        public int getSnapshotDepth() {
            return snapshotDepth;
        }
        
        public void setSnapshotDepth(int snapshotDepth) {
            this.snapshotDepth = snapshotDepth;
        }
        
        public long getSnapshotIntervalMs() {
            return snapshotIntervalMs;
        }
        
        public void setSnapshotIntervalMs(long snapshotIntervalMs) {
            this.snapshotIntervalMs = snapshotIntervalMs;
        }
        
        public boolean isEnableNegativeVolume() {
            return enableNegativeVolume;
        }
        
        public void setEnableNegativeVolume(boolean enableNegativeVolume) {
            this.enableNegativeVolume = enableNegativeVolume;
        }
        
        public boolean isEnablePriceRecalculation() {
            return enablePriceRecalculation;
        }
        
        public void setEnablePriceRecalculation(boolean enablePriceRecalculation) {
            this.enablePriceRecalculation = enablePriceRecalculation;
        }
        
        public long getPriceUpdateTimeoutMs() {
            return priceUpdateTimeoutMs;
        }
        
        public void setPriceUpdateTimeoutMs(long priceUpdateTimeoutMs) {
            this.priceUpdateTimeoutMs = priceUpdateTimeoutMs;
        }
    }
}
