package com.futures.system.sink;

import com.futures.system.model.*;
import org.apache.flink.api.common.functions.RichSinkFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 控制台调试输出Sink
 * 用于开发和调试阶段的数据输出监控
 */
public class ConsoleDebugSink {
    
    private static final Logger LOG = LoggerFactory.getLogger(ConsoleDebugSink.class);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
    
    /**
     * 订单簿快照控制台输出Sink
     */
    public static class OrderBookSnapshotConsoleSink extends RichSinkFunction<OrderBookSnapshot> {
        
        private transient AtomicLong counter;
        private long outputInterval = 1000L; // 1秒输出一次
        private long lastOutputTime = 0L;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            counter = new AtomicLong(0);
            LOG.info("OrderBookSnapshot Console Sink opened");
        }
        
        @Override
        public void invoke(OrderBookSnapshot snapshot, Context context) throws Exception {
            long currentTime = System.currentTimeMillis();
            long count = counter.incrementAndGet();
            
            // 控制输出频率
            if (currentTime - lastOutputTime >= outputInterval) {
                lastOutputTime = currentTime;
                
                String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
                
                System.out.println("=== 订单簿快照 [" + timestamp + "] ===");
                System.out.printf("合约: %s | 序列: %d | 虚拟层: %s%n",
                        snapshot.getContractCode(),
                        snapshot.getSnapshotSequence(),
                        snapshot.getHasVirtualLayer() ? "是" : "否");
                
                System.out.printf("最优买价: %s(%s) | 最优卖价: %s(%s)%n",
                        formatPrice(snapshot.getBestBidPrice()),
                        formatVolume(snapshot.getBestBidVolume()),
                        formatPrice(snapshot.getBestAskPrice()),
                        formatVolume(snapshot.getBestAskVolume()));
                
                System.out.printf("买方档位: %d | 卖方档位: %d | 总订单: %d%n",
                        snapshot.getBidLevels().size(),
                        snapshot.getAskLevels().size(),
                        snapshot.getTotalBidOrders() + snapshot.getTotalAskOrders());
                
                System.out.printf("买方总量: %s | 卖方总量: %s%n",
                        formatVolume(snapshot.getTotalBidVolume()),
                        formatVolume(snapshot.getTotalAskVolume()));
                
                System.out.println("----------------------------------------");
                
                if (count % 10 == 0) {
                    LOG.info("Processed {} OrderBookSnapshot records", count);
                }
            }
        }
        
        @Override
        public void close() throws Exception {
            super.close();
            LOG.info("OrderBookSnapshot Console Sink closed, total processed: {}", counter.get());
        }
    }
    
    /**
     * 盈亏计算控制台输出Sink
     */
    public static class PnLCalculationConsoleSink extends RichSinkFunction<PnLCalculation> {
        
        private transient AtomicLong counter;
        private long outputInterval = 2000L; // 2秒输出一次
        private long lastOutputTime = 0L;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            counter = new AtomicLong(0);
            LOG.info("PnLCalculation Console Sink opened");
        }
        
        @Override
        public void invoke(PnLCalculation calculation, Context context) throws Exception {
            long currentTime = System.currentTimeMillis();
            long count = counter.incrementAndGet();
            
            // 控制输出频率
            if (currentTime - lastOutputTime >= outputInterval) {
                lastOutputTime = currentTime;
                
                String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
                
                System.out.println("=== 盈亏计算 [" + timestamp + "] ===");
                System.out.printf("结算会员: %s | 合约: %s%n",
                        calculation.getSettlementMemberCode(),
                        calculation.getContractCode());
                
                System.out.printf("净持仓: %s | 开仓均价: %s | 最新价: %s%n",
                        formatVolume(calculation.getCurrentPosition()),
                        formatPrice(calculation.getAverageOpenPrice()),
                        formatPrice(calculation.getLatestTradePrice()));
                
                System.out.printf("已实现盈亏: %s | 浮动盈亏: %s | 总盈亏: %s%n",
                        formatPnL(calculation.getRealizedPnL()),
                        formatPnL(calculation.getUnrealizedPnL()),
                        formatPnL(calculation.getTotalPnL()));
                
                System.out.printf("开仓量: %s | 平仓量: %s | 开仓金额: %s%n",
                        formatVolume(calculation.getOpenVolume()),
                        formatVolume(calculation.getCloseVolume()),
                        formatAmount(calculation.getOpenAmount()));
                
                System.out.println("----------------------------------------");
                
                if (count % 5 == 0) {
                    LOG.info("Processed {} PnLCalculation records", count);
                }
            }
        }
        
        @Override
        public void close() throws Exception {
            super.close();
            LOG.info("PnLCalculation Console Sink closed, total processed: {}", counter.get());
        }
    }
    
    /**
     * 盈亏汇总控制台输出Sink
     */
    public static class PnLSummaryConsoleSink extends RichSinkFunction<PnLSummary> {
        
        private transient AtomicLong counter;
        private long outputInterval = 5000L; // 5秒输出一次
        private long lastOutputTime = 0L;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            counter = new AtomicLong(0);
            LOG.info("PnLSummary Console Sink opened");
        }
        
        @Override
        public void invoke(PnLSummary summary, Context context) throws Exception {
            long currentTime = System.currentTimeMillis();
            long count = counter.incrementAndGet();
            
            // 控制输出频率
            if (currentTime - lastOutputTime >= outputInterval) {
                lastOutputTime = currentTime;
                
                String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
                
                System.out.println("=== 盈亏汇总 [" + timestamp + "] ===");
                System.out.printf("结算会员: %s | 序列: %d%n",
                        summary.getSettlementMemberCode(),
                        summary.getSummarySequence());
                
                System.out.printf("总盈亏: %s | 已实现: %s | 浮动: %s%n",
                        formatPnL(summary.getTotalPnL()),
                        formatPnL(summary.getTotalRealizedPnL()),
                        formatPnL(summary.getTotalUnrealizedPnL()));
                
                System.out.printf("总收益率: %s%% | 已实现收益率: %s%%\n",
                        formatPercentage(summary.getTotalReturnRate()),
                        formatPercentage(summary.getRealizedReturnRate()));
                
                System.out.printf("合约总数: %d | 有持仓: %d | 盈利: %d | 亏损: %d%n",
                        summary.getTotalContracts(),
                        summary.getActiveContracts(),
                        summary.getProfitableContractsCount(),
                        summary.getLossContractsCount());
                
                System.out.printf("风险等级: %s | 最大盈亏: %s | 最小盈亏: %s%n",
                        summary.getRiskLevel().getDescription(),
                        formatPnL(summary.getMaxContractPnL()),
                        formatPnL(summary.getMinContractPnL()));
                
                System.out.printf("开仓金额: %s | 平仓金额: %s%n",
                        formatAmount(summary.getTotalOpenAmount()),
                        formatAmount(summary.getTotalCloseAmount()));
                
                System.out.println("========================================");
                
                if (count % 3 == 0) {
                    LOG.info("Processed {} PnLSummary records", count);
                }
            }
        }
        
        @Override
        public void close() throws Exception {
            super.close();
            LOG.info("PnLSummary Console Sink closed, total processed: {}", counter.get());
        }
    }
    
    // 格式化工具方法
    private static String formatPrice(java.math.BigDecimal price) {
        return price != null ? String.format("%.2f", price) : "N/A";
    }
    
    private static String formatVolume(java.math.BigDecimal volume) {
        return volume != null ? String.format("%.0f", volume) : "0";
    }
    
    private static String formatPnL(java.math.BigDecimal pnl) {
        if (pnl == null) return "0.00";
        String formatted = String.format("%.2f", pnl);
        return pnl.compareTo(java.math.BigDecimal.ZERO) >= 0 ? "+" + formatted : formatted;
    }
    
    private static String formatAmount(java.math.BigDecimal amount) {
        return amount != null ? String.format("%.2f", amount) : "0.00";
    }
    
    private static String formatPercentage(java.math.BigDecimal rate) {
        if (rate == null) return "0.00";
        return String.format("%.2f", rate.multiply(java.math.BigDecimal.valueOf(100)));
    }
}
