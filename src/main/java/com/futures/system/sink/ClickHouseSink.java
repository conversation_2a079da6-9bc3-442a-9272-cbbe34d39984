package com.futures.system.sink;

import com.futures.system.model.*;
import org.apache.flink.api.common.functions.RichSinkFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * ClickHouse输出Sink
 * 支持批量写入和连接池管理
 */
public class ClickHouseSink {
    
    private static final Logger LOG = LoggerFactory.getLogger(ClickHouseSink.class);
    
    /**
     * ClickHouse配置
     */
    public static class ClickHouseConfig {
        private String jdbcUrl = "****************************************";
        private String username = "default";
        private String password = "";
        private int batchSize = 1000;
        private long flushIntervalMs = 5000L;
        private int maxRetries = 3;
        private long retryDelayMs = 1000L;
        private int connectionPoolSize = 5;
        
        // Getters and Setters
        public String getJdbcUrl() { return jdbcUrl; }
        public void setJdbcUrl(String jdbcUrl) { this.jdbcUrl = jdbcUrl; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        
        public long getFlushIntervalMs() { return flushIntervalMs; }
        public void setFlushIntervalMs(long flushIntervalMs) { this.flushIntervalMs = flushIntervalMs; }
        
        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }
        
        public long getRetryDelayMs() { return retryDelayMs; }
        public void setRetryDelayMs(long retryDelayMs) { this.retryDelayMs = retryDelayMs; }
        
        public int getConnectionPoolSize() { return connectionPoolSize; }
        public void setConnectionPoolSize(int connectionPoolSize) { this.connectionPoolSize = connectionPoolSize; }
    }
    
    /**
     * 订单簿快照ClickHouse Sink
     */
    public static class OrderBookSnapshotClickHouseSink extends RichSinkFunction<OrderBookSnapshot> {
        
        private final ClickHouseConfig config;
        private transient Connection connection;
        private transient PreparedStatement insertStatement;
        private transient List<OrderBookSnapshot> batch;
        private transient ScheduledExecutorService scheduler;
        private transient long lastFlushTime;
        
        public OrderBookSnapshotClickHouseSink(ClickHouseConfig config) {
            this.config = config;
        }
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            // 初始化连接
            initConnection();
            
            // 初始化批次
            batch = new ArrayList<>(config.getBatchSize());
            lastFlushTime = System.currentTimeMillis();
            
            // 启动定时刷新
            scheduler = Executors.newSingleThreadScheduledExecutor();
            scheduler.scheduleAtFixedRate(this::flushBatch, 
                    config.getFlushIntervalMs(), config.getFlushIntervalMs(), TimeUnit.MILLISECONDS);
            
            LOG.info("OrderBookSnapshot ClickHouse Sink opened with batch size: {}", config.getBatchSize());
        }
        
        @Override
        public void invoke(OrderBookSnapshot snapshot, Context context) throws Exception {
            synchronized (batch) {
                batch.add(snapshot);
                
                if (batch.size() >= config.getBatchSize()) {
                    flushBatch();
                }
            }
        }
        
        private void flushBatch() {
            synchronized (batch) {
                if (batch.isEmpty()) {
                    return;
                }
                
                try {
                    executeBatch();
                    batch.clear();
                    lastFlushTime = System.currentTimeMillis();
                } catch (Exception e) {
                    LOG.error("Failed to flush OrderBookSnapshot batch", e);
                    // 重试逻辑
                    retryBatch();
                }
            }
        }
        
        private void executeBatch() throws SQLException {
            String sql = "INSERT INTO futures_orderbook_snapshots (" +
                    "snapshot_time, snapshot_sequence, contract_code, trade_date, exchange_code, commodity_code, " +
                    "best_bid_price, best_ask_price, best_bid_volume, best_ask_volume, " +
                    "bid_levels_count, ask_levels_count, base_bid_levels_count, base_ask_levels_count, " +
                    "total_bid_volume, total_ask_volume, total_bid_orders, total_ask_orders, has_virtual_layer" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                for (OrderBookSnapshot snapshot : batch) {
                    stmt.setTimestamp(1, Timestamp.valueOf(snapshot.getSnapshotTime()));
                    stmt.setLong(2, snapshot.getSnapshotSequence());
                    stmt.setString(3, snapshot.getContractCode());
                    stmt.setDate(4, Date.valueOf(snapshot.getTradeDate()));
                    stmt.setString(5, snapshot.getExchangeCode());
                    stmt.setString(6, snapshot.getCommodityCode());
                    
                    setBigDecimalOrNull(stmt, 7, snapshot.getBestBidPrice());
                    setBigDecimalOrNull(stmt, 8, snapshot.getBestAskPrice());
                    setBigDecimalOrNull(stmt, 9, snapshot.getBestBidVolume());
                    setBigDecimalOrNull(stmt, 10, snapshot.getBestAskVolume());
                    
                    stmt.setInt(11, snapshot.getBidLevels().size());
                    stmt.setInt(12, snapshot.getAskLevels().size());
                    stmt.setInt(13, snapshot.getBaseBidLevelsCount());
                    stmt.setInt(14, snapshot.getBaseAskLevelsCount());
                    
                    setBigDecimalOrNull(stmt, 15, snapshot.getTotalBidVolume());
                    setBigDecimalOrNull(stmt, 16, snapshot.getTotalAskVolume());
                    stmt.setInt(17, snapshot.getTotalBidOrders());
                    stmt.setInt(18, snapshot.getTotalAskOrders());
                    stmt.setInt(19, snapshot.getHasVirtualLayer() ? 1 : 0);
                    
                    stmt.addBatch();
                }
                
                stmt.executeBatch();
                LOG.debug("Successfully inserted {} OrderBookSnapshot records", batch.size());
            }
        }
        
        private void retryBatch() {
            for (int i = 0; i < config.getMaxRetries(); i++) {
                try {
                    Thread.sleep(config.getRetryDelayMs() * (i + 1));
                    initConnection(); // 重新初始化连接
                    executeBatch();
                    batch.clear();
                    LOG.info("Successfully retried OrderBookSnapshot batch after {} attempts", i + 1);
                    return;
                } catch (Exception e) {
                    LOG.warn("Retry {} failed for OrderBookSnapshot batch", i + 1, e);
                }
            }
            
            LOG.error("Failed to insert OrderBookSnapshot batch after {} retries, dropping {} records", 
                    config.getMaxRetries(), batch.size());
            batch.clear();
        }
        
        private void initConnection() throws SQLException {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            
            Properties props = new Properties();
            props.setProperty("user", config.getUsername());
            props.setProperty("password", config.getPassword());
            props.setProperty("socket_timeout", "30000");
            props.setProperty("connection_timeout", "10000");
            
            connection = DriverManager.getConnection(config.getJdbcUrl(), props);
            connection.setAutoCommit(true);
        }
        
        private void setBigDecimalOrNull(PreparedStatement stmt, int index, java.math.BigDecimal value) throws SQLException {
            if (value != null) {
                stmt.setBigDecimal(index, value);
            } else {
                stmt.setNull(index, Types.DECIMAL);
            }
        }
        
        @Override
        public void close() throws Exception {
            if (scheduler != null) {
                scheduler.shutdown();
            }

            // 刷新剩余数据
            flushBatch();

            if (connection != null && !connection.isClosed()) {
                connection.close();
            }

            super.close();
            LOG.info("OrderBookSnapshot ClickHouse Sink closed");
        }
    }

    /**
     * 盈亏计算ClickHouse Sink
     */
    public static class PnLCalculationClickHouseSink extends RichSinkFunction<PnLCalculation> {

        private final ClickHouseConfig config;
        private transient Connection connection;
        private transient List<PnLCalculation> batch;
        private transient ScheduledExecutorService scheduler;

        public PnLCalculationClickHouseSink(ClickHouseConfig config) {
            this.config = config;
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            initConnection();
            batch = new ArrayList<>(config.getBatchSize());

            scheduler = Executors.newSingleThreadScheduledExecutor();
            scheduler.scheduleAtFixedRate(this::flushBatch,
                    config.getFlushIntervalMs(), config.getFlushIntervalMs(), TimeUnit.MILLISECONDS);

            LOG.info("PnLCalculation ClickHouse Sink opened");
        }

        @Override
        public void invoke(PnLCalculation calculation, Context context) throws Exception {
            synchronized (batch) {
                batch.add(calculation);
                if (batch.size() >= config.getBatchSize()) {
                    flushBatch();
                }
            }
        }

        private void flushBatch() {
            synchronized (batch) {
                if (batch.isEmpty()) return;

                try {
                    executeBatch();
                    batch.clear();
                } catch (Exception e) {
                    LOG.error("Failed to flush PnLCalculation batch", e);
                    retryBatch();
                }
            }
        }

        private void executeBatch() throws SQLException {
            String sql = "INSERT INTO futures_pnl_calculations (" +
                    "calculation_time, settlement_member_code, contract_code, trade_date, " +
                    "current_position, open_volume, close_volume, average_open_price, latest_trade_price, " +
                    "realized_pnl, unrealized_pnl, total_pnl, open_amount, close_amount, " +
                    "net_position_side, update_sequence" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                for (PnLCalculation calc : batch) {
                    stmt.setTimestamp(1, Timestamp.valueOf(calc.getCalculationTime()));
                    stmt.setString(2, calc.getSettlementMemberCode());
                    stmt.setString(3, calc.getContractCode());
                    stmt.setDate(4, Date.valueOf(calc.getTradeDate()));

                    setBigDecimalOrNull(stmt, 5, calc.getCurrentPosition());
                    setBigDecimalOrNull(stmt, 6, calc.getOpenVolume());
                    setBigDecimalOrNull(stmt, 7, calc.getCloseVolume());
                    setBigDecimalOrNull(stmt, 8, calc.getAverageOpenPrice());
                    setBigDecimalOrNull(stmt, 9, calc.getLatestTradePrice());

                    setBigDecimalOrNull(stmt, 10, calc.getRealizedPnL());
                    setBigDecimalOrNull(stmt, 11, calc.getUnrealizedPnL());
                    setBigDecimalOrNull(stmt, 12, calc.getTotalPnL());
                    setBigDecimalOrNull(stmt, 13, calc.getOpenAmount());
                    setBigDecimalOrNull(stmt, 14, calc.getCloseAmount());

                    setBigDecimalOrNull(stmt, 15, calc.getNetPositionSide());
                    stmt.setLong(16, calc.getUpdateSequence());

                    stmt.addBatch();
                }

                stmt.executeBatch();
                LOG.debug("Successfully inserted {} PnLCalculation records", batch.size());
            }
        }

        private void retryBatch() {
            for (int i = 0; i < config.getMaxRetries(); i++) {
                try {
                    Thread.sleep(config.getRetryDelayMs() * (i + 1));
                    initConnection();
                    executeBatch();
                    batch.clear();
                    LOG.info("Successfully retried PnLCalculation batch after {} attempts", i + 1);
                    return;
                } catch (Exception e) {
                    LOG.warn("Retry {} failed for PnLCalculation batch", i + 1, e);
                }
            }

            LOG.error("Failed to insert PnLCalculation batch after {} retries, dropping {} records",
                    config.getMaxRetries(), batch.size());
            batch.clear();
        }

        private void initConnection() throws SQLException {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }

            Properties props = new Properties();
            props.setProperty("user", config.getUsername());
            props.setProperty("password", config.getPassword());

            connection = DriverManager.getConnection(config.getJdbcUrl(), props);
            connection.setAutoCommit(true);
        }

        private void setBigDecimalOrNull(PreparedStatement stmt, int index, java.math.BigDecimal value) throws SQLException {
            if (value != null) {
                stmt.setBigDecimal(index, value);
            } else {
                stmt.setNull(index, Types.DECIMAL);
            }
        }

        @Override
        public void close() throws Exception {
            if (scheduler != null) {
                scheduler.shutdown();
            }
            flushBatch();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            super.close();
            LOG.info("PnLCalculation ClickHouse Sink closed");
        }
    }

    /**
     * 盈亏汇总ClickHouse Sink
     */
    public static class PnLSummaryClickHouseSink extends RichSinkFunction<PnLSummary> {

        private final ClickHouseConfig config;
        private transient Connection connection;
        private transient List<PnLSummary> batch;
        private transient ScheduledExecutorService scheduler;

        public PnLSummaryClickHouseSink(ClickHouseConfig config) {
            this.config = config;
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            initConnection();
            batch = new ArrayList<>(config.getBatchSize());

            scheduler = Executors.newSingleThreadScheduledExecutor();
            scheduler.scheduleAtFixedRate(this::flushBatch,
                    config.getFlushIntervalMs(), config.getFlushIntervalMs(), TimeUnit.MILLISECONDS);

            LOG.info("PnLSummary ClickHouse Sink opened");
        }

        @Override
        public void invoke(PnLSummary summary, Context context) throws Exception {
            synchronized (batch) {
                batch.add(summary);
                if (batch.size() >= config.getBatchSize()) {
                    flushBatch();
                }
            }
        }

        private void flushBatch() {
            synchronized (batch) {
                if (batch.isEmpty()) return;

                try {
                    executeBatch();
                    batch.clear();
                } catch (Exception e) {
                    LOG.error("Failed to flush PnLSummary batch", e);
                    retryBatch();
                }
            }
        }

        private void executeBatch() throws SQLException {
            String sql = "INSERT INTO futures_pnl_summaries (" +
                    "summary_time, summary_sequence, settlement_member_code, " +
                    "total_realized_pnl, total_unrealized_pnl, total_pnl, " +
                    "total_open_amount, total_close_amount, total_return_rate, realized_return_rate, " +
                    "total_contracts, active_contracts, max_contract_pnl, min_contract_pnl" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                for (PnLSummary summary : batch) {
                    stmt.setTimestamp(1, Timestamp.valueOf(summary.getSummaryTime()));
                    stmt.setLong(2, summary.getSummarySequence());
                    stmt.setString(3, summary.getSettlementMemberCode());

                    setBigDecimalOrNull(stmt, 4, summary.getTotalRealizedPnL());
                    setBigDecimalOrNull(stmt, 5, summary.getTotalUnrealizedPnL());
                    setBigDecimalOrNull(stmt, 6, summary.getTotalPnL());

                    setBigDecimalOrNull(stmt, 7, summary.getTotalOpenAmount());
                    setBigDecimalOrNull(stmt, 8, summary.getTotalCloseAmount());
                    setBigDecimalOrNull(stmt, 9, summary.getTotalReturnRate());
                    setBigDecimalOrNull(stmt, 10, summary.getRealizedReturnRate());

                    stmt.setInt(11, summary.getTotalContracts());
                    stmt.setInt(12, summary.getActiveContracts());
                    setBigDecimalOrNull(stmt, 13, summary.getMaxContractPnL());
                    setBigDecimalOrNull(stmt, 14, summary.getMinContractPnL());

                    stmt.addBatch();
                }

                stmt.executeBatch();
                LOG.debug("Successfully inserted {} PnLSummary records", batch.size());
            }
        }

        private void retryBatch() {
            for (int i = 0; i < config.getMaxRetries(); i++) {
                try {
                    Thread.sleep(config.getRetryDelayMs() * (i + 1));
                    initConnection();
                    executeBatch();
                    batch.clear();
                    LOG.info("Successfully retried PnLSummary batch after {} attempts", i + 1);
                    return;
                } catch (Exception e) {
                    LOG.warn("Retry {} failed for PnLSummary batch", i + 1, e);
                }
            }

            LOG.error("Failed to insert PnLSummary batch after {} retries, dropping {} records",
                    config.getMaxRetries(), batch.size());
            batch.clear();
        }

        private void initConnection() throws SQLException {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }

            Properties props = new Properties();
            props.setProperty("user", config.getUsername());
            props.setProperty("password", config.getPassword());

            connection = DriverManager.getConnection(config.getJdbcUrl(), props);
            connection.setAutoCommit(true);
        }

        private void setBigDecimalOrNull(PreparedStatement stmt, int index, java.math.BigDecimal value) throws SQLException {
            if (value != null) {
                stmt.setBigDecimal(index, value);
            } else {
                stmt.setNull(index, Types.DECIMAL);
            }
        }

        @Override
        public void close() throws Exception {
            if (scheduler != null) {
                scheduler.shutdown();
            }
            flushBatch();
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            super.close();
            LOG.info("PnLSummary ClickHouse Sink closed");
        }
    }
}
