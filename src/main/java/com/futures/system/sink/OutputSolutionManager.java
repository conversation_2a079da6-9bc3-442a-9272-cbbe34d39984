package com.futures.system.sink;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 输出方案管理器
 * 统一管理调试输出和生产输出两套完整方案
 */
public class OutputSolutionManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(OutputSolutionManager.class);
    
    /**
     * 输出方案类型
     */
    public enum OutputSolutionType {
        DEBUG_CONSOLE("调试控制台输出"),
        PRODUCTION_CLICKHOUSE("生产ClickHouse输出"),
        HYBRID("混合输出");
        
        private final String description;
        
        OutputSolutionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 输出方案配置
     */
    public static class OutputSolutionConfig {
        private OutputSolutionType solutionType;
        private DebugOutputManager.DebugConfig debugConfig;
        private ClickHouseSink.ClickHouseConfig clickHouseConfig;
        private boolean enableDebugOutput = false;
        private boolean enableClickHouseOutput = false;
        
        public OutputSolutionConfig(OutputSolutionType solutionType) {
            this.solutionType = solutionType;
            
            switch (solutionType) {
                case DEBUG_CONSOLE:
                    this.enableDebugOutput = true;
                    this.enableClickHouseOutput = false;
                    this.debugConfig = DebugOutputManager.createDefaultConfig();
                    break;
                    
                case PRODUCTION_CLICKHOUSE:
                    this.enableDebugOutput = false;
                    this.enableClickHouseOutput = true;
                    this.clickHouseConfig = ClickHouseOutputManager.createDefaultConfig();
                    break;
                    
                case HYBRID:
                    this.enableDebugOutput = true;
                    this.enableClickHouseOutput = true;
                    this.debugConfig = DebugOutputManager.createProductionConfig();
                    this.clickHouseConfig = ClickHouseOutputManager.createDefaultConfig();
                    break;
            }
        }
        
        // Getters and Setters
        public OutputSolutionType getSolutionType() { return solutionType; }
        public void setSolutionType(OutputSolutionType solutionType) { this.solutionType = solutionType; }
        
        public DebugOutputManager.DebugConfig getDebugConfig() { return debugConfig; }
        public void setDebugConfig(DebugOutputManager.DebugConfig debugConfig) { this.debugConfig = debugConfig; }
        
        public ClickHouseSink.ClickHouseConfig getClickHouseConfig() { return clickHouseConfig; }
        public void setClickHouseConfig(ClickHouseSink.ClickHouseConfig clickHouseConfig) { this.clickHouseConfig = clickHouseConfig; }
        
        public boolean isEnableDebugOutput() { return enableDebugOutput; }
        public void setEnableDebugOutput(boolean enableDebugOutput) { this.enableDebugOutput = enableDebugOutput; }
        
        public boolean isEnableClickHouseOutput() { return enableClickHouseOutput; }
        public void setEnableClickHouseOutput(boolean enableClickHouseOutput) { this.enableClickHouseOutput = enableClickHouseOutput; }
    }
    
    /**
     * 应用完整输出方案
     */
    public static void applyOutputSolution(
            DataStream<OrderBookSnapshot> snapshotStream,
            DataStream<PnLCalculation> pnlCalculationStream,
            DataStream<PnLSummary> pnlSummaryStream,
            OutputSolutionConfig config) {
        
        LOG.info("Applying output solution: {}", config.getSolutionType().getDescription());
        
        // 应用调试输出
        if (config.isEnableDebugOutput() && config.getDebugConfig() != null) {
            LOG.info("Applying debug console output");
            DebugOutputManager.applyAllDebugOutputs(
                    snapshotStream, pnlCalculationStream, pnlSummaryStream, config.getDebugConfig());
        }
        
        // 应用ClickHouse输出
        if (config.isEnableClickHouseOutput() && config.getClickHouseConfig() != null) {
            LOG.info("Applying ClickHouse production output");
            
            // 验证ClickHouse连接
            if (ClickHouseOutputManager.validateConnection(config.getClickHouseConfig())) {
                ClickHouseOutputManager.applyAllClickHouseOutputs(
                        snapshotStream, pnlCalculationStream, pnlSummaryStream, config.getClickHouseConfig());
            } else {
                LOG.error("ClickHouse connection validation failed, skipping ClickHouse output");
            }
        }
        
        LOG.info("Output solution applied successfully");
    }
    
    /**
     * 创建调试输出方案配置
     */
    public static OutputSolutionConfig createDebugSolution() {
        OutputSolutionConfig config = new OutputSolutionConfig(OutputSolutionType.DEBUG_CONSOLE);
        config.setDebugConfig(DebugOutputManager.createDevelopmentConfig());
        return config;
    }
    
    /**
     * 创建生产输出方案配置
     */
    public static OutputSolutionConfig createProductionSolution(
            String clickHouseUrl, String username, String password) {
        OutputSolutionConfig config = new OutputSolutionConfig(OutputSolutionType.PRODUCTION_CLICKHOUSE);
        config.setClickHouseConfig(ClickHouseOutputManager.createProductionConfig(
                clickHouseUrl, username, password));
        return config;
    }
    
    /**
     * 创建混合输出方案配置
     */
    public static OutputSolutionConfig createHybridSolution(
            String clickHouseUrl, String username, String password) {
        OutputSolutionConfig config = new OutputSolutionConfig(OutputSolutionType.HYBRID);
        
        // 生产级别的ClickHouse配置
        config.setClickHouseConfig(ClickHouseOutputManager.createProductionConfig(
                clickHouseUrl, username, password));
        
        // 低频率的调试输出
        config.setDebugConfig(DebugOutputManager.createProductionConfig());
        
        return config;
    }
    
    /**
     * 从环境变量创建输出方案配置
     */
    public static OutputSolutionConfig createConfigFromEnvironment() {
        String solutionType = System.getenv("OUTPUT_SOLUTION_TYPE");
        if (solutionType == null) {
            solutionType = "DEBUG_CONSOLE";
        }
        
        OutputSolutionConfig config;
        
        switch (solutionType.toUpperCase()) {
            case "PRODUCTION_CLICKHOUSE":
                config = new OutputSolutionConfig(OutputSolutionType.PRODUCTION_CLICKHOUSE);
                config.setClickHouseConfig(ClickHouseOutputManager.createConfigFromEnv());
                break;
                
            case "HYBRID":
                config = new OutputSolutionConfig(OutputSolutionType.HYBRID);
                config.setClickHouseConfig(ClickHouseOutputManager.createConfigFromEnv());
                config.setDebugConfig(DebugOutputManager.createProductionConfig());
                break;
                
            default:
                config = new OutputSolutionConfig(OutputSolutionType.DEBUG_CONSOLE);
                config.setDebugConfig(DebugOutputManager.createDefaultConfig());
                break;
        }
        
        LOG.info("Created output solution config from environment: {}", config.getSolutionType());
        return config;
    }
    
    /**
     * 验证输出方案配置
     */
    public static boolean validateOutputSolution(OutputSolutionConfig config) {
        if (config == null) {
            LOG.error("Output solution config is null");
            return false;
        }
        
        if (config.isEnableClickHouseOutput()) {
            if (config.getClickHouseConfig() == null) {
                LOG.error("ClickHouse config is required but not provided");
                return false;
            }
            
            if (!ClickHouseOutputManager.validateConnection(config.getClickHouseConfig())) {
                LOG.error("ClickHouse connection validation failed");
                return false;
            }
        }
        
        if (config.isEnableDebugOutput() && config.getDebugConfig() == null) {
            LOG.warn("Debug config is null, using default config");
            config.setDebugConfig(DebugOutputManager.createDefaultConfig());
        }
        
        LOG.info("Output solution validation successful");
        return true;
    }
    
    /**
     * 打印输出方案配置信息
     */
    public static void printConfigInfo(OutputSolutionConfig config) {
        LOG.info("=== 输出方案配置信息 ===");
        LOG.info("方案类型: {}", config.getSolutionType().getDescription());
        LOG.info("调试输出: {}", config.isEnableDebugOutput() ? "启用" : "禁用");
        LOG.info("ClickHouse输出: {}", config.isEnableClickHouseOutput() ? "启用" : "禁用");
        
        if (config.isEnableClickHouseOutput() && config.getClickHouseConfig() != null) {
            ClickHouseSink.ClickHouseConfig chConfig = config.getClickHouseConfig();
            LOG.info("ClickHouse URL: {}", chConfig.getJdbcUrl());
            LOG.info("批次大小: {}", chConfig.getBatchSize());
            LOG.info("刷新间隔: {}ms", chConfig.getFlushIntervalMs());
        }
        
        if (config.isEnableDebugOutput() && config.getDebugConfig() != null) {
            DebugOutputManager.DebugConfig debugConfig = config.getDebugConfig();
            LOG.info("订单簿输出间隔: {}ms", debugConfig.getOrderBookOutputInterval());
            LOG.info("盈亏计算输出间隔: {}ms", debugConfig.getPnlCalculationOutputInterval());
            LOG.info("盈亏汇总输出间隔: {}ms", debugConfig.getPnlSummaryOutputInterval());
        }
        
        LOG.info("========================");
    }
}
