package com.futures.system.sink;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 调试输出管理器
 * 统一管理所有调试输出Sink的配置和应用
 */
public class DebugOutputManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(DebugOutputManager.class);
    
    /**
     * 调试输出配置
     */
    public static class DebugConfig {
        private boolean enableOrderBookOutput = true;
        private boolean enablePnLCalculationOutput = true;
        private boolean enablePnLSummaryOutput = true;
        private long orderBookOutputInterval = 1000L; // 1秒
        private long pnlCalculationOutputInterval = 2000L; // 2秒
        private long pnlSummaryOutputInterval = 5000L; // 5秒
        private String logLevel = "INFO";
        
        public boolean isEnableOrderBookOutput() {
            return enableOrderBookOutput;
        }
        
        public void setEnableOrderBookOutput(boolean enableOrderBookOutput) {
            this.enableOrderBookOutput = enableOrderBookOutput;
        }
        
        public boolean isEnablePnLCalculationOutput() {
            return enablePnLCalculationOutput;
        }
        
        public void setEnablePnLCalculationOutput(boolean enablePnLCalculationOutput) {
            this.enablePnLCalculationOutput = enablePnLCalculationOutput;
        }
        
        public boolean isEnablePnLSummaryOutput() {
            return enablePnLSummaryOutput;
        }
        
        public void setEnablePnLSummaryOutput(boolean enablePnLSummaryOutput) {
            this.enablePnLSummaryOutput = enablePnLSummaryOutput;
        }
        
        public long getOrderBookOutputInterval() {
            return orderBookOutputInterval;
        }
        
        public void setOrderBookOutputInterval(long orderBookOutputInterval) {
            this.orderBookOutputInterval = orderBookOutputInterval;
        }
        
        public long getPnlCalculationOutputInterval() {
            return pnlCalculationOutputInterval;
        }
        
        public void setPnlCalculationOutputInterval(long pnlCalculationOutputInterval) {
            this.pnlCalculationOutputInterval = pnlCalculationOutputInterval;
        }
        
        public long getPnlSummaryOutputInterval() {
            return pnlSummaryOutputInterval;
        }
        
        public void setPnlSummaryOutputInterval(long pnlSummaryOutputInterval) {
            this.pnlSummaryOutputInterval = pnlSummaryOutputInterval;
        }
        
        public String getLogLevel() {
            return logLevel;
        }
        
        public void setLogLevel(String logLevel) {
            this.logLevel = logLevel;
        }
    }
    
    /**
     * 应用调试输出到订单簿快照流
     */
    public static void applyOrderBookDebugOutput(DataStream<OrderBookSnapshot> snapshotStream, DebugConfig config) {
        if (config.isEnableOrderBookOutput()) {
            LOG.info("Applying debug output to OrderBookSnapshot stream with interval: {}ms", 
                    config.getOrderBookOutputInterval());
            
            snapshotStream
                    .addSink(new ConsoleDebugSink.OrderBookSnapshotConsoleSink())
                    .name("OrderBookSnapshot-DebugOutput")
                    .setParallelism(1);
        }
    }
    
    /**
     * 应用调试输出到盈亏计算流
     */
    public static void applyPnLCalculationDebugOutput(DataStream<PnLCalculation> pnlStream, DebugConfig config) {
        if (config.isEnablePnLCalculationOutput()) {
            LOG.info("Applying debug output to PnLCalculation stream with interval: {}ms", 
                    config.getPnlCalculationOutputInterval());
            
            pnlStream
                    .addSink(new ConsoleDebugSink.PnLCalculationConsoleSink())
                    .name("PnLCalculation-DebugOutput")
                    .setParallelism(1);
        }
    }
    
    /**
     * 应用调试输出到盈亏汇总流
     */
    public static void applyPnLSummaryDebugOutput(DataStream<PnLSummary> summaryStream, DebugConfig config) {
        if (config.isEnablePnLSummaryOutput()) {
            LOG.info("Applying debug output to PnLSummary stream with interval: {}ms", 
                    config.getPnlSummaryOutputInterval());
            
            summaryStream
                    .addSink(new ConsoleDebugSink.PnLSummaryConsoleSink())
                    .name("PnLSummary-DebugOutput")
                    .setParallelism(1);
        }
    }
    
    /**
     * 应用所有调试输出
     */
    public static void applyAllDebugOutputs(
            DataStream<OrderBookSnapshot> snapshotStream,
            DataStream<PnLCalculation> pnlCalculationStream,
            DataStream<PnLSummary> pnlSummaryStream,
            DebugConfig config) {
        
        LOG.info("Applying all debug outputs with config: {}", config);
        
        applyOrderBookDebugOutput(snapshotStream, config);
        applyPnLCalculationDebugOutput(pnlCalculationStream, config);
        applyPnLSummaryDebugOutput(pnlSummaryStream, config);
        
        LOG.info("All debug outputs applied successfully");
    }
    
    /**
     * 创建默认调试配置
     */
    public static DebugConfig createDefaultConfig() {
        return new DebugConfig();
    }
    
    /**
     * 创建生产环境调试配置（较低频率输出）
     */
    public static DebugConfig createProductionConfig() {
        DebugConfig config = new DebugConfig();
        config.setOrderBookOutputInterval(5000L); // 5秒
        config.setPnlCalculationOutputInterval(10000L); // 10秒
        config.setPnlSummaryOutputInterval(30000L); // 30秒
        config.setLogLevel("WARN");
        return config;
    }
    
    /**
     * 创建开发环境调试配置（高频率输出）
     */
    public static DebugConfig createDevelopmentConfig() {
        DebugConfig config = new DebugConfig();
        config.setOrderBookOutputInterval(500L); // 0.5秒
        config.setPnlCalculationOutputInterval(1000L); // 1秒
        config.setPnlSummaryOutputInterval(2000L); // 2秒
        config.setLogLevel("DEBUG");
        return config;
    }
    
    /**
     * 创建测试环境调试配置（禁用部分输出）
     */
    public static DebugConfig createTestConfig() {
        DebugConfig config = new DebugConfig();
        config.setEnableOrderBookOutput(false); // 测试时禁用订单簿输出
        config.setPnlCalculationOutputInterval(3000L); // 3秒
        config.setPnlSummaryOutputInterval(10000L); // 10秒
        config.setLogLevel("INFO");
        return config;
    }
}
