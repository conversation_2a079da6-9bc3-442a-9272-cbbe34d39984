package com.futures.system.sink;

import com.futures.system.model.*;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ClickHouse输出管理器
 * 统一管理所有ClickHouse输出Sink的配置和应用
 */
public class ClickHouseOutputManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(ClickHouseOutputManager.class);
    
    /**
     * 应用ClickHouse输出到订单簿快照流
     */
    public static void applyOrderBookClickHouseOutput(
            DataStream<OrderBookSnapshot> snapshotStream, 
            ClickHouseSink.ClickHouseConfig config) {
        
        LOG.info("Applying ClickHouse output to OrderBookSnapshot stream");
        
        snapshotStream
                .addSink(new ClickHouseSink.OrderBookSnapshotClickHouseSink(config))
                .name("OrderBookSnapshot-ClickHouseOutput")
                .setParallelism(2); // 适当的并行度
    }
    
    /**
     * 应用ClickHouse输出到盈亏计算流
     */
    public static void applyPnLCalculationClickHouseOutput(
            DataStream<PnLCalculation> pnlStream, 
            ClickHouseSink.ClickHouseConfig config) {
        
        LOG.info("Applying ClickHouse output to PnLCalculation stream");
        
        pnlStream
                .addSink(new ClickHouseSink.PnLCalculationClickHouseSink(config))
                .name("PnLCalculation-ClickHouseOutput")
                .setParallelism(2);
    }
    
    /**
     * 应用ClickHouse输出到盈亏汇总流
     */
    public static void applyPnLSummaryClickHouseOutput(
            DataStream<PnLSummary> summaryStream, 
            ClickHouseSink.ClickHouseConfig config) {
        
        LOG.info("Applying ClickHouse output to PnLSummary stream");
        
        summaryStream
                .addSink(new ClickHouseSink.PnLSummaryClickHouseSink(config))
                .name("PnLSummary-ClickHouseOutput")
                .setParallelism(1); // 汇总数据量较小，使用单并行度
    }
    
    /**
     * 应用所有ClickHouse输出
     */
    public static void applyAllClickHouseOutputs(
            DataStream<OrderBookSnapshot> snapshotStream,
            DataStream<PnLCalculation> pnlCalculationStream,
            DataStream<PnLSummary> pnlSummaryStream,
            ClickHouseSink.ClickHouseConfig config) {
        
        LOG.info("Applying all ClickHouse outputs");
        
        applyOrderBookClickHouseOutput(snapshotStream, config);
        applyPnLCalculationClickHouseOutput(pnlCalculationStream, config);
        applyPnLSummaryClickHouseOutput(pnlSummaryStream, config);
        
        LOG.info("All ClickHouse outputs applied successfully");
    }
    
    /**
     * 创建默认ClickHouse配置
     */
    public static ClickHouseSink.ClickHouseConfig createDefaultConfig() {
        ClickHouseSink.ClickHouseConfig config = new ClickHouseSink.ClickHouseConfig();
        config.setJdbcUrl("****************************************");
        config.setUsername("default");
        config.setPassword("");
        config.setBatchSize(1000);
        config.setFlushIntervalMs(5000L);
        config.setMaxRetries(3);
        config.setRetryDelayMs(1000L);
        config.setConnectionPoolSize(5);
        return config;
    }
    
    /**
     * 创建生产环境ClickHouse配置
     */
    public static ClickHouseSink.ClickHouseConfig createProductionConfig(
            String jdbcUrl, String username, String password) {
        ClickHouseSink.ClickHouseConfig config = new ClickHouseSink.ClickHouseConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        config.setBatchSize(2000); // 生产环境更大的批次
        config.setFlushIntervalMs(3000L); // 更频繁的刷新
        config.setMaxRetries(5); // 更多重试次数
        config.setRetryDelayMs(2000L);
        config.setConnectionPoolSize(10); // 更大的连接池
        return config;
    }
    
    /**
     * 创建高性能ClickHouse配置
     */
    public static ClickHouseSink.ClickHouseConfig createHighPerformanceConfig(
            String jdbcUrl, String username, String password) {
        ClickHouseSink.ClickHouseConfig config = new ClickHouseSink.ClickHouseConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        config.setBatchSize(5000); // 大批次提高吞吐量
        config.setFlushIntervalMs(1000L); // 快速刷新保证实时性
        config.setMaxRetries(3);
        config.setRetryDelayMs(500L);
        config.setConnectionPoolSize(15);
        return config;
    }
    
    /**
     * 从环境变量创建ClickHouse配置
     */
    public static ClickHouseSink.ClickHouseConfig createConfigFromEnv() {
        ClickHouseSink.ClickHouseConfig config = new ClickHouseSink.ClickHouseConfig();
        
        // 从环境变量读取配置
        String jdbcUrl = System.getenv("CLICKHOUSE_JDBC_URL");
        if (jdbcUrl != null) {
            config.setJdbcUrl(jdbcUrl);
        }
        
        String username = System.getenv("CLICKHOUSE_USERNAME");
        if (username != null) {
            config.setUsername(username);
        }
        
        String password = System.getenv("CLICKHOUSE_PASSWORD");
        if (password != null) {
            config.setPassword(password);
        }
        
        String batchSize = System.getenv("CLICKHOUSE_BATCH_SIZE");
        if (batchSize != null) {
            config.setBatchSize(Integer.parseInt(batchSize));
        }
        
        String flushInterval = System.getenv("CLICKHOUSE_FLUSH_INTERVAL_MS");
        if (flushInterval != null) {
            config.setFlushIntervalMs(Long.parseLong(flushInterval));
        }
        
        LOG.info("Created ClickHouse config from environment variables: {}", config.getJdbcUrl());
        return config;
    }
    
    /**
     * 验证ClickHouse连接
     */
    public static boolean validateConnection(ClickHouseSink.ClickHouseConfig config) {
        try {
            java.sql.Connection conn = java.sql.DriverManager.getConnection(
                    config.getJdbcUrl(), config.getUsername(), config.getPassword());
            
            // 执行简单查询验证连接
            try (java.sql.Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT 1");
            }
            
            conn.close();
            LOG.info("ClickHouse connection validation successful");
            return true;
        } catch (Exception e) {
            LOG.error("ClickHouse connection validation failed", e);
            return false;
        }
    }
    
    /**
     * 创建表（如果不存在）
     */
    public static void createTablesIfNotExists(ClickHouseSink.ClickHouseConfig config) {
        try {
            java.sql.Connection conn = java.sql.DriverManager.getConnection(
                    config.getJdbcUrl(), config.getUsername(), config.getPassword());
            
            // 读取并执行建表SQL
            String createTablesSql = loadCreateTablesSQL();
            
            try (java.sql.Statement stmt = conn.createStatement()) {
                String[] sqlStatements = createTablesSql.split(";");
                for (String sql : sqlStatements) {
                    if (!sql.trim().isEmpty()) {
                        stmt.execute(sql.trim());
                    }
                }
            }
            
            conn.close();
            LOG.info("ClickHouse tables created successfully");
        } catch (Exception e) {
            LOG.error("Failed to create ClickHouse tables", e);
            throw new RuntimeException("Failed to create ClickHouse tables", e);
        }
    }
    
    /**
     * 加载建表SQL（简化实现）
     */
    private static String loadCreateTablesSQL() {
        // 这里应该从resources/clickhouse/futures_system_tables.sql文件读取
        // 为简化示例，返回基本的建表语句
        return "-- Tables will be created from SQL file";
    }
}
