package com.futures.system.mock;

import com.futures.system.model.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模拟数据集合
 * 包含完整的期货交易模拟数据，支持JSON序列化和数据统计
 */
public class MockDataSet {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataSet.class);
    
    @JsonProperty("single_leg_orders")
    private List<SingleLegOrder> singleLegOrders = new ArrayList<>();
    
    @JsonProperty("trade_details")
    private List<TradeDetail> tradeDetails = new ArrayList<>();
    
    @JsonProperty("cancel_orders")
    private List<SingleLegOrder> cancelOrders = new ArrayList<>();
    
    @JsonProperty("combination_orders")
    private List<CombinationOrder> combinationOrders = new ArrayList<>();
    
    @JsonProperty("generation_metadata")
    private GenerationMetadata metadata = new GenerationMetadata();
    
    /**
     * 生成元数据
     */
    public static class GenerationMetadata {
        @JsonProperty("generation_time")
        private String generationTime = java.time.LocalDateTime.now().toString();
        
        @JsonProperty("total_orders")
        private int totalOrders;
        
        @JsonProperty("total_trades")
        private int totalTrades;
        
        @JsonProperty("total_cancels")
        private int totalCancels;
        
        @JsonProperty("total_combinations")
        private int totalCombinations;
        
        @JsonProperty("contracts")
        private List<String> contracts = new ArrayList<>();
        
        @JsonProperty("members")
        private List<String> members = new ArrayList<>();
        
        @JsonProperty("time_range")
        private TimeRange timeRange = new TimeRange();
        
        // Getters and Setters
        public String getGenerationTime() { return generationTime; }
        public void setGenerationTime(String generationTime) { this.generationTime = generationTime; }
        
        public int getTotalOrders() { return totalOrders; }
        public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
        
        public int getTotalTrades() { return totalTrades; }
        public void setTotalTrades(int totalTrades) { this.totalTrades = totalTrades; }
        
        public int getTotalCancels() { return totalCancels; }
        public void setTotalCancels(int totalCancels) { this.totalCancels = totalCancels; }
        
        public int getTotalCombinations() { return totalCombinations; }
        public void setTotalCombinations(int totalCombinations) { this.totalCombinations = totalCombinations; }
        
        public List<String> getContracts() { return contracts; }
        public void setContracts(List<String> contracts) { this.contracts = contracts; }
        
        public List<String> getMembers() { return members; }
        public void setMembers(List<String> members) { this.members = members; }
        
        public TimeRange getTimeRange() { return timeRange; }
        public void setTimeRange(TimeRange timeRange) { this.timeRange = timeRange; }
    }
    
    /**
     * 时间范围
     */
    public static class TimeRange {
        @JsonProperty("start_time")
        private String startTime;
        
        @JsonProperty("end_time")
        private String endTime;
        
        @JsonProperty("duration_seconds")
        private double durationSeconds;
        
        // Getters and Setters
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        
        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
        
        public double getDurationSeconds() { return durationSeconds; }
        public void setDurationSeconds(double durationSeconds) { this.durationSeconds = durationSeconds; }
    }
    
    /**
     * 更新元数据
     */
    public void updateMetadata() {
        metadata.setTotalOrders(singleLegOrders.size());
        metadata.setTotalTrades(tradeDetails.size());
        metadata.setTotalCancels(cancelOrders.size());
        metadata.setTotalCombinations(combinationOrders.size());
        
        // 统计合约
        List<String> contracts = singleLegOrders.stream()
                .map(SingleLegOrder::getContractCode)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        metadata.setContracts(contracts);
        
        // 统计会员
        List<String> members = singleLegOrders.stream()
                .map(SingleLegOrder::getSettlementMemberCode)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        metadata.setMembers(members);
        
        // 计算时间范围
        if (!singleLegOrders.isEmpty()) {
            java.time.LocalTime minTime = singleLegOrders.stream()
                    .map(SingleLegOrder::getOrderTime)
                    .min(java.time.LocalTime::compareTo)
                    .orElse(java.time.LocalTime.now());
            
            java.time.LocalTime maxTime = singleLegOrders.stream()
                    .map(SingleLegOrder::getOrderTime)
                    .max(java.time.LocalTime::compareTo)
                    .orElse(java.time.LocalTime.now());
            
            metadata.getTimeRange().setStartTime(minTime.toString());
            metadata.getTimeRange().setEndTime(maxTime.toString());
            metadata.getTimeRange().setDurationSeconds(
                    java.time.Duration.between(minTime, maxTime).toMillis() / 1000.0);
        }
    }
    
    /**
     * 保存到JSON文件
     */
    public void saveToJsonFile(String filePath) throws IOException {
        updateMetadata();
        
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.writerWithDefaultPrettyPrinter().writeValue(new File(filePath), this);
        
        LOG.info("模拟数据已保存到文件: {}", filePath);
    }
    
    /**
     * 从JSON文件加载
     */
    public static MockDataSet loadFromJsonFile(String filePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        
        MockDataSet dataSet = mapper.readValue(new File(filePath), MockDataSet.class);
        LOG.info("模拟数据已从文件加载: {}", filePath);
        
        return dataSet;
    }
    
    /**
     * 打印数据统计信息
     */
    public void printStatistics() {
        updateMetadata();
        
        LOG.info("=== 模拟数据统计信息 ===");
        LOG.info("生成时间: {}", metadata.getGenerationTime());
        LOG.info("单腿委托: {} 条", metadata.getTotalOrders());
        LOG.info("成交明细: {} 条", metadata.getTotalTrades());
        LOG.info("撤销订单: {} 条", metadata.getTotalCancels());
        LOG.info("组合委托: {} 条", metadata.getTotalCombinations());
        LOG.info("涉及合约: {}", String.join(", ", metadata.getContracts()));
        LOG.info("涉及会员: {}", String.join(", ", metadata.getMembers()));
        LOG.info("时间范围: {} - {} ({}秒)", 
                metadata.getTimeRange().getStartTime(),
                metadata.getTimeRange().getEndTime(),
                metadata.getTimeRange().getDurationSeconds());
        
        // 按合约统计
        Map<String, Long> contractStats = singleLegOrders.stream()
                .collect(Collectors.groupingBy(SingleLegOrder::getContractCode, Collectors.counting()));
        
        LOG.info("按合约统计:");
        contractStats.forEach((contract, count) -> 
                LOG.info("  {}: {} 条订单", contract, count));
        
        // 按会员统计
        Map<String, Long> memberStats = singleLegOrders.stream()
                .collect(Collectors.groupingBy(SingleLegOrder::getSettlementMemberCode, Collectors.counting()));
        
        LOG.info("按会员统计:");
        memberStats.forEach((member, count) -> 
                LOG.info("  {}: {} 条订单", member, count));
        
        LOG.info("========================");
    }
    
    /**
     * 验证数据完整性
     */
    public boolean validateDataIntegrity() {
        boolean isValid = true;
        
        // 检查基本数据
        if (singleLegOrders.isEmpty()) {
            LOG.error("单腿委托数据为空");
            isValid = false;
        }
        
        // 检查成交数据与委托数据的对应关系
        java.util.Set<String> orderNumbers = singleLegOrders.stream()
                .map(SingleLegOrder::getOrderNumber)
                .collect(Collectors.toSet());
        
        java.util.Set<String> combinationOrderNumbers = combinationOrders.stream()
                .map(CombinationOrder::getOrderNumber)
                .collect(Collectors.toSet());
        
        long orphanTrades = tradeDetails.stream()
                .filter(trade -> !orderNumbers.contains(trade.getOrderNumber()) && 
                               !combinationOrderNumbers.contains(trade.getOrderNumber()))
                .count();
        
        if (orphanTrades > 0) {
            LOG.error("发现 {} 条孤立的成交记录", orphanTrades);
            isValid = false;
        }
        
        // 检查时间序列
        boolean timeValid = singleLegOrders.stream()
                .allMatch(order -> order.getOrderTime() != null);
        
        if (!timeValid) {
            LOG.error("发现时间为空的订单");
            isValid = false;
        }
        
        LOG.info("数据完整性验证结果: {}", isValid ? "通过" : "失败");
        return isValid;
    }
    
    // Getters and Setters
    public List<SingleLegOrder> getSingleLegOrders() {
        return singleLegOrders;
    }
    
    public void setSingleLegOrders(List<SingleLegOrder> singleLegOrders) {
        this.singleLegOrders = singleLegOrders;
    }
    
    public List<TradeDetail> getTradeDetails() {
        return tradeDetails;
    }
    
    public void setTradeDetails(List<TradeDetail> tradeDetails) {
        this.tradeDetails = tradeDetails;
    }
    
    public List<SingleLegOrder> getCancelOrders() {
        return cancelOrders;
    }
    
    public void setCancelOrders(List<SingleLegOrder> cancelOrders) {
        this.cancelOrders = cancelOrders;
    }
    
    public List<CombinationOrder> getCombinationOrders() {
        return combinationOrders;
    }
    
    public void setCombinationOrders(List<CombinationOrder> combinationOrders) {
        this.combinationOrders = combinationOrders;
    }
    
    public GenerationMetadata getMetadata() {
        return metadata;
    }
    
    public void setMetadata(GenerationMetadata metadata) {
        this.metadata = metadata;
    }
}
