package com.futures.system.mock;

import com.futures.system.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 模拟数据生成器
 * 生成符合业务逻辑的期货交易模拟数据，用于本地调试和功能验证
 */
public class MockDataGenerator {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataGenerator.class);
    
    // 基准时间配置
    private static final LocalDate TRADE_DATE = LocalDate.now();
    private static final LocalTime BASE_TIME = LocalTime.of(9, 0, 0); // 9:00:00开始
    
    // 合约配置
    private static final String[] CONTRACTS = {"CU2401", "AL2401", "ZN2401", "PB2401", "SN2401"};
    private static final Map<String, ContractConfig> CONTRACT_CONFIGS = new HashMap<>();
    
    // 会员和交易编码配置
    private static final String[] MEMBER_CODES = {"1001", "1002", "1003", "1004", "1005"};
    private static final String[] TRADE_CODES = {"T001", "T002", "T003", "T004", "T005"};
    
    // 数据生成计数器
    private static long orderSequence = 1;
    private static long tradeSequence = 1;
    private static long combinationSequence = 1;
    
    static {
        // 初始化合约配置
        CONTRACT_CONFIGS.put("CU2401", new ContractConfig("CU2401", "SHFE", "CU", new BigDecimal("73000"), new BigDecimal("500")));
        CONTRACT_CONFIGS.put("AL2401", new ContractConfig("AL2401", "SHFE", "AL", new BigDecimal("19000"), new BigDecimal("200")));
        CONTRACT_CONFIGS.put("ZN2401", new ContractConfig("ZN2401", "SHFE", "ZN", new BigDecimal("25000"), new BigDecimal("300")));
        CONTRACT_CONFIGS.put("PB2401", new ContractConfig("PB2401", "SHFE", "PB", new BigDecimal("16000"), new BigDecimal("150")));
        CONTRACT_CONFIGS.put("SN2401", new ContractConfig("SN2401", "SHFE", "SN", new BigDecimal("240000"), new BigDecimal("1000")));
    }
    
    /**
     * 合约配置类
     */
    public static class ContractConfig {
        private final String contractCode;
        private final String exchangeCode;
        private final String commodityCode;
        private final BigDecimal basePrice;
        private final BigDecimal priceStep;
        
        public ContractConfig(String contractCode, String exchangeCode, String commodityCode, 
                            BigDecimal basePrice, BigDecimal priceStep) {
            this.contractCode = contractCode;
            this.exchangeCode = exchangeCode;
            this.commodityCode = commodityCode;
            this.basePrice = basePrice;
            this.priceStep = priceStep;
        }
        
        // Getters
        public String getContractCode() { return contractCode; }
        public String getExchangeCode() { return exchangeCode; }
        public String getCommodityCode() { return commodityCode; }
        public BigDecimal getBasePrice() { return basePrice; }
        public BigDecimal getPriceStep() { return priceStep; }
    }
    
    /**
     * 生成完整的模拟数据集
     */
    public static MockDataSet generateCompleteDataSet() {
        LOG.info("开始生成完整的模拟数据集...");
        
        MockDataSet dataSet = new MockDataSet();
        
        // 1. 生成单腿委托数据
        List<SingleLegOrder> singleLegOrders = generateSingleLegOrders(80);
        dataSet.setSingleLegOrders(singleLegOrders);
        LOG.info("生成单腿委托数据: {} 条", singleLegOrders.size());
        
        // 2. 生成成交数据
        List<TradeDetail> tradeDetails = generateTradeDetails(singleLegOrders);
        dataSet.setTradeDetails(tradeDetails);
        LOG.info("生成成交数据: {} 条", tradeDetails.size());
        
        // 3. 生成撤销数据
        List<SingleLegOrder> cancelOrders = generateCancelOrders(singleLegOrders);
        dataSet.getCancelOrders().addAll(cancelOrders);
        LOG.info("生成撤销数据: {} 条", cancelOrders.size());
        
        // 4. 生成组合委托数据
        List<CombinationOrder> combinationOrders = generateCombinationOrders(15);
        dataSet.setCombinationOrders(combinationOrders);
        LOG.info("生成组合委托数据: {} 条", combinationOrders.size());
        
        // 5. 生成组合成交数据
        List<TradeDetail> combinationTrades = generateCombinationTrades(combinationOrders);
        dataSet.getTradeDetails().addAll(combinationTrades);
        LOG.info("生成组合成交数据: {} 条", combinationTrades.size());
        
        // 6. 验证数据一致性
        validateDataConsistency(dataSet);
        
        LOG.info("模拟数据集生成完成 - 单腿委托: {}, 成交: {}, 撤销: {}, 组合委托: {}", 
                dataSet.getSingleLegOrders().size(), 
                dataSet.getTradeDetails().size(),
                dataSet.getCancelOrders().size(),
                dataSet.getCombinationOrders().size());
        
        return dataSet;
    }
    
    /**
     * 生成单腿委托订单
     */
    public static List<SingleLegOrder> generateSingleLegOrders(int count) {
        List<SingleLegOrder> orders = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重现
        
        for (int i = 0; i < count; i++) {
            String contractCode = CONTRACTS[i % CONTRACTS.length];
            ContractConfig config = CONTRACT_CONFIGS.get(contractCode);
            
            SingleLegOrder order = new SingleLegOrder();
            
            // 基本信息
            order.setOrderNumber(String.format("O%06d", orderSequence++));
            order.setContractCode(contractCode);
            order.setExchangeCode(config.getExchangeCode());
            order.setCommodityCode(config.getCommodityCode());
            order.setTradeDate(TRADE_DATE);
            
            // 时间信息（3秒内分布）
            LocalTime orderTime = BASE_TIME.plusNanos(i * 37_500_000L); // 每37.5毫秒一个订单
            order.setOrderTime(orderTime);
            
            // 价格和数量
            BigDecimal priceOffset = config.getPriceStep().multiply(BigDecimal.valueOf(random.nextInt(21) - 10)); // -10到+10档
            order.setOrderPrice(config.getBasePrice().add(priceOffset));
            order.setOrderVolume(BigDecimal.valueOf((random.nextInt(10) + 1) * 10)); // 10-100手
            order.setRemainingVolume(order.getOrderVolume());
            
            // 买卖方向
            order.setBuySellFlag(random.nextBoolean() ? BuySellFlag.BUY : BuySellFlag.SELL);
            
            // 开平仓类型
            order.setOpenCloseType(random.nextDouble() < 0.7 ? OpenCloseType.OPEN : OpenCloseType.CLOSE);
            
            // 订单类型
            order.setOrderType(random.nextDouble() < 0.9 ? OrderType.LIMIT : OrderType.MARKET);
            
            // 会员信息
            String memberCode = MEMBER_CODES[i % MEMBER_CODES.length];
            order.setMemberCode(memberCode);
            order.setTradeCode(TRADE_CODES[i % TRADE_CODES.length]);
            order.setSettlementMemberCode(memberCode);
            
            // 订单状态
            order.setOrderStatus(OrderStatus.NOT_FILLED_IN_QUEUE);
            
            orders.add(order);
        }
        
        return orders;
    }
    
    /**
     * 生成成交明细
     */
    public static List<TradeDetail> generateTradeDetails(List<SingleLegOrder> orders) {
        List<TradeDetail> trades = new ArrayList<>();
        Random random = new Random(43);
        
        for (SingleLegOrder order : orders) {
            // 80%的订单会成交
            if (random.nextDouble() < 0.8) {
                // 决定成交次数（1-3次）
                int tradeCount = random.nextInt(3) + 1;
                BigDecimal remainingVolume = order.getOrderVolume();
                
                for (int i = 0; i < tradeCount && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
                    TradeDetail trade = new TradeDetail();
                    
                    // 基本信息
                    trade.setTradeNumber(String.format("T%06d", tradeSequence++));
                    trade.setOrderNumber(order.getOrderNumber());
                    trade.setContractCode(order.getContractCode());
                    trade.setExchangeCode(order.getExchangeCode());
                    trade.setCommodityCode(order.getCommodityCode());
                    trade.setTradeDate(order.getTradeDate());
                    
                    // 成交时间（在委托时间后1-2秒内）
                    LocalTime tradeTime = order.getOrderTime().plusNanos((i + 1) * 500_000_000L + random.nextInt(500_000_000));
                    trade.setTradeTime(tradeTime);
                    
                    // 成交价格（在委托价格附近波动）
                    BigDecimal priceVariation = order.getOrderPrice().multiply(BigDecimal.valueOf(random.nextGaussian() * 0.001));
                    trade.setTradePrice(order.getOrderPrice().add(priceVariation));
                    
                    // 成交数量
                    BigDecimal maxTradeVolume = remainingVolume;
                    if (i < tradeCount - 1) {
                        // 不是最后一次成交，随机分配数量
                        maxTradeVolume = remainingVolume.multiply(BigDecimal.valueOf(0.3 + random.nextDouble() * 0.4));
                    }
                    trade.setTradeVolume(maxTradeVolume);
                    remainingVolume = remainingVolume.subtract(maxTradeVolume);
                    
                    // 其他信息
                    trade.setBuySellFlag(order.getBuySellFlag());
                    trade.setOpenCloseType(order.getOpenCloseType());
                    trade.setTradeType(TradeType.NORMAL);
                    trade.setMemberCode(order.getMemberCode());
                    trade.setTradeCode(order.getTradeCode());
                    trade.setSettlementMemberCode(order.getSettlementMemberCode());
                    
                    trades.add(trade);
                }
            }
        }
        
        return trades;
    }

    /**
     * 生成撤销订单
     */
    public static List<SingleLegOrder> generateCancelOrders(List<SingleLegOrder> originalOrders) {
        List<SingleLegOrder> cancelOrders = new ArrayList<>();
        Random random = new Random(44);

        // 选择20-30%的订单进行撤销
        int cancelCount = (int) (originalOrders.size() * (0.2 + random.nextDouble() * 0.1));
        List<SingleLegOrder> ordersToCancel = new ArrayList<>(originalOrders);
        Collections.shuffle(ordersToCancel, random);

        for (int i = 0; i < cancelCount && i < ordersToCancel.size(); i++) {
            SingleLegOrder originalOrder = ordersToCancel.get(i);
            SingleLegOrder cancelOrder = createCancelOrder(originalOrder, random);
            cancelOrders.add(cancelOrder);
        }

        return cancelOrders;
    }

    /**
     * 创建撤销订单
     */
    private static SingleLegOrder createCancelOrder(SingleLegOrder originalOrder, Random random) {
        SingleLegOrder cancelOrder = new SingleLegOrder();

        // 复制原订单信息
        cancelOrder.setOrderNumber(originalOrder.getOrderNumber());
        cancelOrder.setContractCode(originalOrder.getContractCode());
        cancelOrder.setExchangeCode(originalOrder.getExchangeCode());
        cancelOrder.setCommodityCode(originalOrder.getCommodityCode());
        cancelOrder.setTradeDate(originalOrder.getTradeDate());
        cancelOrder.setOrderPrice(originalOrder.getOrderPrice());
        cancelOrder.setOrderVolume(originalOrder.getOrderVolume());
        cancelOrder.setBuySellFlag(originalOrder.getBuySellFlag());
        cancelOrder.setOpenCloseType(originalOrder.getOpenCloseType());
        cancelOrder.setOrderType(originalOrder.getOrderType());
        cancelOrder.setMemberCode(originalOrder.getMemberCode());
        cancelOrder.setTradeCode(originalOrder.getTradeCode());
        cancelOrder.setSettlementMemberCode(originalOrder.getSettlementMemberCode());

        // 撤销时间（在原订单后1-2秒）
        LocalTime cancelTime = originalOrder.getOrderTime().plusSeconds(1 + random.nextInt(2));
        cancelOrder.setOrderTime(cancelTime);

        // 撤销状态和数量
        if (random.nextDouble() < 0.7) {
            // 全部撤销
            cancelOrder.setOrderStatus(OrderStatus.CANCELLED);
            cancelOrder.setRemainingVolume(BigDecimal.ZERO);
        } else {
            // 部分撤销
            cancelOrder.setOrderStatus(OrderStatus.PARTIALLY_CANCELLED);
            BigDecimal cancelVolume = originalOrder.getOrderVolume().multiply(BigDecimal.valueOf(0.3 + random.nextDouble() * 0.4));
            cancelOrder.setRemainingVolume(originalOrder.getOrderVolume().subtract(cancelVolume));
        }

        return cancelOrder;
    }

    /**
     * 生成组合委托订单
     */
    public static List<CombinationOrder> generateCombinationOrders(int count) {
        List<CombinationOrder> orders = new ArrayList<>();
        Random random = new Random(45);

        for (int i = 0; i < count; i++) {
            CombinationOrder order = new CombinationOrder();

            // 选择两个不同的合约作为腿
            String leg1Contract = CONTRACTS[i % CONTRACTS.length];
            String leg2Contract = CONTRACTS[(i + 1) % CONTRACTS.length];

            // 基本信息
            order.setOrderNumber(String.format("C%06d", combinationSequence++));
            order.setCombinationContractCode(leg1Contract + "&" + leg2Contract);
            order.setLeg1ContractCode(leg1Contract);
            order.setLeg2ContractCode(leg2Contract);
            order.setTradeDate(TRADE_DATE);

            // 时间信息（在单腿委托之后）
            LocalTime orderTime = BASE_TIME.plusSeconds(1).plusNanos(i * 50_000_000L);
            order.setOrderTime(orderTime);

            // 组合价格（价差）
            ContractConfig config1 = CONTRACT_CONFIGS.get(leg1Contract);
            ContractConfig config2 = CONTRACT_CONFIGS.get(leg2Contract);
            BigDecimal spreadPrice = config1.getBasePrice().subtract(config2.getBasePrice());
            BigDecimal priceVariation = BigDecimal.valueOf(random.nextGaussian() * 100);
            order.setOrderPrice(spreadPrice.add(priceVariation));

            // 数量
            order.setOrderVolume(BigDecimal.valueOf((random.nextInt(5) + 1) * 10)); // 10-50手
            order.setRemainingVolume(order.getOrderVolume());

            // 买卖方向
            order.setBuySellFlag(random.nextBoolean() ? BuySellFlag.BUY : BuySellFlag.SELL);

            // 开平仓类型
            order.setOpenCloseType(random.nextDouble() < 0.8 ? OpenCloseType.OPEN : OpenCloseType.CLOSE);

            // 订单类型
            order.setOrderType(OrderType.LIMIT);

            // 会员信息
            String memberCode = MEMBER_CODES[i % MEMBER_CODES.length];
            order.setMemberCode(memberCode);
            order.setTradeCode(TRADE_CODES[i % TRADE_CODES.length]);
            order.setSettlementMemberCode(memberCode);

            // 订单状态
            order.setOrderStatus(OrderStatus.NOT_FILLED_IN_QUEUE);

            orders.add(order);
        }

        return orders;
    }

    /**
     * 生成组合委托成交数据
     */
    public static List<TradeDetail> generateCombinationTrades(List<CombinationOrder> combinationOrders) {
        List<TradeDetail> trades = new ArrayList<>();
        Random random = new Random(46);

        for (CombinationOrder order : combinationOrders) {
            // 60%的组合委托会成交
            if (random.nextDouble() < 0.6) {
                // 为每个腿生成成交记录
                TradeDetail leg1Trade = createCombinationTrade(order, order.getLeg1ContractCode(), random, true);
                TradeDetail leg2Trade = createCombinationTrade(order, order.getLeg2ContractCode(), random, false);

                trades.add(leg1Trade);
                trades.add(leg2Trade);
            }
        }

        return trades;
    }

    /**
     * 创建组合委托的腿成交记录
     */
    private static TradeDetail createCombinationTrade(CombinationOrder order, String contractCode, Random random, boolean isLeg1) {
        TradeDetail trade = new TradeDetail();

        // 基本信息
        trade.setTradeNumber(String.format("CT%05d", tradeSequence++));
        trade.setOrderNumber(order.getOrderNumber());
        trade.setContractCode(contractCode);

        ContractConfig config = CONTRACT_CONFIGS.get(contractCode);
        trade.setExchangeCode(config.getExchangeCode());
        trade.setCommodityCode(config.getCommodityCode());
        trade.setTradeDate(order.getTradeDate());

        // 成交时间
        LocalTime tradeTime = order.getOrderTime().plusSeconds(1 + random.nextInt(2));
        trade.setTradeTime(tradeTime);

        // 成交价格
        BigDecimal basePrice = config.getBasePrice();
        BigDecimal priceVariation = basePrice.multiply(BigDecimal.valueOf(random.nextGaussian() * 0.002));
        trade.setTradePrice(basePrice.add(priceVariation));

        // 成交数量（与组合委托数量一致）
        trade.setTradeVolume(order.getOrderVolume());

        // 买卖方向（腿1和腿2方向可能相反）
        BuySellFlag tradeSide = order.getBuySellFlag();
        if (!isLeg1 && random.nextBoolean()) {
            tradeSide = (tradeSide == BuySellFlag.BUY) ? BuySellFlag.SELL : BuySellFlag.BUY;
        }
        trade.setBuySellFlag(tradeSide);

        // 其他信息
        trade.setOpenCloseType(order.getOpenCloseType());
        trade.setTradeType(TradeType.COMBINATION);
        trade.setMemberCode(order.getMemberCode());
        trade.setTradeCode(order.getTradeCode());
        trade.setSettlementMemberCode(order.getSettlementMemberCode());

        return trade;
    }

    /**
     * 验证数据一致性
     */
    public static void validateDataConsistency(MockDataSet dataSet) {
        LOG.info("开始验证数据一致性...");

        // 验证成交数据与委托数据的对应关系
        Set<String> orderNumbers = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getOrderNumber)
                .collect(java.util.stream.Collectors.toSet());

        Set<String> combinationOrderNumbers = dataSet.getCombinationOrders().stream()
                .map(CombinationOrder::getOrderNumber)
                .collect(java.util.stream.Collectors.toSet());

        long invalidTrades = dataSet.getTradeDetails().stream()
                .filter(trade -> !orderNumbers.contains(trade.getOrderNumber()) &&
                               !combinationOrderNumbers.contains(trade.getOrderNumber()))
                .count();

        if (invalidTrades > 0) {
            LOG.warn("发现 {} 条成交记录没有对应的委托订单", invalidTrades);
        }

        // 验证时间序列
        boolean timeConsistent = dataSet.getSingleLegOrders().stream()
                .allMatch(order -> order.getOrderTime().isAfter(BASE_TIME.minusSeconds(1)));

        if (!timeConsistent) {
            LOG.warn("发现时间序列不一致的数据");
        }

        // 验证价格合理性
        long unreasonablePrices = dataSet.getSingleLegOrders().stream()
                .filter(order -> order.getOrderPrice().compareTo(BigDecimal.ZERO) <= 0)
                .count();

        if (unreasonablePrices > 0) {
            LOG.warn("发现 {} 条价格不合理的订单", unreasonablePrices);
        }

        LOG.info("数据一致性验证完成 - 无效成交: {}, 时间一致: {}, 价格异常: {}",
                invalidTrades, timeConsistent, unreasonablePrices);
    }

    /**
     * 生成指定时间范围内的数据
     */
    public static MockDataSet generateDataForTimeRange(LocalTime startTime, LocalTime endTime, int orderCount) {
        // 临时修改基准时间
        LocalTime originalBaseTime = BASE_TIME;

        try {
            // 使用反射或其他方式修改BASE_TIME（简化处理）
            MockDataSet dataSet = new MockDataSet();

            // 重新生成数据
            List<SingleLegOrder> orders = generateSingleLegOrders(orderCount);

            // 调整时间到指定范围
            long timeRangeNanos = java.time.Duration.between(startTime, endTime).toNanos();
            for (int i = 0; i < orders.size(); i++) {
                SingleLegOrder order = orders.get(i);
                long offsetNanos = (timeRangeNanos * i) / orders.size();
                order.setOrderTime(startTime.plusNanos(offsetNanos));
            }

            dataSet.setSingleLegOrders(orders);
            dataSet.setTradeDetails(generateTradeDetails(orders));
            dataSet.setCombinationOrders(generateCombinationOrders(orderCount / 5));

            return dataSet;
        } finally {
            // 恢复原始基准时间
        }
    }

    /**
     * 生成特定合约的数据
     */
    public static MockDataSet generateDataForContract(String contractCode, int orderCount) {
        if (!CONTRACT_CONFIGS.containsKey(contractCode)) {
            throw new IllegalArgumentException("不支持的合约代码: " + contractCode);
        }

        MockDataSet dataSet = new MockDataSet();
        List<SingleLegOrder> orders = new ArrayList<>();
        Random random = new Random(47);

        ContractConfig config = CONTRACT_CONFIGS.get(contractCode);

        for (int i = 0; i < orderCount; i++) {
            SingleLegOrder order = new SingleLegOrder();

            // 基本信息
            order.setOrderNumber(String.format("SC%05d", orderSequence++));
            order.setContractCode(contractCode);
            order.setExchangeCode(config.getExchangeCode());
            order.setCommodityCode(config.getCommodityCode());
            order.setTradeDate(TRADE_DATE);

            // 时间分布
            LocalTime orderTime = BASE_TIME.plusNanos(i * 100_000_000L);
            order.setOrderTime(orderTime);

            // 价格和数量
            BigDecimal priceOffset = config.getPriceStep().multiply(BigDecimal.valueOf(random.nextInt(21) - 10));
            order.setOrderPrice(config.getBasePrice().add(priceOffset));
            order.setOrderVolume(BigDecimal.valueOf((random.nextInt(10) + 1) * 10));
            order.setRemainingVolume(order.getOrderVolume());

            // 其他属性
            order.setBuySellFlag(random.nextBoolean() ? BuySellFlag.BUY : BuySellFlag.SELL);
            order.setOpenCloseType(random.nextDouble() < 0.7 ? OpenCloseType.OPEN : OpenCloseType.CLOSE);
            order.setOrderType(OrderType.LIMIT);
            order.setOrderStatus(OrderStatus.NOT_FILLED_IN_QUEUE);

            String memberCode = MEMBER_CODES[i % MEMBER_CODES.length];
            order.setMemberCode(memberCode);
            order.setTradeCode(TRADE_CODES[i % TRADE_CODES.length]);
            order.setSettlementMemberCode(memberCode);

            orders.add(order);
        }

        dataSet.setSingleLegOrders(orders);
        dataSet.setTradeDetails(generateTradeDetails(orders));

        return dataSet;
    }

    /**
     * 重置序列号计数器
     */
    public static void resetSequenceCounters() {
        orderSequence = 1;
        tradeSequence = 1;
        combinationSequence = 1;
        LOG.info("序列号计数器已重置");
    }

    /**
     * 获取支持的合约列表
     */
    public static String[] getSupportedContracts() {
        return CONTRACTS.clone();
    }

    /**
     * 获取合约配置
     */
    public static ContractConfig getContractConfig(String contractCode) {
        return CONTRACT_CONFIGS.get(contractCode);
    }
}
