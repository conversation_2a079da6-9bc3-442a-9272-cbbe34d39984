package com.futures.system.mock;

import com.futures.system.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模拟数据验证器
 * 验证生成的模拟数据是否符合业务逻辑和数据一致性要求
 */
public class MockDataValidator {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataValidator.class);
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean isValid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private Map<String, Object> statistics = new HashMap<>();
        
        public void addError(String error) {
            errors.add(error);
            isValid = false;
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public void addStatistic(String key, Object value) {
            statistics.put(key, value);
        }
        
        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        public Map<String, Object> getStatistics() { return statistics; }
    }
    
    /**
     * 完整验证模拟数据集
     */
    public static ValidationResult validateCompleteDataSet(MockDataSet dataSet) {
        LOG.info("开始完整验证模拟数据集...");
        
        ValidationResult result = new ValidationResult();
        
        // 1. 基础数据验证
        validateBasicData(dataSet, result);
        
        // 2. 业务逻辑验证
        validateBusinessLogic(dataSet, result);
        
        // 3. 时间序列验证
        validateTimeSequence(dataSet, result);
        
        // 4. 数据关联验证
        validateDataRelationships(dataSet, result);
        
        // 5. 价格合理性验证
        validatePriceReasonableness(dataSet, result);
        
        // 6. 数量一致性验证
        validateVolumeConsistency(dataSet, result);
        
        // 7. 统计信息
        generateStatistics(dataSet, result);
        
        LOG.info("模拟数据验证完成 - 有效性: {}, 错误: {}, 警告: {}", 
                result.isValid(), result.getErrors().size(), result.getWarnings().size());
        
        return result;
    }
    
    /**
     * 验证基础数据
     */
    private static void validateBasicData(MockDataSet dataSet, ValidationResult result) {
        // 检查数据是否为空
        if (dataSet.getSingleLegOrders().isEmpty()) {
            result.addError("单腿委托数据为空");
        }
        
        if (dataSet.getTradeDetails().isEmpty()) {
            result.addWarning("成交明细数据为空");
        }
        
        // 检查必填字段
        for (SingleLegOrder order : dataSet.getSingleLegOrders()) {
            if (order.getOrderNumber() == null || order.getOrderNumber().trim().isEmpty()) {
                result.addError("发现报单编号为空的订单");
            }
            
            if (order.getContractCode() == null || order.getContractCode().trim().isEmpty()) {
                result.addError("发现合约代码为空的订单: " + order.getOrderNumber());
            }
            
            if (order.getOrderPrice() == null || order.getOrderPrice().compareTo(BigDecimal.ZERO) <= 0) {
                result.addError("发现价格无效的订单: " + order.getOrderNumber());
            }
            
            if (order.getOrderVolume() == null || order.getOrderVolume().compareTo(BigDecimal.ZERO) <= 0) {
                result.addError("发现数量无效的订单: " + order.getOrderNumber());
            }
        }
        
        // 检查成交数据必填字段
        for (TradeDetail trade : dataSet.getTradeDetails()) {
            if (trade.getTradeNumber() == null || trade.getTradeNumber().trim().isEmpty()) {
                result.addError("发现成交编号为空的成交记录");
            }
            
            if (trade.getOrderNumber() == null || trade.getOrderNumber().trim().isEmpty()) {
                result.addError("发现报单编号为空的成交记录: " + trade.getTradeNumber());
            }
        }
    }
    
    /**
     * 验证业务逻辑
     */
    private static void validateBusinessLogic(MockDataSet dataSet, ValidationResult result) {
        // 验证成交数据与委托数据的对应关系
        Set<String> orderNumbers = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getOrderNumber)
                .collect(Collectors.toSet());
        
        Set<String> combinationOrderNumbers = dataSet.getCombinationOrders().stream()
                .map(CombinationOrder::getOrderNumber)
                .collect(Collectors.toSet());
        
        for (TradeDetail trade : dataSet.getTradeDetails()) {
            if (!orderNumbers.contains(trade.getOrderNumber()) && 
                !combinationOrderNumbers.contains(trade.getOrderNumber())) {
                result.addError("成交记录 " + trade.getTradeNumber() + 
                              " 对应的委托订单 " + trade.getOrderNumber() + " 不存在");
            }
        }
        
        // 验证组合委托的腿合约
        Set<String> availableContracts = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getContractCode)
                .collect(Collectors.toSet());
        
        for (CombinationOrder combo : dataSet.getCombinationOrders()) {
            if (!availableContracts.contains(combo.getLeg1ContractCode())) {
                result.addWarning("组合委托 " + combo.getOrderNumber() + 
                                " 的腿1合约 " + combo.getLeg1ContractCode() + " 在单腿委托中不存在");
            }
            
            if (!availableContracts.contains(combo.getLeg2ContractCode())) {
                result.addWarning("组合委托 " + combo.getOrderNumber() + 
                                " 的腿2合约 " + combo.getLeg2ContractCode() + " 在单腿委托中不存在");
            }
        }
    }
    
    /**
     * 验证时间序列
     */
    private static void validateTimeSequence(MockDataSet dataSet, ValidationResult result) {
        // 验证委托时间序列
        List<LocalTime> orderTimes = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getOrderTime)
                .filter(Objects::nonNull)
                .sorted()
                .collect(Collectors.toList());
        
        if (orderTimes.size() > 1) {
            LocalTime firstTime = orderTimes.get(0);
            LocalTime lastTime = orderTimes.get(orderTimes.size() - 1);
            
            long durationSeconds = java.time.Duration.between(firstTime, lastTime).getSeconds();
            if (durationSeconds > 10) {
                result.addWarning("委托时间跨度过长: " + durationSeconds + " 秒");
            }
        }
        
        // 验证成交时间必须晚于委托时间
        Map<String, LocalTime> orderTimeMap = dataSet.getSingleLegOrders().stream()
                .collect(Collectors.toMap(SingleLegOrder::getOrderNumber, SingleLegOrder::getOrderTime));
        
        for (TradeDetail trade : dataSet.getTradeDetails()) {
            LocalTime orderTime = orderTimeMap.get(trade.getOrderNumber());
            if (orderTime != null && trade.getTradeTime() != null) {
                if (trade.getTradeTime().isBefore(orderTime)) {
                    result.addError("成交时间早于委托时间: 成交 " + trade.getTradeNumber() + 
                                  ", 委托 " + trade.getOrderNumber());
                }
            }
        }
    }
    
    /**
     * 验证数据关联
     */
    private static void validateDataRelationships(MockDataSet dataSet, ValidationResult result) {
        // 验证成交数量不超过委托数量
        Map<String, BigDecimal> orderVolumeMap = dataSet.getSingleLegOrders().stream()
                .collect(Collectors.toMap(SingleLegOrder::getOrderNumber, SingleLegOrder::getOrderVolume));
        
        Map<String, BigDecimal> tradeVolumeMap = dataSet.getTradeDetails().stream()
                .collect(Collectors.groupingBy(TradeDetail::getOrderNumber,
                        Collectors.reducing(BigDecimal.ZERO, TradeDetail::getTradeVolume, BigDecimal::add)));
        
        for (Map.Entry<String, BigDecimal> entry : tradeVolumeMap.entrySet()) {
            String orderNumber = entry.getKey();
            BigDecimal totalTradeVolume = entry.getValue();
            BigDecimal orderVolume = orderVolumeMap.get(orderNumber);
            
            if (orderVolume != null && totalTradeVolume.compareTo(orderVolume) > 0) {
                result.addError("订单 " + orderNumber + " 的成交数量 " + totalTradeVolume + 
                              " 超过委托数量 " + orderVolume);
            }
        }
    }
    
    /**
     * 验证价格合理性
     */
    private static void validatePriceReasonableness(MockDataSet dataSet, ValidationResult result) {
        // 按合约分组验证价格范围
        Map<String, List<BigDecimal>> contractPrices = dataSet.getSingleLegOrders().stream()
                .collect(Collectors.groupingBy(SingleLegOrder::getContractCode,
                        Collectors.mapping(SingleLegOrder::getOrderPrice, Collectors.toList())));
        
        for (Map.Entry<String, List<BigDecimal>> entry : contractPrices.entrySet()) {
            String contract = entry.getKey();
            List<BigDecimal> prices = entry.getValue();
            
            if (!prices.isEmpty()) {
                BigDecimal minPrice = prices.stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                BigDecimal maxPrice = prices.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                
                // 检查价格波动是否合理（不超过50%）
                if (minPrice.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal priceRange = maxPrice.subtract(minPrice);
                    BigDecimal volatility = priceRange.divide(minPrice, 4, BigDecimal.ROUND_HALF_UP);
                    
                    if (volatility.compareTo(BigDecimal.valueOf(0.5)) > 0) {
                        result.addWarning("合约 " + contract + " 价格波动过大: " + 
                                        volatility.multiply(BigDecimal.valueOf(100)) + "%");
                    }
                }
            }
        }
    }
    
    /**
     * 验证数量一致性
     */
    private static void validateVolumeConsistency(MockDataSet dataSet, ValidationResult result) {
        // 验证数量是否为合理的交易单位
        for (SingleLegOrder order : dataSet.getSingleLegOrders()) {
            BigDecimal volume = order.getOrderVolume();
            if (volume != null) {
                // 检查是否为10的倍数（期货交易通常以手为单位）
                if (volume.remainder(BigDecimal.TEN).compareTo(BigDecimal.ZERO) != 0) {
                    result.addWarning("订单 " + order.getOrderNumber() + " 数量不是10的倍数: " + volume);
                }
                
                // 检查数量是否过大
                if (volume.compareTo(BigDecimal.valueOf(1000)) > 0) {
                    result.addWarning("订单 " + order.getOrderNumber() + " 数量过大: " + volume);
                }
            }
        }
    }
    
    /**
     * 生成统计信息
     */
    private static void generateStatistics(MockDataSet dataSet, ValidationResult result) {
        result.addStatistic("total_single_leg_orders", dataSet.getSingleLegOrders().size());
        result.addStatistic("total_trade_details", dataSet.getTradeDetails().size());
        result.addStatistic("total_cancel_orders", dataSet.getCancelOrders().size());
        result.addStatistic("total_combination_orders", dataSet.getCombinationOrders().size());
        
        // 合约统计
        Set<String> contracts = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getContractCode)
                .collect(Collectors.toSet());
        result.addStatistic("unique_contracts", contracts.size());
        
        // 会员统计
        Set<String> members = dataSet.getSingleLegOrders().stream()
                .map(SingleLegOrder::getSettlementMemberCode)
                .collect(Collectors.toSet());
        result.addStatistic("unique_members", members.size());
        
        // 成交率统计
        Set<String> tradedOrders = dataSet.getTradeDetails().stream()
                .map(TradeDetail::getOrderNumber)
                .collect(Collectors.toSet());
        double tradeRate = (double) tradedOrders.size() / dataSet.getSingleLegOrders().size();
        result.addStatistic("trade_rate", String.format("%.2f%%", tradeRate * 100));
    }
    
    /**
     * 打印验证结果
     */
    public static void printValidationResult(ValidationResult result) {
        LOG.info("=== 模拟数据验证结果 ===");
        LOG.info("验证状态: {}", result.isValid() ? "通过" : "失败");
        
        if (!result.getErrors().isEmpty()) {
            LOG.error("发现 {} 个错误:", result.getErrors().size());
            result.getErrors().forEach(error -> LOG.error("  - {}", error));
        }
        
        if (!result.getWarnings().isEmpty()) {
            LOG.warn("发现 {} 个警告:", result.getWarnings().size());
            result.getWarnings().forEach(warning -> LOG.warn("  - {}", warning));
        }
        
        LOG.info("统计信息:");
        result.getStatistics().forEach((key, value) -> 
                LOG.info("  {}: {}", key, value));
        
        LOG.info("========================");
    }
}
