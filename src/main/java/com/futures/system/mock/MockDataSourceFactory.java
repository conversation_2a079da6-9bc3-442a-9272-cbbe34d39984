package com.futures.system.mock;

import com.futures.system.model.*;
import org.apache.flink.api.common.functions.RichSourceFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 模拟数据源工厂
 * 将模拟数据转换为Flink DataStream，支持时间顺序播放和实时模拟
 */
public class MockDataSourceFactory {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataSourceFactory.class);
    
    /**
     * 模拟数据源配置
     */
    public static class MockSourceConfig {
        private boolean realTimePlayback = true;  // 是否实时播放
        private long playbackSpeedMultiplier = 1; // 播放速度倍数
        private boolean loopPlayback = false;      // 是否循环播放
        private long delayBetweenRecords = 100;    // 记录间延迟(毫秒)
        
        public boolean isRealTimePlayback() { return realTimePlayback; }
        public void setRealTimePlayback(boolean realTimePlayback) { this.realTimePlayback = realTimePlayback; }
        
        public long getPlaybackSpeedMultiplier() { return playbackSpeedMultiplier; }
        public void setPlaybackSpeedMultiplier(long playbackSpeedMultiplier) { this.playbackSpeedMultiplier = playbackSpeedMultiplier; }
        
        public boolean isLoopPlayback() { return loopPlayback; }
        public void setLoopPlayback(boolean loopPlayback) { this.loopPlayback = loopPlayback; }
        
        public long getDelayBetweenRecords() { return delayBetweenRecords; }
        public void setDelayBetweenRecords(long delayBetweenRecords) { this.delayBetweenRecords = delayBetweenRecords; }
    }
    
    /**
     * 创建模拟数据源集合
     */
    public static MockDataSources createMockDataSources(StreamExecutionEnvironment env, MockDataSet dataSet) {
        return createMockDataSources(env, dataSet, new MockSourceConfig());
    }
    
    /**
     * 创建带配置的模拟数据源集合
     */
    public static MockDataSources createMockDataSources(StreamExecutionEnvironment env, MockDataSet dataSet, MockSourceConfig config) {
        LOG.info("创建模拟数据源，配置: 实时播放={}, 速度倍数={}, 循环播放={}", 
                config.isRealTimePlayback(), config.getPlaybackSpeedMultiplier(), config.isLoopPlayback());
        
        // 创建单腿委托数据源
        DataStream<SingleLegOrder> singleLegOrderStream = env
                .addSource(new SingleLegOrderMockSource(dataSet.getSingleLegOrders(), config))
                .name("MockSingleLegOrderSource");
        
        // 创建成交明细数据源
        DataStream<TradeDetail> tradeDetailStream = env
                .addSource(new TradeDetailMockSource(dataSet.getTradeDetails(), config))
                .name("MockTradeDetailSource");
        
        // 创建组合委托数据源
        DataStream<CombinationOrder> combinationOrderStream = env
                .addSource(new CombinationOrderMockSource(dataSet.getCombinationOrders(), config))
                .name("MockCombinationOrderSource");
        
        return new MockDataSources(singleLegOrderStream, tradeDetailStream, combinationOrderStream);
    }
    
    /**
     * 模拟数据源集合
     */
    public static class MockDataSources {
        private final DataStream<SingleLegOrder> singleLegOrderStream;
        private final DataStream<TradeDetail> tradeDetailStream;
        private final DataStream<CombinationOrder> combinationOrderStream;
        
        public MockDataSources(DataStream<SingleLegOrder> singleLegOrderStream,
                              DataStream<TradeDetail> tradeDetailStream,
                              DataStream<CombinationOrder> combinationOrderStream) {
            this.singleLegOrderStream = singleLegOrderStream;
            this.tradeDetailStream = tradeDetailStream;
            this.combinationOrderStream = combinationOrderStream;
        }
        
        public DataStream<SingleLegOrder> getSingleLegOrderStream() { return singleLegOrderStream; }
        public DataStream<TradeDetail> getTradeDetailStream() { return tradeDetailStream; }
        public DataStream<CombinationOrder> getCombinationOrderStream() { return combinationOrderStream; }
    }
    
    /**
     * 单腿委托模拟数据源
     */
    public static class SingleLegOrderMockSource extends RichSourceFunction<SingleLegOrder> {
        private final List<SingleLegOrder> orders;
        private final MockSourceConfig config;
        private volatile boolean running = true;
        
        public SingleLegOrderMockSource(List<SingleLegOrder> orders, MockSourceConfig config) {
            this.orders = new ArrayList<>(orders);
            this.config = config;
            
            // 按时间排序
            this.orders.sort(Comparator.comparing(SingleLegOrder::getOrderTime));
        }
        
        @Override
        public void run(SourceContext<SingleLegOrder> ctx) throws Exception {
            LOG.info("启动单腿委托模拟数据源，数据量: {}", orders.size());
            
            do {
                LocalTime lastTime = null;
                
                for (SingleLegOrder order : orders) {
                    if (!running) {
                        break;
                    }
                    
                    // 计算延迟时间
                    if (config.isRealTimePlayback() && lastTime != null) {
                        long delayMs = calculateDelay(lastTime, order.getOrderTime());
                        if (delayMs > 0) {
                            Thread.sleep(delayMs / config.getPlaybackSpeedMultiplier());
                        }
                    } else {
                        Thread.sleep(config.getDelayBetweenRecords());
                    }
                    
                    // 发送数据
                    ctx.collect(order);
                    lastTime = order.getOrderTime();
                    
                    LOG.debug("发送单腿委托: {}", order.getOrderNumber());
                }
                
            } while (running && config.isLoopPlayback());
            
            LOG.info("单腿委托模拟数据源结束");
        }
        
        @Override
        public void cancel() {
            running = false;
        }
        
        private long calculateDelay(LocalTime lastTime, LocalTime currentTime) {
            return java.time.Duration.between(lastTime, currentTime).toMillis();
        }
    }
    
    /**
     * 成交明细模拟数据源
     */
    public static class TradeDetailMockSource extends RichSourceFunction<TradeDetail> {
        private final List<TradeDetail> trades;
        private final MockSourceConfig config;
        private volatile boolean running = true;
        
        public TradeDetailMockSource(List<TradeDetail> trades, MockSourceConfig config) {
            this.trades = new ArrayList<>(trades);
            this.config = config;
            
            // 按时间排序
            this.trades.sort(Comparator.comparing(TradeDetail::getTradeTime));
        }
        
        @Override
        public void run(SourceContext<TradeDetail> ctx) throws Exception {
            LOG.info("启动成交明细模拟数据源，数据量: {}", trades.size());
            
            do {
                LocalTime lastTime = null;
                
                for (TradeDetail trade : trades) {
                    if (!running) {
                        break;
                    }
                    
                    // 计算延迟时间
                    if (config.isRealTimePlayback() && lastTime != null) {
                        long delayMs = calculateDelay(lastTime, trade.getTradeTime());
                        if (delayMs > 0) {
                            Thread.sleep(delayMs / config.getPlaybackSpeedMultiplier());
                        }
                    } else {
                        Thread.sleep(config.getDelayBetweenRecords());
                    }
                    
                    // 发送数据
                    ctx.collect(trade);
                    lastTime = trade.getTradeTime();
                    
                    LOG.debug("发送成交明细: {}", trade.getTradeNumber());
                }
                
            } while (running && config.isLoopPlayback());
            
            LOG.info("成交明细模拟数据源结束");
        }
        
        @Override
        public void cancel() {
            running = false;
        }
        
        private long calculateDelay(LocalTime lastTime, LocalTime currentTime) {
            return java.time.Duration.between(lastTime, currentTime).toMillis();
        }
    }
    
    /**
     * 组合委托模拟数据源
     */
    public static class CombinationOrderMockSource extends RichSourceFunction<CombinationOrder> {
        private final List<CombinationOrder> orders;
        private final MockSourceConfig config;
        private volatile boolean running = true;
        
        public CombinationOrderMockSource(List<CombinationOrder> orders, MockSourceConfig config) {
            this.orders = new ArrayList<>(orders);
            this.config = config;
            
            // 按时间排序
            this.orders.sort(Comparator.comparing(CombinationOrder::getOrderTime));
        }
        
        @Override
        public void run(SourceContext<CombinationOrder> ctx) throws Exception {
            LOG.info("启动组合委托模拟数据源，数据量: {}", orders.size());
            
            do {
                LocalTime lastTime = null;
                
                for (CombinationOrder order : orders) {
                    if (!running) {
                        break;
                    }
                    
                    // 计算延迟时间
                    if (config.isRealTimePlayback() && lastTime != null) {
                        long delayMs = calculateDelay(lastTime, order.getOrderTime());
                        if (delayMs > 0) {
                            Thread.sleep(delayMs / config.getPlaybackSpeedMultiplier());
                        }
                    } else {
                        Thread.sleep(config.getDelayBetweenRecords());
                    }
                    
                    // 发送数据
                    ctx.collect(order);
                    lastTime = order.getOrderTime();
                    
                    LOG.debug("发送组合委托: {}", order.getOrderNumber());
                }
                
            } while (running && config.isLoopPlayback());
            
            LOG.info("组合委托模拟数据源结束");
        }
        
        @Override
        public void cancel() {
            running = false;
        }
        
        private long calculateDelay(LocalTime lastTime, LocalTime currentTime) {
            return java.time.Duration.between(lastTime, currentTime).toMillis();
        }
    }
    
    /**
     * 创建快速测试数据源（无延迟）
     */
    public static MockDataSources createFastTestSources(StreamExecutionEnvironment env, MockDataSet dataSet) {
        MockSourceConfig config = new MockSourceConfig();
        config.setRealTimePlayback(false);
        config.setDelayBetweenRecords(10); // 10ms延迟
        config.setLoopPlayback(false);
        
        return createMockDataSources(env, dataSet, config);
    }
    
    /**
     * 创建实时模拟数据源
     */
    public static MockDataSources createRealTimeSimulationSources(StreamExecutionEnvironment env, MockDataSet dataSet) {
        MockSourceConfig config = new MockSourceConfig();
        config.setRealTimePlayback(true);
        config.setPlaybackSpeedMultiplier(1);
        config.setLoopPlayback(true);
        
        return createMockDataSources(env, dataSet, config);
    }
}
