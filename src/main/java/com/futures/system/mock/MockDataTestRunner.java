package com.futures.system.mock;

import com.futures.system.example.MockDataExample;
import com.futures.system.example.OutputSolutionExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 模拟数据测试运行器
 * 提供完整的模拟数据测试套件，用于验证系统功能
 */
public class MockDataTestRunner {
    
    private static final Logger LOG = LoggerFactory.getLogger(MockDataTestRunner.class);
    
    public static void main(String[] args) {
        try {
            LOG.info("=== 期货订单簿重建和盈亏计算系统 - 模拟数据测试套件 ===");
            
            // 1. 数据生成和验证测试
            runDataGenerationTest();
            
            // 2. 基本功能测试
            runBasicFunctionalityTest();
            
            // 3. 完整系统集成测试
            runSystemIntegrationTest();
            
            // 4. 输出方案测试
            runOutputSolutionTest();
            
            LOG.info("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            LOG.error("测试执行过程中发生错误", e);
            System.exit(1);
        }
    }
    
    /**
     * 数据生成和验证测试
     */
    private static void runDataGenerationTest() {
        LOG.info("--- 开始数据生成和验证测试 ---");
        
        try {
            // 1. 生成模拟数据
            LOG.info("生成模拟数据...");
            MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
            
            // 2. 打印统计信息
            dataSet.printStatistics();
            
            // 3. 验证数据
            LOG.info("验证数据完整性...");
            MockDataValidator.ValidationResult result = MockDataValidator.validateCompleteDataSet(dataSet);
            MockDataValidator.printValidationResult(result);
            
            if (!result.isValid()) {
                LOG.error("数据验证失败，停止后续测试");
                return;
            }
            
            // 4. 保存数据到文件
            String dataFilePath = "target/test_data_complete.json";
            dataSet.saveToJsonFile(dataFilePath);
            LOG.info("测试数据已保存到: {}", dataFilePath);
            
            // 5. 测试数据加载
            LOG.info("测试数据加载...");
            MockDataSet loadedDataSet = MockDataSet.loadFromJsonFile(dataFilePath);
            LOG.info("数据加载成功，订单数量: {}", loadedDataSet.getSingleLegOrders().size());
            
            LOG.info("数据生成和验证测试完成");
            
        } catch (Exception e) {
            LOG.error("数据生成和验证测试失败", e);
        }
    }
    
    /**
     * 基本功能测试
     */
    private static void runBasicFunctionalityTest() {
        LOG.info("--- 开始基本功能测试 ---");
        
        try {
            // 1. 测试特定合约数据生成
            LOG.info("测试特定合约数据生成...");
            String[] contracts = MockDataGenerator.getSupportedContracts();
            for (String contract : contracts) {
                MockDataSet contractData = MockDataGenerator.generateDataForContract(contract, 20);
                LOG.info("合约 {} 数据生成完成，订单数量: {}", contract, contractData.getSingleLegOrders().size());
            }
            
            // 2. 测试时间范围数据生成
            LOG.info("测试时间范围数据生成...");
            java.time.LocalTime startTime = java.time.LocalTime.of(9, 0, 0);
            java.time.LocalTime endTime = java.time.LocalTime.of(9, 0, 5);
            MockDataSet timeRangeData = MockDataGenerator.generateDataForTimeRange(startTime, endTime, 30);
            LOG.info("时间范围数据生成完成，订单数量: {}", timeRangeData.getSingleLegOrders().size());
            
            // 3. 测试数据源工厂
            LOG.info("测试数据源工厂...");
            MockDataSet testDataSet = MockDataGenerator.generateCompleteDataSet();
            
            // 测试不同配置的数据源
            MockDataSourceFactory.MockSourceConfig fastConfig = new MockDataSourceFactory.MockSourceConfig();
            fastConfig.setRealTimePlayback(false);
            fastConfig.setDelayBetweenRecords(1);
            
            MockDataSourceFactory.MockSourceConfig realTimeConfig = new MockDataSourceFactory.MockSourceConfig();
            realTimeConfig.setRealTimePlayback(true);
            realTimeConfig.setPlaybackSpeedMultiplier(10); // 10倍速播放
            
            LOG.info("数据源配置测试完成");
            
            LOG.info("基本功能测试完成");
            
        } catch (Exception e) {
            LOG.error("基本功能测试失败", e);
        }
    }
    
    /**
     * 系统集成测试
     */
    private static void runSystemIntegrationTest() {
        LOG.info("--- 开始系统集成测试 ---");
        
        try {
            // 运行模拟数据示例
            LOG.info("运行基本模拟数据测试...");
            MockDataExample.runBasicMockDataTest();
            
            LOG.info("系统集成测试完成");
            
        } catch (Exception e) {
            LOG.error("系统集成测试失败", e);
        }
    }
    
    /**
     * 输出方案测试
     */
    private static void runOutputSolutionTest() {
        LOG.info("--- 开始输出方案测试 ---");
        
        try {
            // 运行调试输出方案测试
            LOG.info("运行调试输出方案测试...");
            OutputSolutionExample.runDebugOutputExample();
            
            LOG.info("输出方案测试完成");
            
        } catch (Exception e) {
            LOG.error("输出方案测试失败", e);
        }
    }
    
    /**
     * 运行性能测试
     */
    public static void runPerformanceTest() {
        LOG.info("--- 开始性能测试 ---");
        
        try {
            // 生成大量数据
            LOG.info("生成大量测试数据...");
            long startTime = System.currentTimeMillis();
            
            MockDataSet largeDataSet = new MockDataSet();
            largeDataSet.setSingleLegOrders(MockDataGenerator.generateSingleLegOrders(1000));
            largeDataSet.setTradeDetails(MockDataGenerator.generateTradeDetails(largeDataSet.getSingleLegOrders()));
            largeDataSet.setCombinationOrders(MockDataGenerator.generateCombinationOrders(200));
            
            long generationTime = System.currentTimeMillis() - startTime;
            LOG.info("数据生成完成，耗时: {} 毫秒", generationTime);
            
            // 验证大量数据
            LOG.info("验证大量数据...");
            startTime = System.currentTimeMillis();
            MockDataValidator.ValidationResult result = MockDataValidator.validateCompleteDataSet(largeDataSet);
            long validationTime = System.currentTimeMillis() - startTime;
            LOG.info("数据验证完成，耗时: {} 毫秒，结果: {}", validationTime, result.isValid());
            
            // 保存大量数据
            LOG.info("保存大量数据...");
            startTime = System.currentTimeMillis();
            largeDataSet.saveToJsonFile("target/large_test_data.json");
            long saveTime = System.currentTimeMillis() - startTime;
            LOG.info("数据保存完成，耗时: {} 毫秒", saveTime);
            
            LOG.info("性能测试完成");
            
        } catch (Exception e) {
            LOG.error("性能测试失败", e);
        }
    }
    
    /**
     * 运行压力测试
     */
    public static void runStressTest() {
        LOG.info("--- 开始压力测试 ---");
        
        try {
            // 运行多轮测试
            int rounds = 5;
            for (int i = 1; i <= rounds; i++) {
                LOG.info("压力测试第 {} 轮...", i);
                
                // 重置计数器
                MockDataGenerator.resetSequenceCounters();
                
                // 生成数据
                MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();
                
                // 验证数据
                MockDataValidator.ValidationResult result = MockDataValidator.validateCompleteDataSet(dataSet);
                
                if (!result.isValid()) {
                    LOG.error("第 {} 轮压力测试失败", i);
                    break;
                }
                
                LOG.info("第 {} 轮压力测试完成", i);
            }
            
            LOG.info("压力测试完成");
            
        } catch (Exception e) {
            LOG.error("压力测试失败", e);
        }
    }
    
    /**
     * 运行所有测试
     */
    public static void runAllTests() {
        LOG.info("=== 运行完整测试套件 ===");
        
        runDataGenerationTest();
        runBasicFunctionalityTest();
        runSystemIntegrationTest();
        runOutputSolutionTest();
        runPerformanceTest();
        runStressTest();
        
        LOG.info("=== 完整测试套件执行完成 ===");
    }
    
    /**
     * 运行快速验证测试
     */
    public static void runQuickValidationTest() {
        LOG.info("=== 运行快速验证测试 ===");
        
        try {
            // 生成小量数据
            MockDataSet dataSet = new MockDataSet();
            dataSet.setSingleLegOrders(MockDataGenerator.generateSingleLegOrders(20));
            dataSet.setTradeDetails(MockDataGenerator.generateTradeDetails(dataSet.getSingleLegOrders()));
            dataSet.setCombinationOrders(MockDataGenerator.generateCombinationOrders(5));
            
            // 验证数据
            MockDataValidator.ValidationResult result = MockDataValidator.validateCompleteDataSet(dataSet);
            MockDataValidator.printValidationResult(result);
            
            if (result.isValid()) {
                LOG.info("快速验证测试通过");
            } else {
                LOG.error("快速验证测试失败");
            }
            
        } catch (Exception e) {
            LOG.error("快速验证测试异常", e);
        }
    }
}
