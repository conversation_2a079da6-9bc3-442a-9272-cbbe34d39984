-- 期货订单簿重建和盈亏计算系统 ClickHouse 表结构
-- 兼容现有 ch.sql 文件的表结构设计

-- 1. 订单簿快照表
CREATE TABLE IF NOT EXISTS futures_orderbook_snapshots
(
    snapshot_time DateTime64(3) COMMENT '快照时间',
    snapshot_sequence UInt64 COMMENT '快照序列号',
    contract_code String COMMENT '合约代码',
    trade_date Date COMMENT '交易日期',
    exchange_code String COMMENT '交易所代码',
    commodity_code String COMMENT '商品代码',
    
    -- 最优价格信息
    best_bid_price Nullable(Decimal64(6)) COMMENT '最优买价',
    best_ask_price Nullable(Decimal64(6)) COMMENT '最优卖价',
    best_bid_volume Nullable(Decimal64(0)) COMMENT '最优买量',
    best_ask_volume Nullable(Decimal64(0)) COMMENT '最优卖量',
    
    -- 档位统计
    bid_levels_count UInt32 COMMENT '买方档位数',
    ask_levels_count UInt32 COMMENT '卖方档位数',
    base_bid_levels_count UInt32 COMMENT '基础层买方档位数',
    base_ask_levels_count UInt32 COMMENT '基础层卖方档位数',
    
    -- 总量统计
    total_bid_volume Decimal64(0) COMMENT '买方总量',
    total_ask_volume Decimal64(0) COMMENT '卖方总量',
    total_bid_orders UInt32 COMMENT '买方总订单数',
    total_ask_orders UInt32 COMMENT '卖方总订单数',
    
    -- 虚拟层标识
    has_virtual_layer UInt8 COMMENT '是否包含虚拟层',
    
    -- 元数据
    created_at DateTime DEFAULT now() COMMENT '创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (contract_code, snapshot_time, snapshot_sequence)
TTL trade_date + INTERVAL 90 DAY
SETTINGS index_granularity = 8192
COMMENT '订单簿快照表';

-- 2. 盈亏计算表
CREATE TABLE IF NOT EXISTS futures_pnl_calculations
(
    calculation_time DateTime64(3) COMMENT '计算时间',
    settlement_member_code String COMMENT '结算会员编码',
    contract_code String COMMENT '合约代码',
    trade_date Date COMMENT '交易日期',
    
    -- 持仓信息
    current_position Decimal64(0) COMMENT '当前净持仓',
    open_volume Decimal64(0) COMMENT '开仓量',
    close_volume Decimal64(0) COMMENT '平仓量',
    
    -- 价格信息
    average_open_price Nullable(Decimal64(6)) COMMENT '平均开仓价格',
    latest_trade_price Nullable(Decimal64(6)) COMMENT '最新成交价格',
    
    -- 盈亏信息
    realized_pnl Decimal64(2) COMMENT '已实现盈亏',
    unrealized_pnl Decimal64(2) COMMENT '浮动盈亏',
    total_pnl Decimal64(2) COMMENT '总盈亏',
    
    -- 金额信息
    open_amount Decimal64(2) COMMENT '开仓金额',
    close_amount Decimal64(2) COMMENT '平仓金额',
    
    -- 持仓方向
    net_position_side Decimal64(0) COMMENT '净持仓方向',
    
    -- 元数据
    update_sequence UInt64 COMMENT '更新序列号',
    created_at DateTime DEFAULT now() COMMENT '创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (settlement_member_code, contract_code, calculation_time)
TTL trade_date + INTERVAL 365 DAY
SETTINGS index_granularity = 8192
COMMENT '盈亏计算表';

-- 3. 盈亏汇总表
CREATE TABLE IF NOT EXISTS futures_pnl_summaries
(
    summary_time DateTime64(3) COMMENT '汇总时间',
    summary_sequence UInt64 COMMENT '汇总序列号',
    settlement_member_code String COMMENT '结算会员编码',
    
    -- 汇总盈亏
    total_realized_pnl Decimal64(2) COMMENT '总已实现盈亏',
    total_unrealized_pnl Decimal64(2) COMMENT '总浮动盈亏',
    total_pnl Decimal64(2) COMMENT '总盈亏',
    
    -- 汇总金额
    total_open_amount Decimal64(2) COMMENT '总开仓金额',
    total_close_amount Decimal64(2) COMMENT '总平仓金额',
    
    -- 收益率
    total_return_rate Nullable(Decimal64(6)) COMMENT '总收益率',
    realized_return_rate Nullable(Decimal64(6)) COMMENT '已实现收益率',
    
    -- 合约统计
    total_contracts UInt32 COMMENT '合约总数',
    active_contracts UInt32 COMMENT '有持仓合约数',
    
    -- 风险指标
    max_contract_pnl Decimal64(2) COMMENT '最大单合约盈亏',
    min_contract_pnl Decimal64(2) COMMENT '最小单合约盈亏',
    
    -- 元数据
    created_at DateTime DEFAULT now() COMMENT '创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDate(summary_time))
ORDER BY (settlement_member_code, summary_time, summary_sequence)
TTL toDate(summary_time) + INTERVAL 365 DAY
SETTINGS index_granularity = 8192
COMMENT '盈亏汇总表';

-- 4. 订单簿价格档位明细表（可选，用于详细分析）
CREATE TABLE IF NOT EXISTS futures_orderbook_levels
(
    snapshot_time DateTime64(3) COMMENT '快照时间',
    contract_code String COMMENT '合约代码',
    side Enum8('BUY' = 1, 'SELL' = 2) COMMENT '买卖方向',
    level_index UInt8 COMMENT '档位序号(1-5)',
    price Decimal64(6) COMMENT '价格',
    volume Decimal64(0) COMMENT '数量',
    order_count UInt32 COMMENT '订单数',
    
    -- 虚拟层信息
    base_volume Decimal64(0) COMMENT '基础层数量',
    virtual_volume Decimal64(0) COMMENT '虚拟层数量',
    base_order_count UInt32 COMMENT '基础层订单数',
    virtual_order_count UInt32 COMMENT '虚拟层订单数',
    
    created_at DateTime DEFAULT now() COMMENT '创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDate(snapshot_time))
ORDER BY (contract_code, snapshot_time, side, level_index)
TTL toDate(snapshot_time) + INTERVAL 30 DAY
SETTINGS index_granularity = 8192
COMMENT '订单簿价格档位明细表';

-- 创建索引优化查询性能
-- 订单簿快照表索引
ALTER TABLE futures_orderbook_snapshots ADD INDEX idx_contract_time (contract_code, snapshot_time) TYPE minmax GRANULARITY 1;
ALTER TABLE futures_orderbook_snapshots ADD INDEX idx_trade_date (trade_date) TYPE minmax GRANULARITY 1;

-- 盈亏计算表索引
ALTER TABLE futures_pnl_calculations ADD INDEX idx_member_contract (settlement_member_code, contract_code) TYPE bloom_filter GRANULARITY 1;
ALTER TABLE futures_pnl_calculations ADD INDEX idx_calculation_time (calculation_time) TYPE minmax GRANULARITY 1;
ALTER TABLE futures_pnl_calculations ADD INDEX idx_total_pnl (total_pnl) TYPE minmax GRANULARITY 1;

-- 盈亏汇总表索引
ALTER TABLE futures_pnl_summaries ADD INDEX idx_member_time (settlement_member_code, summary_time) TYPE minmax GRANULARITY 1;
ALTER TABLE futures_pnl_summaries ADD INDEX idx_total_pnl (total_pnl) TYPE minmax GRANULARITY 1;

-- 创建物化视图用于实时监控
-- 1. 合约最新快照视图
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_latest_orderbook_snapshots
ENGINE = ReplacingMergeTree(snapshot_sequence)
ORDER BY contract_code
AS SELECT
    contract_code,
    argMax(snapshot_time, snapshot_sequence) as latest_snapshot_time,
    argMax(best_bid_price, snapshot_sequence) as latest_best_bid_price,
    argMax(best_ask_price, snapshot_sequence) as latest_best_ask_price,
    argMax(total_bid_volume, snapshot_sequence) as latest_total_bid_volume,
    argMax(total_ask_volume, snapshot_sequence) as latest_total_ask_volume
FROM futures_orderbook_snapshots
GROUP BY contract_code;

-- 2. 结算会员最新盈亏视图
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_latest_member_pnl
ENGINE = ReplacingMergeTree(summary_sequence)
ORDER BY settlement_member_code
AS SELECT
    settlement_member_code,
    argMax(summary_time, summary_sequence) as latest_summary_time,
    argMax(total_pnl, summary_sequence) as latest_total_pnl,
    argMax(total_return_rate, summary_sequence) as latest_return_rate,
    argMax(active_contracts, summary_sequence) as latest_active_contracts
FROM futures_pnl_summaries
GROUP BY settlement_member_code;

-- 创建用于监控的聚合表
-- 1. 每分钟订单簿统计
CREATE TABLE IF NOT EXISTS futures_orderbook_stats_1min
(
    minute_time DateTime COMMENT '分钟时间',
    contract_code String COMMENT '合约代码',
    avg_bid_price Nullable(Decimal64(6)) COMMENT '平均买价',
    avg_ask_price Nullable(Decimal64(6)) COMMENT '平均卖价',
    max_bid_volume Decimal64(0) COMMENT '最大买量',
    max_ask_volume Decimal64(0) COMMENT '最大卖量',
    snapshot_count UInt32 COMMENT '快照数量'
)
ENGINE = SummingMergeTree()
ORDER BY (contract_code, minute_time)
TTL minute_time + INTERVAL 7 DAY;

-- 2. 每小时盈亏统计
CREATE TABLE IF NOT EXISTS futures_pnl_stats_1hour
(
    hour_time DateTime COMMENT '小时时间',
    settlement_member_code String COMMENT '结算会员编码',
    avg_total_pnl Decimal64(2) COMMENT '平均总盈亏',
    max_total_pnl Decimal64(2) COMMENT '最大总盈亏',
    min_total_pnl Decimal64(2) COMMENT '最小总盈亏',
    calculation_count UInt32 COMMENT '计算次数'
)
ENGINE = SummingMergeTree()
ORDER BY (settlement_member_code, hour_time)
TTL hour_time + INTERVAL 30 DAY;
