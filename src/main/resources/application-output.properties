# æè´§è®¢åç°¿éå»ºåçäºè®¡ç®ç³»ç» - è¾åºéç½®

# ===========================================
# è¾åºæ¹æ¡éç½®
# ===========================================
# è¾åºæ¹æ¡ç±»å: DEBUG_CONSOLE, PRODUCTION_CLICKHOUSE, HYBRID
output.solution.type=DEBUG_CONSOLE

# ===========================================
# è°è¯è¾åºéç½®
# ===========================================
# æ¯å¦å¯ç¨è°è¯è¾åº
debug.output.enabled=true

# è®¢åç°¿å¿«ç§è¾åºéç½®
debug.orderbook.enabled=true
debug.orderbook.interval.ms=1000

# çäºè®¡ç®è¾åºéç½®
debug.pnl.calculation.enabled=true
debug.pnl.calculation.interval.ms=2000

# çäºæ±æ»è¾åºéç½®
debug.pnl.summary.enabled=true
debug.pnl.summary.interval.ms=5000

# æ¥å¿çº§å«
debug.log.level=INFO

# ===========================================
# ClickHouseçäº§è¾åºéç½®
# ===========================================
# æ¯å¦å¯ç¨ClickHouseè¾åº
clickhouse.output.enabled=false

# ClickHouseè¿æ¥éç½®
clickhouse.jdbc.url=****************************************
clickhouse.username=default
clickhouse.password=
clickhouse.database=default

# ClickHouseæ¹éåå¥éç½®
clickhouse.batch.size=1000
clickhouse.flush.interval.ms=5000
clickhouse.max.retries=3
clickhouse.retry.delay.ms=1000
clickhouse.connection.pool.size=5

# ClickHouseè¿æ¥è¶æ¶éç½®
clickhouse.socket.timeout.ms=30000
clickhouse.connection.timeout.ms=10000

# ===========================================
# ç¯å¢ç¹å®éç½®
# ===========================================

# å¼åç¯å¢éç½®
[development]
output.solution.type=DEBUG_CONSOLE
debug.output.enabled=true
debug.orderbook.interval.ms=500
debug.pnl.calculation.interval.ms=1000
debug.pnl.summary.interval.ms=2000
debug.log.level=DEBUG

# æµè¯ç¯å¢éç½®
[testing]
output.solution.type=HYBRID
debug.output.enabled=true
debug.orderbook.enabled=false
debug.pnl.calculation.interval.ms=3000
debug.pnl.summary.interval.ms=10000
clickhouse.output.enabled=true
clickhouse.batch.size=500
clickhouse.flush.interval.ms=10000

# çäº§ç¯å¢éç½®
[production]
output.solution.type=PRODUCTION_CLICKHOUSE
debug.output.enabled=false
clickhouse.output.enabled=true
clickhouse.batch.size=2000
clickhouse.flush.interval.ms=3000
clickhouse.max.retries=5
clickhouse.retry.delay.ms=2000
clickhouse.connection.pool.size=10

# é«æ§è½ç¯å¢éç½®
[high-performance]
output.solution.type=PRODUCTION_CLICKHOUSE
clickhouse.batch.size=5000
clickhouse.flush.interval.ms=1000
clickhouse.connection.pool.size=15

# ===========================================
# çæ§ååè­¦éç½®
# ===========================================
# è¾åºçæ§éç½®
output.monitoring.enabled=true
output.monitoring.interval.ms=30000

# æ§è½ææ éç½®
output.metrics.enabled=true
output.metrics.batch.success.threshold=0.95
output.metrics.latency.threshold.ms=1000

# åè­¦éç½®
output.alert.enabled=false
output.alert.email=<EMAIL>
output.alert.batch.failure.threshold=5
output.alert.connection.failure.threshold=3
