Create table if not exists cfmmc_trade.dwd_trd_trd_dtl_rt
(
    trd_dt              Date COMMENT '交易日期',
    unfy_opnacct_cde            String COMMENT '统一开户账号',
    exchg_cde                   String COMMENT '交易所编码',
    memb_cde                    String COMMENT '会员编码',
    trd_cde                     String COMMENT '交易编码',
    fcomp_unfy_cde              String COMMENT '期货公司统一编码',
    futrs_opt_tag               String COMMENT '期货期权标志',
    comm_cde                    String COMMENT '品种代码',
    contract_cde                String COMMENT '合约代码',
    b_s_tag                     String COMMENT '买卖标志',
    uniq_trd_nbr                String COMMENT '唯一交易编号',
    trd_nbr                     String COMMENT '成交编号',
    settle_memb_memb_cde        String COMMENT '结算会员会员编码',
    settle_memb_unfy_cde        String COMMENT '结算会员统一编码',
    intv_pasv_tag               String COMMENT '主动被动标志',
    ord_vol                     Decimal(32, 0) COMMENT '委托数量',
    ord_tm                      String COMMENT '报单时间',
    ord_prc                     Decimal(24, 6) COMMENT '委托价格',
    ord_type                    String COMMENT '委托类型',
    memb_fund_acctnbr          String COMMENT '会员资金账号',
    contpty_uniq_trd_nbr        String COMMENT '对手唯一交易编号',
    contpty_ord_nbr            String COMMENT '对手报单编号',
    contpty_unfy_opnacct_cde    String COMMENT '对手统一开户账号',
    contpty_memb_cde            String COMMENT '对手会员编码',
    contpty_fcomp_unfy_cde      String COMMENT '对手期货公司统一编码',
    contpty_ocpos_type          String COMMENT '对手开平仓类型',
    contpty_b_s_tag             String COMMENT '对手买卖标志',
    contpty_ord_vol             Decimal(32, 0) COMMENT '对手委托数量',
    contpty_ord_tm              String COMMENT '对手报单时间',
    contpty_ord_prc             Decimal(24, 6) COMMENT '对手委托价格',
    contpty_ord_type            String COMMENT '对手报单类型',
    trd_prc                     Decimal(24, 6) COMMENT '成交价格',
    ocpos_type                  String COMMENT '开平仓类型',
    highfrequ_ord_tag           String COMMENT '高频委托标志',
    trd_type                    String COMMENT '成交类型',
    ord_prc_cndt                String COMMENT '报单价格条件',
    specu_hedg_tag              String COMMENT '投机套保标志',
    c_p_tag                     String COMMENT '看涨看跌标志',
    exec_prc                    Decimal(24, 6) COMMENT '执行价格',
    trd_role                    String COMMENT '交易角色',
    local_ord_nbr               String COMMENT '本地报单编号',
    ord_nbr                     String COMMENT '报单编号',
    ocr_dt                      Date COMMENT '发生日期',
    rcod_nbr                    String COMMENT '记录号',
    seat_cde                    String COMMENT '席位号',
    trd_vol                     Decimal(32, 0) COMMENT '成交数量',
    trd_tm_millisec             Decimal(32, 0) '成交时间毫秒',
    trd_tm_microsec             Decimal(32, 0) '成交时间微秒',
    trd_tm                      String COMMENT '成交时间',
    _seq                        Int64 COMMENT '全局序列号'，
    _eno                        Int64 COMMENT '事件号'
    _dt                         DateTime64(3) Default now(3) COMMENT '数据时间'
)
    ENGINE = MergeTree()
    PARTITION BY toYYYYMM(trd_dt)
    ORDER BY (trd_cde, contract_cde, trd_tm)
    SETTINGS non_replicated_deduplication_window = 1000
    COMMENT 'DWD_成交明细_实时';

Create table if not exists cfmmc_trade.dwd_trd_snglleg_ord_dtl_rt
(
    trd_dt                      Date COMMENT '交易日期',
    unfy_opnacct_cde            String COMMENT '统一开户账号',
    exchg_cde                   String COMMENT '交易所编码',
    memb_cde                    String COMMENT '会员编码',
    trd_cde                     String COMMENT '交易编码',
    fcomp_unfy_cde              String COMMENT '期货公司统一编码',
    futrs_opt_tag               String COMMENT '期货期权标志',
    comm_cde                    String COMMENT '品种代码',
    contract_cde                String COMMENT '合约代码',
    ord_nbr                     String COMMENT '报单编号',
    settle_memb_memb_cde        String COMMENT '结算会员会员编码',
    highfrequ_ord_tag           String COMMENT '高频委托标志',
    ord_type                    String COMMENT '报单类型',
    ord_sts                     String COMMENT '报单状态',
    ord_src                     String COMMENT '报单来源',
    trd_vol_type                String COMMENT '成交量类型',
    ord_prc_cndt                String COMMENT '报单价格条件',
    vldprd_type                 String COMMENT '有效期类型',
    final_alt_seat_nbr          String COMMENT '最终修改席位号',
    seat_nbr                    String COMMENT '席位号',
    rmn_vol                     Decimal(32, 0) COMMENT '剩余量',
    trd_vol                     Decimal(32, 0) COMMENT '成交量',
    min_trd_vol                 Decimal(32, 0) COMMENT '最小成交量',
    ord_vol                     Decimal(32, 0) COMMENT '委托量',
    trig_prc                    Decimal(24, 6) COMMENT '触发价格',
    ord_prc                     Decimal(24, 6) COMMENT '委托价格',
    rcnt_renew_tm_microsec      Decimal(32, 0) COMMENT '最近更新时间微秒',
    rcnt_renew_tm_millisec      Decimal(32, 0) COMMENT '最近更新时间毫秒',
    final_alt_tm                String COMMENT '最终修改时间',
    ord_tm_microsec             Decimal(32, 0) COMMENT '报单时间微秒',
    ord_tm_millisec             Decimal(32, 0) COMMENT '报单时间毫秒',
    ord_tm                      String COMMENT '报单时间',
    repel_tm                    String COMMENT '撤销时间',
    susp_tm                     String COMMENT '挂起时间',
    actv_tm                     String COMMENT '激活时间',
    ord_tm                      String COMMENT '报单时间',
    forcclos_resn               String COMMENT '强平原因',
    trig_cndt                   String COMMENT '触发条件',
    specu_hedg_tag              String COMMENT '投机套保标志',
    ocpos_type                  String COMMENT '开平仓类型',
    b_s_tag                     String COMMENT '买卖标志',
    c_p_tag                     String COMMENT '看涨看跌标志',
    exec_prc                    Decimal(24, 6) COMMENT '执行价格',
    local_ord_nbr               String COMMENT '本地报单编号',
    ocr_dt                      Date COMMENT '发生日期',
    repel_tm_microsec           Decimal(32, 0) COMMENT '撤销时间微秒',
    repel_tm_millisec           Decimal(32, 0) COMMENT '撤销时间毫秒',
    _seq                        Int64 COMMENT '全局序列号',
    _eno                        Int64 COMMENT '事件号',
    _dt                         DateTime64(3) Default now(3) COMMENT '数据时间'
)
    ENGINE = ReplicatedMergeTree(_seq)
    PARTITION BY (trd_dt)
    ORDER BY (ord_nbr, exchg_cde, fcomp_unfy_cde, b_s_tag)
    SETTINGS non_replicated_deduplication_window = 1000
    COMMENT 'DWD_单腿委托明细_实时';

Create table if not exists cfmmc_trade.dwd_trd_cmb_ord_dtl_rt
(
    trd_dt                      Date COMMENT '交易日期',
    unfy_opnacct_cde            String COMMENT '统一开户账号',
    exchg_cde                   String COMMENT '交易所编码',
    memb_cde                    String COMMENT '会员编码',
    trd_cde                     String COMMENT '交易编码',
    fcomp_unfy_cde              String COMMENT '期货公司统一编码',
    futrs_opt_tag               String COMMENT '期货期权标志',
    cmb_contract_cde            String COMMENT '组合合约代码',
    leg1_contract_cde           String COMMENT '腿1合约代码',
    leg2_contract_cde           String COMMENT '腿2合约代码',
    leg1_comm_cde               String COMMENT '腿1品种代码',
    leg2_comm_cde               String COMMENT '腿2品种代码',
    settle_memb_memb_cde        String COMMENT '结算会员会员编码',
    highfrequ_ord_tag           String COMMENT '高频委托标志',
    ord_type                    String COMMENT '报单类型',
    ord_sts                     String COMMENT '报单状态',
    ord_src                     String COMMENT '报单来源',
    trd_vol_type                String COMMENT '成交量类型',
    ord_prc_cndt                String COMMENT '报单价格条件',
    vldprd_type                 String COMMENT '有效期类型',
    final_alt_seat_nbr          String COMMENT '最终修改席位号',
    seat_nbr                    String COMMENT '席位号',
    rmn_vol                     Decimal(32, 0) COMMENT '剩余量',
    trd_vol                     Decimal(32, 0) COMMENT '成交量',
    min_trd_vol                 Decimal(32, 0) COMMENT '最小成交量',
    ord_vol                     Decimal(32, 0) COMMENT '委托量',
    trig_prc                    Decimal(24, 6) COMMENT '触发价格',
    ord_prc                     Decimal(24, 6) COMMENT '委托价格',
    rcnt_renew_tm_microsec      Decimal(32, 0) COMMENT '最近更新时间微秒',
    rcnt_renew_tm_millisec      Decimal(32, 0) COMMENT '最近更新时间毫秒',
    final_alt_tm                String COMMENT '最终修改时间',
    ord_tm_microsec             Decimal(32, 0) COMMENT '报单时间微秒',
    ord_tm_millisec             Decimal(32, 0) COMMENT '报单时间毫秒',
    ord_tm                      String COMMENT '报单时间',
    repel_tm                    String COMMENT '撤销时间',
    susp_tm                     String COMMENT '挂起时间',
    actv_tm                     String COMMENT '激活时间',
    ord_tm                      String COMMENT '报单时间',
    forcclos_resn               String COMMENT '强平原因',
    trig_cndt                   String COMMENT '触发条件',
    specu_hedg_tag              String COMMENT '投机套保标志',
    ocpos_type                  String COMMENT '开平仓类型',
    b_s_tag                     String COMMENT '买卖标志',
    c_p_tag                     String COMMENT '看涨看跌标志',
    exec_prc                    Decimal(24, 6) COMMENT '执行价格',
    local_ord_nbr               String COMMENT '本地报单编号',
    ocr_dt                      Date COMMENT '发生日期',
    repel_tm_microsec           Decimal(32, 0) COMMENT '撤销时间微秒',
    repel_tm_millisec           Decimal(32, 0) COMMENT '撤销时间毫秒',
    _seq                        Int64 COMMENT '全局序列号',
    _eno                        Int64 COMMENT '事件号',
    _dt                         DateTime64(3) Default now(3) COMMENT '数据时间'
)
    ENGINE = ReplicatedMergeTree(_seq)
    PARTITION BY (trd_dt)
    ORDER BY (ord_nbr, exchg_cde, fcomp_unfy_cde, b_s_tag)
    SETTINGS non_replicated_deduplication_window = 1000
    COMMENT 'DWD_组合委托明细_实时';
