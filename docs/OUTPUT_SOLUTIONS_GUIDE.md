# 期货订单簿重建和盈亏计算系统 - 输出方案指南

## 概述

本系统提供两套完整的数据输出方案：
- **版本1：调试输出版本** - 控制台输出，用于开发调试
- **版本2：ClickHouse生产输出版本** - 数据库持久化，用于生产环境

## 版本1：调试输出版本

### 功能特性
- 实时控制台输出，便于开发调试
- 可配置的输出频率控制
- 格式化显示关键业务指标
- 支持不同环境的配置切换

### 输出内容

#### 订单簿快照输出
```
=== 订单簿快照 [14:30:25.123] ===
合约: CU2401 | 序列: 12345 | 虚拟层: 是
最优买价: 73250.00(100) | 最优卖价: 73260.00(150)
买方档位: 5 | 卖方档位: 5 | 总订单: 25
买方总量: 500 | 卖方总量: 480
----------------------------------------
```

#### 盈亏计算输出
```
=== 盈亏计算 [14:30:27.456] ===
结算会员: 1001 | 合约: CU2401
净持仓: 100 | 开仓均价: 73200.00 | 最新价: 73250.00
已实现盈亏: +1250.00 | 浮动盈亏: +5000.00 | 总盈亏: +6250.00
开仓量: 200 | 平仓量: 100 | 开仓金额: 14640000.00
----------------------------------------
```

#### 盈亏汇总输出
```
=== 盈亏汇总 [14:30:30.789] ===
结算会员: 1001 | 序列: 567
总盈亏: +15750.00 | 已实现: +8500.00 | 浮动: +7250.00
总收益率: 1.25% | 已实现收益率: 0.68%
合约总数: 5 | 有持仓: 3 | 盈利: 2 | 亏损: 1
风险等级: 低风险 | 最大盈亏: +6250.00 | 最小盈亏: -1200.00
开仓金额: 1260000.00 | 平仓金额: 850000.00
========================================
```

### 使用方法

#### 1. 基本使用
```java
// 创建调试输出配置
OutputSolutionManager.OutputSolutionConfig config = 
    OutputSolutionManager.createDebugSolution();

// 应用到数据流
OutputSolutionManager.applyOutputSolution(
    snapshotStream, pnlCalculationStream, pnlSummaryStream, config);
```

#### 2. 自定义配置
```java
// 创建自定义调试配置
DebugOutputManager.DebugConfig debugConfig = new DebugOutputManager.DebugConfig();
debugConfig.setOrderBookOutputInterval(500L);  // 0.5秒输出一次
debugConfig.setPnlCalculationOutputInterval(1000L);  // 1秒输出一次
debugConfig.setEnableOrderBookOutput(true);

OutputSolutionManager.OutputSolutionConfig config = 
    new OutputSolutionManager.OutputSolutionConfig(OutputSolutionType.DEBUG_CONSOLE);
config.setDebugConfig(debugConfig);
```

## 版本2：ClickHouse生产输出版本

### 表结构设计

#### 1. 订单簿快照表 (futures_orderbook_snapshots)
```sql
CREATE TABLE futures_orderbook_snapshots (
    snapshot_time DateTime64(3),
    contract_code String,
    best_bid_price Nullable(Decimal64(6)),
    best_ask_price Nullable(Decimal64(6)),
    total_bid_volume Decimal64(0),
    total_ask_volume Decimal64(0),
    has_virtual_layer UInt8,
    -- 更多字段...
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDate(snapshot_time))
ORDER BY (contract_code, snapshot_time);
```

#### 2. 盈亏计算表 (futures_pnl_calculations)
```sql
CREATE TABLE futures_pnl_calculations (
    calculation_time DateTime64(3),
    settlement_member_code String,
    contract_code String,
    current_position Decimal64(0),
    realized_pnl Decimal64(2),
    unrealized_pnl Decimal64(2),
    total_pnl Decimal64(2),
    -- 更多字段...
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDate(calculation_time))
ORDER BY (settlement_member_code, contract_code, calculation_time);
```

#### 3. 盈亏汇总表 (futures_pnl_summaries)
```sql
CREATE TABLE futures_pnl_summaries (
    summary_time DateTime64(3),
    settlement_member_code String,
    total_pnl Decimal64(2),
    total_return_rate Nullable(Decimal64(6)),
    total_contracts UInt32,
    active_contracts UInt32,
    -- 更多字段...
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDate(summary_time))
ORDER BY (settlement_member_code, summary_time);
```

### 部署步骤

#### 1. 安装ClickHouse
```bash
# Docker方式安装
docker run -d --name clickhouse-server \
  -p 8123:8123 -p 9000:9000 \
  --ulimit nofile=262144:262144 \
  clickhouse/clickhouse-server

# 验证安装
curl 'http://localhost:8123/' --data 'SELECT 1'
```

#### 2. 创建数据库和表
```bash
# 执行建表脚本
clickhouse-client --multiquery < src/main/resources/clickhouse/futures_system_tables.sql
```

#### 3. 配置连接参数
```properties
# application-output.properties
clickhouse.jdbc.url=****************************************
clickhouse.username=default
clickhouse.password=
clickhouse.batch.size=2000
clickhouse.flush.interval.ms=3000
```

### 使用方法

#### 1. 生产环境配置
```java
// 创建生产输出配置
OutputSolutionManager.OutputSolutionConfig config = 
    OutputSolutionManager.createProductionSolution(
        "********************************************************",
        "futures_user",
        "futures_password"
    );

// 验证连接
if (OutputSolutionManager.validateOutputSolution(config)) {
    OutputSolutionManager.applyOutputSolution(
        snapshotStream, pnlCalculationStream, pnlSummaryStream, config);
}
```

#### 2. 环境变量配置
```bash
export CLICKHOUSE_JDBC_URL="***********************************************"
export CLICKHOUSE_USERNAME="futures_user"
export CLICKHOUSE_PASSWORD="futures_password"
export CLICKHOUSE_BATCH_SIZE="2000"
export CLICKHOUSE_FLUSH_INTERVAL_MS="3000"
export OUTPUT_SOLUTION_TYPE="PRODUCTION_CLICKHOUSE"
```

```java
// 从环境变量创建配置
OutputSolutionManager.OutputSolutionConfig config = 
    OutputSolutionManager.createConfigFromEnvironment();
```

### 性能优化

#### 1. 批量写入优化
- **批次大小**：生产环境建议2000-5000条记录
- **刷新间隔**：1-3秒，平衡实时性和性能
- **连接池**：10-15个连接，支持高并发写入

#### 2. 分区策略
- **时间分区**：按月分区，便于数据管理和查询
- **TTL设置**：订单簿快照30-90天，盈亏数据365天
- **索引优化**：合约代码、会员编码、时间字段建立索引

#### 3. 查询优化示例
```sql
-- 查询某合约最新订单簿快照
SELECT * FROM futures_orderbook_snapshots 
WHERE contract_code = 'CU2401' 
ORDER BY snapshot_time DESC 
LIMIT 1;

-- 查询某会员当日盈亏汇总
SELECT * FROM futures_pnl_summaries 
WHERE settlement_member_code = '1001' 
  AND toDate(summary_time) = today()
ORDER BY summary_time DESC;

-- 统计各合约交易活跃度
SELECT 
    contract_code,
    count() as snapshot_count,
    avg(total_bid_volume + total_ask_volume) as avg_volume
FROM futures_orderbook_snapshots 
WHERE toDate(snapshot_time) = today()
GROUP BY contract_code
ORDER BY avg_volume DESC;
```

## 混合输出方案

### 适用场景
- 生产环境需要数据持久化
- 同时需要实时监控能力
- 开发测试阶段的渐进式部署

### 配置示例
```java
// 创建混合输出方案
OutputSolutionManager.OutputSolutionConfig config = 
    OutputSolutionManager.createHybridSolution(
        "****************************************",
        "default",
        ""
    );

// 自定义混合配置
config.getDebugConfig().setOrderBookOutputInterval(10000L);  // 降低控制台输出频率
config.getClickHouseConfig().setBatchSize(1000);  // 适中的批次大小
```

## 监控和告警

### 关键指标监控
- **写入成功率**：>95%
- **写入延迟**：<1秒
- **批次失败次数**：<5次/小时
- **连接失败次数**：<3次/小时

### 日志监控
```bash
# 监控ClickHouse写入日志
tail -f flink-taskmanager.log | grep "ClickHouse"

# 监控调试输出
tail -f flink-taskmanager.log | grep "=== 订单簿快照"
```

## 故障排查

### 常见问题

#### 1. ClickHouse连接失败
```
解决方案：
1. 检查网络连接和防火墙设置
2. 验证用户名密码和权限
3. 检查ClickHouse服务状态
4. 调整连接超时参数
```

#### 2. 批量写入失败
```
解决方案：
1. 检查表结构是否匹配
2. 验证数据类型和约束
3. 调整批次大小和刷新间隔
4. 检查磁盘空间和内存使用
```

#### 3. 调试输出频率过高
```
解决方案：
1. 调整输出间隔参数
2. 禁用不必要的输出类型
3. 使用生产环境配置
4. 重定向输出到文件
```

## 最佳实践

1. **开发阶段**：使用调试输出方案，快速验证业务逻辑
2. **测试阶段**：使用混合方案，验证数据持久化
3. **生产阶段**：使用ClickHouse方案，确保数据可靠性
4. **监控告警**：配置关键指标监控和异常告警
5. **性能调优**：根据数据量和实时性要求调整参数
6. **数据备份**：定期备份ClickHouse数据和配置
