# 期货订单簿重建和盈亏计算系统 - 模拟数据生成器使用指南

## 概述

本模拟数据生成器为期货订单簿重建和盈亏计算系统提供完整的测试数据，支持本地调试和功能验证。生成的数据完全符合业务逻辑和时间序列要求，能够触发系统的所有核心处理流程。

## 核心组件

### 1. MockDataGenerator - 核心数据生成器
- **功能**：生成符合业务逻辑的模拟数据
- **特性**：时间序列一致性、业务逻辑正确性、数据关联完整性

### 2. MockDataSet - 数据集合管理
- **功能**：统一管理所有类型的模拟数据
- **特性**：JSON序列化、统计信息、数据验证

### 3. MockDataSourceFactory - Flink数据源适配
- **功能**：将模拟数据转换为Flink DataStream
- **特性**：实时播放、速度控制、循环播放

### 4. MockDataValidator - 数据验证器
- **功能**：验证生成数据的正确性和一致性
- **特性**：业务逻辑验证、时间序列验证、数据关联验证

## 快速开始

### 1. 基本使用示例

```java
// 生成完整的模拟数据集
MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();

// 打印统计信息
dataSet.printStatistics();

// 验证数据完整性
MockDataValidator.ValidationResult result = MockDataValidator.validateCompleteDataSet(dataSet);
MockDataValidator.printValidationResult(result);

// 保存到JSON文件
dataSet.saveToJsonFile("target/test_data.json");
```

### 2. Flink集成示例

```java
// 创建Flink环境
StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

// 生成模拟数据
MockDataSet dataSet = MockDataGenerator.generateCompleteDataSet();

// 创建数据源
MockDataSourceFactory.MockDataSources dataSources = 
    MockDataSourceFactory.createFastTestSources(env, dataSet);

// 获取数据流
DataStream<SingleLegOrder> orderStream = dataSources.getSingleLegOrderStream();
DataStream<TradeDetail> tradeStream = dataSources.getTradeDetailStream();
DataStream<CombinationOrder> comboStream = dataSources.getCombinationOrderStream();
```

### 3. 完整系统测试

```java
// 运行完整测试套件
MockDataTestRunner.runAllTests();

// 或运行快速验证
MockDataTestRunner.runQuickValidationTest();
```

## 数据生成详情

### 单腿委托数据（SingleLegOrder）

**生成规模**：80条订单
**时间分布**：3秒内均匀分布，每37.5毫秒一个订单
**合约覆盖**：CU2401, AL2401, ZN2401, PB2401, SN2401

**数据特征**：
```
- 报单编号：O000001 - O000080
- 价格范围：基准价格 ± 10个价格档位
- 数量范围：10-100手（10的倍数）
- 买卖方向：随机分布
- 开平仓：70%开仓，30%平仓
- 订单类型：90%限价单，10%市价单
```

### 成交明细数据（TradeDetail）

**生成逻辑**：80%的委托订单会产生成交
**成交模式**：支持部分成交和全部成交
**时间关系**：成交时间晚于对应委托时间0.5-2秒

**数据特征**：
```
- 成交编号：T000001 - T000XXX
- 成交价格：委托价格附近小幅波动（±0.1%）
- 成交数量：1-3次分批成交
- 时间一致性：严格保证成交时间 > 委托时间
```

### 撤销数据

**撤销比例**：20-30%的委托订单
**撤销时间**：委托后1-2秒内
**撤销类型**：70%全部撤销，30%部分撤销

### 组合委托数据（CombinationOrder）

**生成规模**：15条组合委托
**组合策略**：主要为价差策略
**腿合约关系**：确保腿合约在单腿委托中存在

**数据特征**：
```
- 报单编号：C000001 - C000015
- 组合价格：腿1价格 - 腿2价格 ± 随机波动
- 数量范围：10-50手
- 成交率：60%的组合委托会成交
```

## 配置选项

### 数据源配置（MockSourceConfig）

```java
MockDataSourceFactory.MockSourceConfig config = new MockDataSourceFactory.MockSourceConfig();

// 实时播放模式
config.setRealTimePlayback(true);          // 按真实时间间隔播放
config.setPlaybackSpeedMultiplier(10);     // 播放速度倍数
config.setLoopPlayback(true);               // 循环播放
config.setDelayBetweenRecords(100);         // 记录间延迟(毫秒)
```

### 预设配置

```java
// 快速测试配置（无延迟）
MockDataSources fastSources = MockDataSourceFactory.createFastTestSources(env, dataSet);

// 实时模拟配置
MockDataSources realTimeSources = MockDataSourceFactory.createRealTimeSimulationSources(env, dataSet);
```

## 数据验证

### 验证项目

1. **基础数据验证**
   - 必填字段检查
   - 数据类型验证
   - 空值检测

2. **业务逻辑验证**
   - 成交与委托对应关系
   - 组合委托腿合约存在性
   - 数量一致性检查

3. **时间序列验证**
   - 时间顺序正确性
   - 成交时间晚于委托时间
   - 时间跨度合理性

4. **价格合理性验证**
   - 价格为正数
   - 价格波动范围合理
   - 同合约价格一致性

### 验证结果

```java
ValidationResult result = MockDataValidator.validateCompleteDataSet(dataSet);

// 检查验证结果
if (result.isValid()) {
    System.out.println("数据验证通过");
} else {
    result.getErrors().forEach(System.out::println);
}

// 查看统计信息
result.getStatistics().forEach((key, value) -> 
    System.out.println(key + ": " + value));
```

## 使用场景

### 1. 本地开发调试

```java
// 生成小量数据用于快速调试
MockDataSet debugData = MockDataGenerator.generateDataForContract("CU2401", 20);

// 创建快速数据源
MockDataSources sources = MockDataSourceFactory.createFastTestSources(env, debugData);
```

### 2. 功能验证测试

```java
// 生成完整数据集
MockDataSet fullData = MockDataGenerator.generateCompleteDataSet();

// 运行完整系统测试
MockDataExample.runCompleteSystemTest();
```

### 3. 性能压力测试

```java
// 生成大量数据
MockDataSet largeData = new MockDataSet();
largeData.setSingleLegOrders(MockDataGenerator.generateSingleLegOrders(1000));
largeData.setTradeDetails(MockDataGenerator.generateTradeDetails(largeData.getSingleLegOrders()));

// 运行性能测试
MockDataTestRunner.runPerformanceTest();
```

### 4. 集成测试

```java
// 使用模拟数据运行完整流水线
OutputSolutionExample.runDebugOutputExample();  // 已集成模拟数据
```

## JSON数据格式

生成的JSON文件包含完整的数据结构和元数据：

```json
{
  "single_leg_orders": [...],
  "trade_details": [...],
  "cancel_orders": [...],
  "combination_orders": [...],
  "generation_metadata": {
    "generation_time": "2024-01-15T14:30:25.123",
    "total_orders": 80,
    "total_trades": 64,
    "total_cancels": 20,
    "total_combinations": 15,
    "contracts": ["CU2401", "AL2401", "ZN2401", "PB2401", "SN2401"],
    "members": ["1001", "1002", "1003", "1004", "1005"],
    "time_range": {
      "start_time": "09:00:00",
      "end_time": "09:00:03",
      "duration_seconds": 3.0
    }
  }
}
```

## 最佳实践

### 1. 开发阶段
- 使用小量数据（20-50条订单）
- 启用详细日志输出
- 使用快速播放模式

### 2. 测试阶段
- 使用完整数据集
- 启用数据验证
- 测试各种异常场景

### 3. 性能测试
- 生成大量数据（500-1000条订单）
- 使用实时播放模式
- 监控系统性能指标

### 4. 集成测试
- 使用真实时间序列
- 测试完整业务流程
- 验证输出结果正确性

## 故障排查

### 常见问题

1. **数据验证失败**
   ```
   解决方案：
   - 检查生成参数是否合理
   - 查看验证错误详情
   - 重新生成数据
   ```

2. **时间序列异常**
   ```
   解决方案：
   - 确保系统时间正确
   - 检查时间范围设置
   - 验证时间计算逻辑
   ```

3. **数据关联错误**
   ```
   解决方案：
   - 检查订单编号唯一性
   - 验证成交与委托对应关系
   - 确认组合委托腿合约存在
   ```

### 调试技巧

1. **启用详细日志**
   ```java
   // 设置日志级别为DEBUG
   System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "DEBUG");
   ```

2. **分步验证**
   ```java
   // 分别验证各类数据
   MockDataValidator.validateBasicData(dataSet, result);
   MockDataValidator.validateBusinessLogic(dataSet, result);
   ```

3. **数据导出分析**
   ```java
   // 导出数据进行人工分析
   dataSet.saveToJsonFile("debug_data.json");
   ```

## 扩展开发

### 添加新合约

```java
// 在MockDataGenerator中添加新合约配置
CONTRACT_CONFIGS.put("AG2401", new ContractConfig(
    "AG2401", "SHFE", "AG", new BigDecimal("5000"), new BigDecimal("100")));
```

### 自定义生成逻辑

```java
// 继承MockDataGenerator实现自定义逻辑
public class CustomMockDataGenerator extends MockDataGenerator {
    public static List<SingleLegOrder> generateCustomOrders(int count) {
        // 自定义生成逻辑
    }
}
```

### 添加新的验证规则

```java
// 在MockDataValidator中添加新的验证方法
private static void validateCustomRules(MockDataSet dataSet, ValidationResult result) {
    // 自定义验证逻辑
}
```

通过这个完整的模拟数据生成器，您可以在本地环境中充分测试和验证期货订单簿重建和盈亏计算系统的所有功能，为后续部署到远程服务器提供可靠的测试基础。
